import React from 'react';
import { Box, Typography } from '@mui/material';
import BookmarkIcon from '../Icons/FeedIcons/BookmarkIcon';

const BACKGROUND_IMAGE =
  'url(https://images.unsplash.com/photo-1551601651-2a8555f1a136?q=80&w=2047&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)';

const suggestedTags = [
  { name: '#CuttingEdge', background: BACKGROUND_IMAGE },
  { name: '#Clinics', background: BACKGROUND_IMAGE },
];

const followedTags = [
  { name: '#Surgery', background: BACKGROUND_IMAGE },
  { name: '#Technology', background: BACKGROUND_IMAGE },
  { name: '#Medicine', background: BACKGROUND_IMAGE },
  { name: '#Cardiology', background: BACKGROUND_IMAGE },
  { name: '#Surgery2', background: BACKGROUND_IMAGE },
  { name: '#Technology2', background: BACKGROUND_IMAGE },
];

interface Tag {
  name: string;
  background: string;
}

type TagCardProps = Tag;

const tagCardStyles = {
  width: '177px',
  height: '64px',
  borderRadius: '8px',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
} as const;

const TagCard: React.FC<TagCardProps> = React.memo(({ name, background }) => (
  <Box sx={{ ...tagCardStyles, backgroundImage: background }}>
    <Typography
      sx={{
        color: 'white',
        fontSize: '16px',
        fontWeight: 700,
      }}
    >
      {name}
    </Typography>
  </Box>
));

const RightSidebarHeader: React.FC = () => (
  <Box
    sx={{
      p: '20px 20px 0 20px',
      mb: '40px',
    }}
  >
    <Typography
      sx={{
        fontWeight: 500,
        fontSize: '20px',
        color: '#1E1E1E',
        height: '25px',
      }}
    >
      Tags
    </Typography>
  </Box>
);

const TagSection: React.FC<{ title: string; tags: Tag[] }> = ({
  title,
  tags,
}) => (
  <Box>
    <Typography
      sx={{
        fontWeight: 400,
        fontSize: '16px',
        color: '#1E1E1E',
        mb: '20px',
        height: '20px',
      }}
    >
      {title}
    </Typography>
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: '16px',
        mb: title === 'Suggested' ? '40px' : '20px',
      }}
    >
      {tags.map((tag, index) => (
        <TagCard key={tag.name + index} {...tag} />
      ))}
    </Box>
  </Box>
);

const RightSidebarFooter: React.FC = () => (
  <Box sx={{ boxShadow: '0 -4px 20px rgba(0,0,0,0.1)', p: '16px 20px' }}>
    <Typography
      sx={{
        fontSize: '12px',
        fontWeight: 700,
        color: '#A24295',
        textAlign: 'center',
        cursor: 'pointer',
        height: '30px',
      }}
    >
      Browse all tags →
    </Typography>
  </Box>
);

const RightSidebar: React.FC = () => {
  return (
    <Box
      sx={{
        display: { xs: 'none', lg: 'flex' },
        flexDirection: 'column',
        position: 'relative',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          position: 'absolute',
          right: '16px',
        }}
      >
        <BookmarkIcon fill="white" />
      </Box>

      <Box
        sx={{
          width: { xs: 217 },
          minWidth: { xs: 217 },
          mr: '40px',
          backgroundColor: 'white',
          borderRadius: '8px',
          mb: '40px',
          mt: '105px',
          maxHeight: '687px',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <RightSidebarHeader />

        <Box
          sx={{
            overflowY: 'auto',
            px: '20px',
            flex: 1,
            scrollbarWidth: 'none',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
          }}
        >
          <TagSection title="Suggested" tags={suggestedTags} />
          <TagSection title="Followed" tags={followedTags} />
        </Box>

        <RightSidebarFooter />
      </Box>
    </Box>
  );
};

export default RightSidebar;
