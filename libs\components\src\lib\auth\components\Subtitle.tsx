'use client';

import { Typography } from '@mui/material';

interface SubtitleProps {
  text: string;
  marginBottom?: string | number;
  highlighted?: string;
  sx?: any;
}

/**
 * Subtitle component for auth pages
 */
export const Subtitle = ({
  text,
  marginBottom = '40px',
  highlighted,
  sx,
}: SubtitleProps) => {
  return (
    <Typography
      sx={(theme) => ({
        ...(theme.typography as unknown as { subtitle3: React.CSSProperties })
          .subtitle3,
        textAlign: 'center',
        mb: marginBottom,
        ...sx,
      })}
    >
      {text}{' '}
      {highlighted && (
        <Typography
          component="span"
          variant="body1"
          display="inline"
          sx={{ fontWeight: 700, color: '#1E1E1E' }}
        >
          {highlighted}
        </Typography>
      )}
    </Typography>
  );
};

export default Subtitle;
