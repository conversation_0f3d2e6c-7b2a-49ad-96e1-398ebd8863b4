export type User = {
  id: string;
  name: string;
  email: string;
  user_type: string;
  verification_status: string;
  registration_date: string;
  avatar_url?: string;
  about: string;
  qualification?: string;
  employer?: string;
  designation?: string;
  profile_picture?: string;
  documents?: {
    medical_degree?: {
      file_name: string;
      file_type: string;
      url: string;
    };
    professional_qualification?: {
      file_name: string;
      file_type: string;
      url: string;
    };
    medical_qualification?: {
      file_name: string;
      file_type: string;
      url: string;
    };
  };
};

export type UserFilters = {
  searchText: string;
  userId: string;
};

export interface IFetchResult<ResultType> {
  data?: ResultType;
  isLoading: boolean;
  error: unknown;
  isValidating: boolean;
}
