const QuestionIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M28.578 14.1002V18.68C28.578 22.95 25.0978 26.43 20.8276 26.43H15.9375C16.7475 27.18 17.8276 27.62 19.0076 27.62H24.3378L27.1279 29.38C27.3879 29.54 27.6879 29.62 27.9779 29.62C28.2479 29.62 28.5179 29.55 28.7579 29.42C29.268 29.14 29.578 28.6 29.578 28.02V27.07C31.048 26.28 31.998 24.73 31.998 23.03V18.5401C31.998 16.4102 30.5482 14.6103 28.578 14.1002Z"
        fill={hoverFill}
      />
      <path
        d="M7.19033 23.82V25.27C7.19033 25.92 7.54036 26.52 8.11036 26.84C8.38037 26.99 8.68037 27.06 8.98041 27.06C9.31041 27.06 9.64044 26.97 9.93047 26.79L13.6906 24.43H20.8307C24.0008 24.43 26.581 21.85 26.581 18.6799V12.75C26.581 9.58008 24.0009 7 20.8307 7H9.7503C6.58019 7 4 9.57998 4 12.75V18.6799C4 20.8799 5.26005 22.8597 7.19014 23.8197L7.19033 23.82ZM15.3106 21.21C14.7606 21.21 14.3106 20.77 14.3106 20.2101C14.3106 19.66 14.7506 19.2101 15.3007 19.2101H15.3107C15.8607 19.2101 16.3107 19.66 16.3107 20.2101C16.3107 20.77 15.8607 21.21 15.3106 21.21ZM13.2606 10.9202C14.0406 10.3002 15.0606 10.0702 16.0507 10.3002C17.2408 10.5702 18.1908 11.5202 18.4708 12.7202C18.7808 14.0702 18.2408 15.4402 17.0907 16.2002C16.6007 16.5301 16.2907 17.1301 16.2907 17.7902C16.2907 18.3402 15.8407 18.7902 15.2907 18.7902C14.7406 18.7902 14.2906 18.3402 14.2906 17.7902C14.2906 16.4502 14.9207 15.2302 15.9907 14.53C16.4307 14.24 16.6407 13.7 16.5207 13.17C16.4207 12.72 16.0507 12.35 15.6007 12.25C15.2007 12.16 14.8106 12.24 14.5006 12.49C14.2006 12.73 14.0306 13.09 14.0306 13.48C14.0306 14.0301 13.5806 14.48 13.0306 14.48C12.4705 14.48 12.0305 14.0301 12.0305 13.48C12.0305 12.48 12.4805 11.5502 13.2606 10.9202Z"
        fill={hoverFill}
      />
    </svg>
  );
};

export default QuestionIcon;
