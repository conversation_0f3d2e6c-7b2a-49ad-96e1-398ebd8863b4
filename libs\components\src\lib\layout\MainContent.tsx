import Box from '@mui/material/Box';
import AddPostOptions from './AddPostOptions';

type Props = {
  children?: React.ReactNode;
};

export default function MainContent({ children }: Props) {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
      }}
    >
      {/* Add posts */}
      <Box
        sx={{
          borderRadius: '8px',
          boxShadow: '0px 4px 20px rgba(30, 30, 30, 0.1)',
          height: '102px',
          overflowX: 'scroll',
          backgroundColor: 'white',
          display: 'none',
          justifyContent: 'center',
          width: 'auto',
          scrollbarWidth: 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          '@media (min-width:1100px)': {
            display: 'flex',
          },
        }}
      >
        <AddPostOptions />
      </Box>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '20px',
          flexGrow: 1,
          overflowY: 'auto',
        }}
      >
        {children}
      </Box>
    </Box>
  );
}
