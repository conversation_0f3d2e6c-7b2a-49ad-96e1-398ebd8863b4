// ProfessionalTypeCards.tsx
import {
  Box,
  Card,
  CardContent,
  Stack,
  Typography,
  useMediaQuery,
} from '@mui/material';
import SignupOptionCard from './SignupOptionCard';
import BackgroundShapeIcon from '../../Icons/BackgroundShape';
import AlliedCardiacPersonOne from '../../Icons/AlliedCardiacPersonOne';
import AlliedCardiacPersonTwo from '../../Icons//AlliedCardiacPersonTwo';
import { useState } from 'react';
import { useTheme } from '@emotion/react';

const PROFESSIONAL_TYPES: any[] = [
  {
    title: 'Cardiac Surgeon',
    key: 'CARDIAC_SURGEON',
    description: '',
    icon: '',
    character:
      'https://assets.dev.minicardiac.com/assets/gray-coat-surgeon.svg',
    path: '/professional/cardiac-surgeon',
  },
  {
    title: 'Cardiologist',
    key: 'CARDIOLOGIST',
    description: '',
    icon: '',
    character:
      'https://assets.dev.minicardiac.com/assets/white-coat-doctor.svg',
    path: '/professional/cardiologist',
  },
  {
    title: 'Allied Cardiac',
    description:
      'This is the category for all other professions who make up the wider cardiac healthcare ecosystem: medical specialists, general practitioners (GPs), perfusionists, nurses, surgical practitioners, researchers, administrators, and more. This is the category for you!',
    icon: '',
    character: 'https://assets.dev.minicardiac.com/assets/3.svg',
    key: 'ALLIED_CARDIAC',
    characterFemale: 'https://assets.dev.minicardiac.com/assets/2.svg',
    path: '/professional/allied-cardiac',
  },
];

type AlliedCardiacSelectionProps = {
  selectedType: string | null;
  handleTypeSelect: (path: string) => void;
};

const AlliedCardiacSelection = ({
  selectedType,
  handleTypeSelect,
}: AlliedCardiacSelectionProps) => {
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const [isExpanded, setIsExpanded] = useState(false);
  const [isCardHovered, setIsCardHovered] = useState(false);

  return (
    <Stack direction="row" spacing={2} sx={{ justifyContent: 'center' }}>
      {PROFESSIONAL_TYPES.slice(2).map((type, index) => {
        const isSelected = selectedType === type.key;

        if (type.title === 'Allied Cardiac') {
          return (
            <Box
              key={index}
              sx={{
                position: 'relative',
                width: {
                  xs: '360px',
                  sm: '100%',
                },
              }}
            >
              <Card
                onClick={() => handleTypeSelect(type.path)}
                onMouseEnter={() => setIsCardHovered(true)}
                onMouseLeave={() => setIsCardHovered(false)}
                sx={{
                  width: {
                    xs: '360px',
                    sm: '100%',
                  },
                  height: 'auto',
                  minHeight: 180,
                  borderRadius: '12px',
                  border: isSelected
                    ? '3px solid #A24295'
                    : '0.5px solid #A24295',
                  borderColor: '#A24295',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  cursor: 'pointer',
                  position: 'relative',
                  overflow: 'hidden',
                  backgroundColor: '#FFFFFF',
                  boxShadow: '0 0 0 0',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                  },
                }}
              >
                <CardContent
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    height: '100%',
                    p: 2,
                    '&:last-child': { pb: 2 },
                  }}
                >
                  {/* Background Shape */}
                  <Box
                    sx={{
                      position: 'absolute',
                      width: '200px',
                      height: '200px',
                      right: '-70px',
                      top: { xs: '-0px', xxl: '-10px' },
                      opacity: 0.7,
                      zIndex: 0,
                      pointerEvents: 'none',
                    }}
                  >
                    <BackgroundShapeIcon
                      hovered={isCardHovered || isSelected}
                    />
                  </Box>

                  {/* Content */}
                  <Stack
                    spacing={1}
                    sx={{
                      position: 'relative',
                      zIndex: 1,
                      maxWidth: '400px',
                      ml: 0,
                      mt: 1,
                    }}
                  >
                    <Typography
                      variant="h4"
                      component="span"
                      sx={{
                        mb: 0.5,
                        fontFamily: "'Plus Jakarta Sans', sans-serif",
                        fontWeight: isSelected ? 700 : 400,
                        fontSize: '20px',
                        lineHeight: '36px',
                        color: isSelected ? '#A24295' : '#1E1E1E',
                      }}
                    >
                      {type.title}
                    </Typography>

                    <Typography
                      variant="body1"
                      sx={{
                        fontFamily: "'Plus Jakarta Sans', sans-serif",
                        fontWeight: 300,
                        fontSize: '12px',
                        lineHeight: '16px',
                        letterSpacing: '0.01em',
                        color: '#737678',
                        maxWidth: isSmallScreen ? '180px' : 'none',
                        width: isSmallScreen ? '180px' : 'auto',
                        display: '-webkit-box',
                        WebkitBoxOrient: 'vertical',
                        overflow:
                          isSmallScreen && !isExpanded ? 'hidden' : 'visible',
                        WebkitLineClamp:
                          isSmallScreen && !isExpanded ? 3 : 'unset',
                      }}
                    >
                      {type.description}
                    </Typography>

                    {/* Show Read More / Less ONLY on small screens */}
                    {isSmallScreen && (
                      <Typography
                        onClick={(e) => {
                          e.stopPropagation();
                          setIsExpanded((prev) => !prev);
                        }}
                        sx={{
                          fontSize: '12px',
                          color: '#A24295',
                          fontWeight: 500,
                          cursor: 'pointer',
                          userSelect: 'none',
                          width: 'fit-content',
                        }}
                      >
                        {isExpanded ? 'Read Less' : 'Read More'}
                      </Typography>
                    )}
                  </Stack>

                  <Box
                    sx={{
                      position: 'absolute',
                      right: '10px',
                      bottom: '0px',
                      maxHeight: '180px',
                      display: 'flex',
                      alignItems: 'flex-end',
                      opacity: 1,
                      zIndex: 2,
                    }}
                  >
                    <AlliedCardiacPersonOne
                      hovered={isCardHovered || isSelected}
                    />
                    <AlliedCardiacPersonTwo
                      hovered={isCardHovered || isSelected}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Box>
          );
        }

        return (
          <SignupOptionCard
            key={index}
            option={{
              title: type.title,
              description: type.description,
              icon: type.icon,
              character: type.character,
              path: type.path,
              shape: 'rectangle',
            }}
            onClick={() => handleTypeSelect(type.path)}
            variant="professional"
            selected={isSelected}
          />
        );
      })}
    </Stack>
  );
};

export default AlliedCardiacSelection;
