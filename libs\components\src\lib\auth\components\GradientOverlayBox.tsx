'use client';

import Image from 'next/image';
import { Box } from '@mui/material';
import { BACKGROUND_GRADIENT_IMAGE } from '../constants/auth.constants';
import { useState } from 'react';

export default function GradientOverlayBox({
  fadeOut = false,
}: {
  fadeOut?: boolean;
}) {
  const [imageError, setImageError] = useState(false);

  return (
    <Box
      sx={{
        position: 'absolute',
        width: '100%',
        height: '100%',
        background: 'linear-gradient(to bottom, #f5f5f5, #fff)',
        transition: 'opacity 0.5s ease',
        opacity: fadeOut ? 0 : 1,
        zIndex: 1,
        display: { xs: 'block', sm: 'none' },
      }}
    >
      {!imageError && (
        <Image
          src={BACKGROUND_GRADIENT_IMAGE}
          alt="Background"
          width={1000}
          height={392}
          style={{
            objectFit: 'fill',
            objectPosition: 'center',
            zIndex: 0,
            transform: 'rotate(180deg)',
          }}
          priority
          onError={() => setImageError(true)}
        />
      )}

      {/* Gradient Overlay */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background:
            'linear-gradient(to right, rgba(249, 34, 67, 0.75), rgba(162, 66, 149, 0.75))',
          zIndex: 1,
        }}
      />
    </Box>
  );
}
