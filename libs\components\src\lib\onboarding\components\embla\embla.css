.embla {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  --slide-height: auto;
  --slide-spacing: 1rem;
  --slide-size: auto;
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}

.embla__slide {
  flex: 0 0 var(--slide-size);
  justify-self: center;
  align-self: center;
  min-width: 0;
  padding-left: var(--slide-spacing);
}

.embla__slide__number {
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--slide-height);
  user-select: none;
}

.embla__button {
  -webkit-tap-highlight-color: #a24295;
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  width: 3.6rem;
  height: 3.6rem;
  z-index: 1;
  border-radius: 50%;
  color: #a24295;
  align-items: center;
  justify-content: center;
}

.embla__button:disabled {
  color: grey;
}

.embla__button__svg {
  width: 35%;
  height: 35%;
}

/* ----------- Responsive Behavior ----------- */

/* Below 1280px: reduce spacing and size */
@media screen and (max-width: 1280px) {
  .embla {
    --slide-spacing: 0.75rem;
    --slide-size: auto;
  }

  .embla__button {
    width: 3rem;
    height: 3rem;
  }

  .embla__button__svg {
    width: 40%;
    height: 40%;
  }
}

/* Below 1024px */
@media screen and (max-width: 1024px) {
  .embla {
    --slide-spacing: 0.5rem;
  }

  .embla__button {
    width: 2.75rem;
    height: 2.75rem;
  }
}

/* Below 768px */
@media screen and (max-width: 768px) {
  .embla {
    --slide-spacing: 0.4rem;
  }

  .embla__button {
    width: 2.6rem;
    height: 2.6rem;
  }
}

/* Below 600px: show only 1 card in center */
@media screen and (max-width: 800px) {
  .embla {
    --slide-spacing: 0.25rem;
    --slide-size: 100%; /* Show only one card */
  }

  .embla__button {
    width: 2.4rem;
    height: 2.4rem;
  }

  .embla__button__svg {
    width: 45%;
    height: 45%;
  }
}
