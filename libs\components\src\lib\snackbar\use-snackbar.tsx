import { useContext } from 'react';
import { AlertColor } from '@mui/material';
import { SnackbarContext } from './snackbar-provider';

export const useSnackbar = () => {
  const context = useContext(SnackbarContext);
  
  if (!context) {
    throw new Error('useSnackbar must be used within a SnackbarProvider');
  }
  
  return {
    showSuccess: (message: string) => context.showSnackbar(message, 'success'),
    showError: (message: string) => context.showSnackbar(message, 'error'),
    showInfo: (message: string) => context.showSnackbar(message, 'info'),
    showWarning: (message: string) => context.showSnackbar(message, 'warning'),
    showSnackbar: (message: string, severity?: AlertColor) => context.showSnackbar(message, severity),
  };
};
