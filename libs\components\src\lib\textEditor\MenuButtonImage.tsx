import { Editor } from '@tiptap/react';
import { Button } from '@mui/material';
import ImageIcon from '@mui/icons-material/Image';

export function MenuButtonImage({ editor }: { editor: Editor }) {
  const handleInsertImage = () => {
    const url = prompt('Enter image URL');
    if (url) {
      editor.chain().focus().setImage({ src: url }).run();
    }
  };

  return (
    <Button
      onClick={handleInsertImage}
      startIcon={<ImageIcon />}
      size="small"
    ></Button>
  );
}
