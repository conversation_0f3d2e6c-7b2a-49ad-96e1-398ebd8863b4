import { useCallback, useState } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { useTheme } from '@mui/material/styles';
import { ExtendedTheme } from '../types/auth.types';
import {
  Box,
  TextField,
  Typography,
  ToggleButtonGroup,
  ToggleButton,
  Grid,
} from '@mui/material';
import { ProfilePicture } from '../../common/ProfilePicture';
import { useProfilePicture } from '../../common/ProfilePicture';
import IntroductoryStatement from '../../onboarding/components/CardiacSpecialist/IntroductoryStatement';
import { ActionButtonsTemplate } from '../../onboarding/templates';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { INTRODUCTORY_STATEMENT_LIMITS } from '@minicardiac-client/constants';
import { removeAWSS3DomainFromURL } from '@minicardiac-client/utilities';



export interface StudentProfileFormData {
  institutionName: string; // Changed from 'institution' to 'institutionName'
  introductoryStatement: string;
  interestedIn: 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH';
  profileImageUrl?: string;
  profileImageUrlThumbnail?: string;
}

interface StudentProfileSetupFormProps {
  isBasicPlan?: boolean;
  onChange?: (formData: StudentProfileFormData) => void;
  onSave?: (formData: StudentProfileFormData) => void;
  onSkip?: () => void;
  isSubmitting?: boolean;
  disabled?: boolean;
  initialData?: Partial<StudentProfileFormData>;
}

const validationSchema = yup.object().shape({
  institutionName: yup.string().required('Institution is required'), // Changed from 'institution'
  introductoryStatement: yup
    .string()
    .required('Introductory statement is required')
    .max(INTRODUCTORY_STATEMENT_LIMITS.MAX, `Maximum ${INTRODUCTORY_STATEMENT_LIMITS.MAX} characters only`),
  interestedIn: yup
    .mixed<'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH'>()
    .oneOf(['CARDIAC_SURGEON', 'CARDIOLOGIST', 'BOTH'], 'Invalid interest')
    .required('Interest selection is required'),
});

export default function StudentProfileSetupForm({
  isBasicPlan = false,
  onChange,
  onSave,
  onSkip,
  isSubmitting = false,
  disabled = false,
  initialData,
}: StudentProfileSetupFormProps) {
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm<StudentProfileFormData>({
    // Explicitly type the form with StudentProfileFormData
    resolver: yupResolver(validationSchema),
    mode: 'onChange',
    defaultValues: {
      institutionName: initialData?.institutionName || '', // Changed from 'institution'
      introductoryStatement: initialData?.introductoryStatement || '',
      interestedIn: initialData?.interestedIn || 'CARDIAC_SURGEON',
      profileImageUrl: initialData?.profileImageUrl || '',
      profileImageUrlThumbnail: initialData?.profileImageUrlThumbnail || '',
    },
  });

  // Watch form values
  const formValues = watch();

  // Initialize state with form values or initial data
  const [, setInterestedIn] = useState<string>(
    initialData?.interestedIn || formValues.interestedIn || 'CARDIAC_SURGEON'
  );

  const onSubmit = (
    data: Omit<
      StudentProfileFormData,
      'profileImageUrl' | 'profileImageUrlThumbnail'
    > & { profileImageUrl?: string; profileImageUrlThumbnail?: string }
  ) => {
    const normalisedData: StudentProfileFormData = {
      ...data,
      profileImageUrl: data.profileImageUrl || '',
      profileImageUrlThumbnail: data.profileImageUrlThumbnail || '',
    };

    if (onSave) {
      onSave(normalisedData);
    }
  };

  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    }
  };

  const handleFieldChange = useCallback(
    (name: keyof StudentProfileFormData, value: any) => {
      // Only setValue for fields registered in react-hook-form
      const formFields: Array<keyof StudentProfileFormData> = [
        'institutionName', // Changed from 'institution'
        'introductoryStatement',
        'interestedIn',
        'profileImageUrl',
        'profileImageUrlThumbnail',
      ];
      if (formFields.includes(name)) {
        setValue(name as any, value, { shouldValidate: true });
      }

      if (onChange) {
        const updatedValues = {
          ...formValues,
          [name]: value,
        };

        onChange(updatedValues as StudentProfileFormData);
      }
    },
    [setValue, onChange, formValues]
  );

  const updateFormField = (field: any, value: any) => {
    handleFieldChange(field, value);
  };

  const theme = useTheme() as ExtendedTheme;

  const { previewUrl, isUploading, error, handleImageChange, handleRemove } =
    useProfilePicture({
      initialImageUrl: initialData?.profileImageUrl || null,
      onImageUpload: (imageUrl, thumbnailUrl) => {
        updateFormField('profileImageUrl', imageUrl);
        const cleanedThumbnailUrl = removeAWSS3DomainFromURL(thumbnailUrl);
        updateFormField('profileImageUrlThumbnail', cleanedThumbnailUrl);
      },
    });

  return (
    <>
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          py: { xs: 3, sm: 5, md: 7 },
          px: { xs: 2, sm: 3, md: 5 },
          borderRadius: 1,
          boxShadow: (theme) =>
            `0px 12px 24px 0px ${(theme.palette as any).neutral[500]}25`,
          mb: 4,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: { xs: 'center', md: 'start' },
            justifyContent: 'space-between',
            width: '100%',
            gap: { xs: 3, md: 5 },
          }}
        >
          {/* Profile Picture Upload Section */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: { xs: 'center', md: 'flex-start' },
            }}
          >
            <ProfilePicture
              theme={theme}
              previewUrl={previewUrl}
              displayName={initialData?.institutionName || 'Student'}
              onImageChange={handleImageChange}
              onRemove={handleRemove}
              isUploading={isUploading}
              error={error}
              disabled={disabled}
            />
          </Box>

          {/* Form Fields Section */}
          <Box flexGrow={1}>
            <Grid
              container
              columns={{ xs: 4, sm: 8, md: 2 }}
              rowSpacing={{ xs: 3, md: 7 }}
              columnSpacing={{ xs: 3, md: 5 }}
              position={'relative'}
            >
              {/* Institution Field */}
              <Grid size={{ xs: 12, md: 1 }} sx={{ width: '100%', mt: 3 }}>
                <Controller
                  name="institutionName" // Changed from 'institution'
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      sx={{
                        width: { xs: '100%', sm: '80%' },
                      }}
                      label="I'm a student at"
                      variant="outlined"
                      placeholder="Name of your institution"
                      fullWidth
                      size="small"
                      error={!!errors.institutionName} // Changed from errors.institution
                      helperText={errors.institutionName?.message} // Changed from errors.institution
                      disabled={disabled}
                      slotProps={{
                        inputLabel: {
                          shrink: true,
                        },
                      }}
                    />
                  )}
                />
              </Grid>

              {/* Interest Selection Field */}
              <Grid size={{ xs: 12, md: 1 }} sx={{ width: '100%' }}>
                <Controller
                  name="interestedIn"
                  control={control}
                  render={({ field }) => (
                    <>
                      <Typography sx={{ mt: 0 }}>
                        I'm interested in working with:
                      </Typography>
                      <ToggleButtonGroup
                        color="secondary"
                        exclusive
                        value={field.value}
                        onChange={(_, value) => {
                          if (value) {
                            field.onChange(value);
                            setInterestedIn(value);
                          }
                        }}
                        disabled={disabled}
                        sx={{ mt: 1, width: '100%' }}
                      >
                        <ToggleButton
                          value="CARDIAC_SURGEON"
                          size="small"
                          sx={(theme) => ({
                            borderRadius: '8px',
                            backgroundColor:
                              field.value === 'CARDIAC_SURGEON'
                                ? '#E3C6DFBF'
                                : 'transparent',
                            color:
                              field.value === 'CARDIAC_SURGEON'
                                ? theme.palette.secondary.main
                                : '#1E1E1E',
                            fontFamily: "'Plus Jakarta Sans', sans-serif",
                            fontWeight:
                              field.value === 'CARDIAC_SURGEON' ? 600 : 300,
                            fontSize: '14px',
                            textTransform: 'none',
                            '&:hover': {
                              backgroundColor:
                                field.value === 'CARDIAC_SURGEON'
                                  ? '#E3C6DFBF'
                                  : 'rgba(0, 0, 0, 0.04)',
                            },
                          })}
                        >
                          Cardiac Surgeons
                        </ToggleButton>
                        <ToggleButton
                          value="CARDIOLOGIST"
                          size="small"
                          sx={(theme) => ({
                            borderRadius: '8px',
                            backgroundColor:
                              field.value === 'CARDIOLOGIST'
                                ? '#E3C6DFBF'
                                : 'transparent',
                            color:
                              field.value === 'CARDIOLOGIST'
                                ? theme.palette.secondary.main
                                : '#1E1E1E',
                            fontFamily: "'Plus Jakarta Sans', sans-serif",
                            fontWeight:
                              field.value === 'CARDIOLOGIST' ? 600 : 300,
                            fontSize: '14px',
                            textTransform: 'none',
                            '&:hover': {
                              backgroundColor:
                                field.value === 'CARDIOLOGIST'
                                  ? '#E3C6DFBF'
                                  : 'rgba(0, 0, 0, 0.04)',
                            },
                          })}
                        >
                          Cardiologists
                        </ToggleButton>
                        {!isBasicPlan && (
                          <ToggleButton
                            value="BOTH"
                            size="small"
                            sx={(theme) => ({
                              borderRadius: '8px',
                              backgroundColor:
                                field.value === 'BOTH'
                                  ? '#E3C6DFBF'
                                  : 'transparent',
                              color:
                                field.value === 'BOTH'
                                  ? theme.palette.secondary.main
                                  : '#1E1E1E',
                              fontFamily: "'Plus Jakarta Sans', sans-serif",
                              fontWeight: field.value === 'BOTH' ? 600 : 300,
                              fontSize: '14px',
                              textTransform: 'none',
                              '&:hover': {
                                backgroundColor:
                                  field.value === 'BOTH'
                                    ? '#E3C6DFBF'
                                    : 'rgba(0, 0, 0, 0.04)',
                              },
                            })}
                          >
                            Both
                          </ToggleButton>
                        )}
                      </ToggleButtonGroup>
                      {errors.interestedIn && (
                        <Typography
                          color="error"
                          variant="caption"
                          sx={{ mt: 1 }}
                        >
                          {errors.interestedIn.message}
                        </Typography>
                      )}
                    </>
                  )}
                />
              </Grid>

              {/* Introductory Statement */}
              <Grid size={{ xs: 12 }} sx={{ width: '100%' }}>
                <IntroductoryStatement
                  initialValue={formValues.introductoryStatement}
                  onChange={(text) =>
                    updateFormField('introductoryStatement', text)
                  }
                />
                {errors.introductoryStatement && (
                  <Typography color="error" variant="caption" sx={{ mt: 1 }}>
                    {errors.introductoryStatement.message}
                  </Typography>
                )}
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Box>

      {/* Action Buttons using shared template */}
      <ActionButtonsTemplate
        onSave={handleSubmit(onSubmit)}
        onSkip={handleSkip}
        isSubmitting={isSubmitting}
        isValid={isValid && !disabled}
        saveButtonText="Save and Continue"
        skipButtonText="Do this later"
        variant="student"
      />
    </>
  );
}
