'use client';

import React from 'react';
import { Box, Container, useMediaQuery } from '@mui/material';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  BackButton,
  CustomizedSteppers,
  PatientProfileWelcome,
  Subtitle,
} from '@minicardiac-client/components';
import {
  ProfileSetupForm,
  ProfileFormData,
} from '@minicardiac-client/components';
import { axiosInstance, useAuth, refreshSession } from '@minicardiac-client/apis';
import { useSnackbar } from '@minicardiac-client/components';
import { useTheme } from '@emotion/react';

export default function ProfessionalPaidProfileSetupPage() {
  const router = useRouter();
  const [activeStep] = React.useState(0);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const { showSuccess, showError } = useSnackbar();
  const [formData, setFormData] = React.useState<ProfileFormData>({
    introductoryStatement: '',
    profileImageUrl: '',
    profileImageUrlThumbnail: '',
    title: '',
    qualifications: '',
    jobTitle: '',
    employerId: '',
    mainProfession: '',
    category: '',
  });
  const [error, setError] = React.useState<string | null>(null);
  const { authState } = useAuth();
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const searchParams = useSearchParams();
  const professionalType = searchParams?.get('type');

  // Session is already managed by SessionVerifier and middleware
  // No need for manual session refresh on component mount

  const handleFormChange = React.useCallback((data: ProfileFormData) => {
    setFormData(data);
  }, []);

  const handleContinue = async () => {
    // Set submitting state
    setIsSubmitting(true);
    setError(null);

    try {
      // Refresh session before making API call to ensure valid token
      const sessionRefreshed = await refreshSession();

      if (!sessionRefreshed) {
        console.warn('Failed to refresh session, but will try to continue');
      }

      // Generate a unique identifier for the profile image if none is provided
      const uniqueImageId = Date.now().toString();
      const defaultImageUrl = `default-profile-${uniqueImageId}.jpg`;

      // Post the profile data to the API
      if (professionalType === 'ALLIED_CARDIAC') {
        await axiosInstance.post('onboarding/profile-setup/allied-cardiac', {
          introductoryStatement: formData.introductoryStatement || '',
          profileImageUrl: formData.profileImageUrl || defaultImageUrl,
          profileImageUrlThumbnail:
            formData.profileImageUrlThumbnail || defaultImageUrl,
          title: formData.title || '',
          qualifications: formData.qualifications || '',
          designation: formData.jobTitle || '',
          employerId: formData.employerId || undefined,
          primarySpeciality: formData.mainProfession || '',
          segmentCategoryId: formData.category || '',
        });
      } else {
        await axiosInstance.post('onboarding/profile-setup/specialist', {
          introductoryStatement: formData.introductoryStatement || '',
          profileImageUrl: formData.profileImageUrl || defaultImageUrl,
          profileImageUrlThumbnail:
            formData.profileImageUrlThumbnail || defaultImageUrl,
          title: formData.title || '',
          qualifications: formData.qualifications || '',
          jobTitle: formData.jobTitle || '',
          employerId: formData.employerId || undefined,
        });
      }

      // Show success message and navigate
      showSuccess('Profile saved successfully!');
      setTimeout(() => {
        router.push('/professional/paid/document-upload');
      }, 1000);
    } catch (err: any) {
      console.error('Error saving profile data:', err);

      // Check for the specific unique constraint error
      if (
        err.response?.data?.message?.includes(
          'unique constraint "users_profile_image_url_unique"'
        )
      ) {
        const errorMsg =
          'This profile image is already in use. Please choose a different image.';
        setError(errorMsg);
        showError(errorMsg);
      } else {
        setError(err.message || 'Failed to save profile data');
        showError(err.message || 'Failed to save profile data');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDoThisLater = () => {
    // Navigate to another page or dashboard
    router.push('/feed?fromSignup=true');
  };

  // Add custom CSS to fix the layout
  React.useEffect(() => {
    // Create a style element
    const style = document.createElement('style');

    // Add CSS to fix the Introductory Statement position
    style.innerHTML = `
      /* Move Introductory Statement below Title */
      .MuiGrid-container > .MuiGrid-root:last-child {
        order: 5 !important;
        margin-top: 24px !important;
        width: 100% !important;
      }

      /* Ensure the Introductory Statement has proper width */
      .MuiGrid-container > .MuiGrid-root:last-child > div {
        width: 100% !important;
      }
    `;

    // Append the style to the document head
    document.head.appendChild(style);

    // Clean up on unmount
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <Container maxWidth="lg">
      <Box mt={'20px'}>
        {/* Welcome Header */}

        {!isSmallScreen && (
          <PatientProfileWelcome
            patientName={authState.user?.displayName || ''}
            subtitle={''}
          />
        )}

        <BackButton />

        <Subtitle
          text={"Let's set up your Professional Account!"}
          sx={{ fontSize: { xs: '12px', sm: '16px' } }}
          marginBottom={'34px'}
        />

        <CustomizedSteppers
          activeStep={activeStep}
          steps={['Profile Setup', 'Document Upload', 'Adding Network']}
        />

        {/* Use the ProfileSetupForm component with isBasicPlan=false for paid users */}
        <Box
          sx={{
            mt: { xs: '25px', sm: '30px', md: '40px' },
            py: { xs: 0, sm: '50px' },
            px: { xs: '0px', sm: '30px', md: '74px' },
          }}
        >
          <ProfileSetupForm
            isBasicPlan={false}
            onChange={handleFormChange}
            onSave={handleContinue}
            onSkip={handleDoThisLater}
            isSubmitting={isSubmitting}
            userData={authState?.user}
            professionalType={professionalType || ''}
          />
          {error && (
            <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
              {error}
            </Box>
          )}
        </Box>
      </Box>
    </Container>
  );
}
