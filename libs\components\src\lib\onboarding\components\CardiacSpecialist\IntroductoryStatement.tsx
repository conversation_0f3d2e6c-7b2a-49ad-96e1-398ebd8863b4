import React, { useState, useEffect } from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import { INTRODUCTORY_STATEMENT_LIMITS } from '@minicardiac-client/constants';
// import { ExtendedTheme } from '../../../../auth/types/auth.types';

interface IntroductoryStatementProps {
  onChange?: (text: string) => void;
  initialValue?: string;
}

const IntroductoryStatement = ({
  onChange,
  initialValue = '',
}: IntroductoryStatementProps) => {
  const theme = useTheme();

  const [introText, setIntroText] = useState(initialValue);

  // Set initial value if provided
  useEffect(() => {
    if (initialValue) {
      setIntroText(initialValue);
    }
  }, [initialValue]);

  const handleIntroTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setIntroText(newValue);

    // Notify parent component if onChange callback is provided
    if (onChange) {
      onChange(newValue);
    }
  };

  return (
    <Box
      sx={{
        // width: '819px',
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
      }}
    >
      <Box
        sx={{
          //   width: '819px',
          height: '210px',
          position: 'relative',
          border: '1px solid #A3A3A3',
          borderRadius: '8px',
          backgroundColor: '#FFFFFF',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* Label at the top of the text field */}
        <Typography
          variant="body1"
          sx={{
            fontWeight: 500,
            fontSize: theme.typography.pxToRem(16),
            color: (theme.palette as any).neutral?.[900] || '#1E1E1E',
            position: 'absolute',
            top: '-10px',
            left: '14px',
            paddingX: '5px',
            backgroundColor: '#FFFFFF',
            zIndex: 1,
          }}
        >
          Introductory Statement
        </Typography>

        {!introText && (
          <Box
            sx={{
              position: 'absolute',
              top: '16px',
              left: '14px',
              right: '14px',
              bottom: '16px',
              pointerEvents: 'none',
              display: 'flex',
              flexDirection: 'column',
              color: (theme.palette as any).neutral?.[600] || '#737678',
              fontSize: theme.typography.pxToRem(14),
            }}
          >
            Write a couple of lines to introduce yourself to others in the
            community.
            <br />
            <br />
            <br />
            Don’t worry though - there will be plenty of space for a much longer
            bio on your About Me page!
          </Box>
        )}

        <textarea
          value={introText}
          onChange={handleIntroTextChange}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            outline: 'none',
            resize: 'none',
            padding: '16px 14px',
            backgroundColor: 'transparent',
            fontFamily: 'Plus Jakarta Sans, sans-serif',
            fontSize: theme.typography.pxToRem(14),
            color: (theme.palette as any).neutral?.[900] || '#1E1E1E',
            borderRadius: '8px',
          }}
          maxLength={INTRODUCTORY_STATEMENT_LIMITS.MAX}
        />
      </Box>

      <Box
        sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'flex-end',
          position: 'absolute',
          bottom: 10,
          right: 19,
          mt: 0.5,
        }}
      >
        <Typography
          variant="body2"
          sx={{
            color: (theme.palette as any).neutral?.[600] || '#737678',
            fontSize: theme.typography.pxToRem(14),
          }}
        >
          {introText.length}/{INTRODUCTORY_STATEMENT_LIMITS.MAX} characters
        </Typography>
      </Box>
    </Box>
  );
};

export default IntroductoryStatement;
