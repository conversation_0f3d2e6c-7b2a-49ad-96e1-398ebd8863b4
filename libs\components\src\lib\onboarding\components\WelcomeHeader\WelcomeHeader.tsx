import { Stack, Typography, useTheme } from '@mui/material';

interface WelcomeHeaderProps {
  name: string;
  subtitle?: string;
}

export const WelcomeHeader = ({
  name,
  subtitle = "Let's set up your Professional Account!",
}: WelcomeHeaderProps) => {
  const theme = useTheme();

  return (
    <Stack
      sx={{
        width: '100%',
        alignItems: 'center',
        position: 'relative',
        mt: '40px',
        mb: '24px',
      }}
      spacing={1}
    >
      {/* Welcome message */}
      <Typography
        variant="h3"
        sx={{
          fontWeight: 500,
          fontSize: '28px',
          lineHeight: '100%',
          letterSpacing: '0%',
          textAlign: 'center',
          color: (theme.palette as any).neutral?.[900] || '#1E1E1E',
        }}
      >
        {name ? ` Welcome to MiniCardiac, ${name}!` : 'Welcome to MiniCardiac!'}
      </Typography>

      {/* Subtitle text */}
      {subtitle && (
        <Typography
          variant="body1"
          sx={{
            fontWeight: 400,
            fontSize: theme.typography.pxToRem(16),
            lineHeight: '100%',
            letterSpacing: '0%',
            textAlign: 'center',
            color: (theme.palette as any).neutral?.[600] || '#737678',
          }}
        >
          {subtitle}
        </Typography>
      )}
    </Stack>
  );
};
