import { jwtDecode } from 'jwt-decode';
import { auth } from '../../../apis/src/lib/firebase/firebase-client.js';

export const getDecodedToken = async (forceRefresh = false): Promise<any | null> => {
  try {
    const user = auth?.currentUser;
    if (!user) return null;

    // For day-old tokens,
    const token = await user.getIdToken(forceRefresh);
    if (!token) return null;

    const decodedToken: any = jwtDecode(token);

    // If token seems stale (no currentStage or other issues), try refreshing once
    if (!forceRefresh && (!decodedToken.currentStage && decodedToken.currentStage !== 'completed')) {
      console.log('Token appears stale, attempting refresh...');
      return await getDecodedToken(true);
    }

    return decodedToken;
  } catch (error) {
    console.error('Failed to decode token:', error);


    if (!forceRefresh) {
      console.log('Retrying with token refresh...');
      return await getDecodedToken(true);
    }

    return null;
  }
};

/**
 * Waits for session to be properly established by polling session state
 * This replaces arbitrary timeouts with actual state checking
 */
export const waitForSessionEstablishment = async (maxAttempts = 10, intervalMs = 100): Promise<boolean> => {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      const decodedToken = await getDecodedToken();

      // Check if we have a valid token with expected properties
      if (decodedToken && (decodedToken.currentStage !== undefined || decodedToken.email_verified !== undefined)) {
        return true;
      }

      // Wait before next attempt
      await new Promise(resolve => setTimeout(resolve, intervalMs));
    } catch (error) {
      console.warn(`Session establishment check attempt ${attempt + 1} failed:`, error);
    }
  }

  console.warn('Session establishment verification timed out');
  return false;
};
