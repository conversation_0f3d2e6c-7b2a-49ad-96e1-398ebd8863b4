import { useState } from 'react';

const EventIcon = ({ fill = '#A3A3A3', hoverFill = '#A24295', size = 24 }) => {
  const [isHovered, setIsHovered] = useState(false);
  const currentFill = isHovered ? hoverFill : fill;

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <path
        d="M4 9.07941H20.0001V8.19051C19.9981 6.57838 18.6916 5.27188 17.0795 5.26985H16.4445V4.63493C16.4445 4.28473 16.1598 4 15.8096 4C15.4594 4 15.1747 4.28473 15.1747 4.63493V5.26985H8.82544V4.63493C8.82544 4.28473 8.54071 4 8.19051 4C7.84031 4 7.55559 4.28473 7.55559 4.63493V5.26985H6.92066C5.30853 5.27184 4.00203 6.57838 4 8.19051V9.07941Z"
        fill={currentFill}
      />
      <path
        d="M4 17.0793C4.00198 18.6915 5.30853 19.998 6.92066 20H17.0795C18.6916 19.998 19.9981 18.6915 20.0001 17.0793V9.84119H4V17.0793ZM9.6836 14.0624C9.70841 13.988 9.77388 13.9345 9.85126 13.9245L11.0199 13.7827C11.1509 13.7678 11.265 13.6854 11.3195 13.5644L11.8225 12.4979C11.8533 12.4265 11.9227 12.3809 12.0001 12.3809C12.0775 12.3809 12.1469 12.4265 12.1777 12.4979L12.6807 13.5644C12.7343 13.6835 12.8454 13.7668 12.9753 13.7827L14.149 13.9245C14.2263 13.9345 14.2918 13.988 14.3166 14.0624C14.3404 14.1368 14.3186 14.2182 14.2601 14.2708L13.397 15.0734C13.3017 15.1626 13.2591 15.2956 13.2849 15.4236L13.5081 16.5863H13.5091C13.525 16.6627 13.4942 16.74 13.4317 16.7857C13.3692 16.8303 13.2859 16.8343 13.2194 16.7946L12.1837 16.2261H12.1827C12.0696 16.1597 11.9307 16.1597 11.8176 16.2261L10.7809 16.7946C10.7144 16.8343 10.6311 16.8303 10.5686 16.7857C10.5061 16.74 10.4753 16.6627 10.4912 16.5863L10.7144 15.4236H10.7154C10.7412 15.2956 10.6986 15.1627 10.6033 15.0734L9.74023 14.2708C9.68169 14.2182 9.65979 14.1368 9.6836 14.0624Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default EventIcon;
