name: Develop Build & Deploy

on:
  workflow_dispatch:
  push:
    branches:
      - develop
  # pull_request:
  #   branches:
  #     - '*'
  #     - '!staging' # do not run when the PR is created to staging
  #   types: [opened, synchronize, reopened]

permissions:
  pull-requests: write
  contents: write
  actions: read
  deployments: write
  id-token: write
  issues: write

env:
  NODE_VERSION: 20
  BEFORE_SHA: ${{ github.event.before }}

jobs:
  deploy:
    name: Build & Deploy
    runs-on: ubuntu-latest
    environment: develop
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 2 # fetch all history for all branches and tags

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: latest

      - name: Cache node modules
        uses: actions/cache@v4
        id: cache-npm
        with:
          path: node_modules
          key: ${{ runner.os }}-npm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-npm-

      - name: Install Node.js dependencies
        if: ${{ steps.cache-npm.outputs.cache-hit != 'true' }}
        run: |
          pnpm install --frozen-lockfile

      - name: Install Doppler CLI
        uses: dopplerhq/cli-action@v3
      - name: Build Admin
        id: affected
        run: |
          doppler run -- npx nx build admin
        env:
          DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Deploy to Develop
        env:
          BUCKET_NAME: ${{ secrets.BUCKET_NAME }}
          DISTRIBUTION_ID: ${{ secrets.DISTRIBUTION_ID }}
        run: |
          npx nx deploy admin
          # the client is picked up by amplify automatically
