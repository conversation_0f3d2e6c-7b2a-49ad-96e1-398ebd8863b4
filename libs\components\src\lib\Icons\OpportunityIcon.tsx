import { useState } from 'react';

const OpportunityIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const currentFill = isHovered ? hoverFill : fill;

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <path
        d="M18.4731 14.2952C18.1689 14.113 17.8116 14.0598 17.4679 14.1455C17.2157 14.2084 16.9926 14.3413 16.8195 14.5281L14.7956 13.3128C15.0083 12.8965 15.1289 12.4257 15.1289 11.9264C15.1289 11.4789 15.0317 11.0548 14.8585 10.6715L16.8357 9.50233C17.0906 9.77924 17.4485 9.93032 17.8143 9.93032C18.0438 9.93032 18.2766 9.87066 18.488 9.7455C19.1177 9.37263 19.3272 8.55683 18.9549 7.92719C18.7747 7.62176 18.4861 7.40517 18.1424 7.31696C17.7987 7.22877 17.4414 7.28 17.1366 7.46028C16.5886 7.78451 16.3591 8.44402 16.5543 9.02506L14.5862 10.1891C14.083 9.46278 13.2744 8.9641 12.3471 8.88045V6.62569C12.9463 6.49794 13.3976 5.96489 13.3976 5.32744C13.3976 4.5953 12.8023 4 12.0702 4C11.338 4 10.7427 4.5953 10.7427 5.32744C10.7427 5.96424 11.1941 6.4973 11.7933 6.62569V8.88045C10.8731 8.96346 10.0696 9.45564 9.56572 10.1729L7.6053 8.99589C7.80438 8.41615 7.57936 7.7547 7.03335 7.42723C6.40563 7.05046 5.58854 7.25472 5.21171 7.88246C4.83488 8.5102 5.03921 9.32727 5.6663 9.7041C5.87575 9.8299 6.10986 9.8941 6.34721 9.8941C6.45486 9.8941 6.5638 9.88113 6.67144 9.85389C6.92434 9.79099 7.14678 9.65805 7.31991 9.47128L9.28864 10.6535C9.11031 11.0412 9.00979 11.4725 9.00979 11.9271C9.00979 12.4186 9.12716 12.8836 9.33402 13.296L7.30305 14.4977C7.13119 14.3102 6.90877 14.176 6.65717 14.1112C6.31347 14.023 5.95615 14.0742 5.65137 14.2545C5.02105 14.6274 4.81224 15.4432 5.18447 16.0728C5.36474 16.3782 5.65332 16.5948 5.99701 16.683C6.10725 16.7109 6.21878 16.7252 6.32903 16.7252C6.56378 16.7252 6.79529 16.6623 7.0028 16.5397C7.30823 16.3594 7.52482 16.0709 7.61302 15.7272C7.67787 15.4749 7.66685 15.2155 7.58514 14.9749L9.62657 13.7675C10.133 14.4387 10.9093 14.8945 11.7925 14.9743V17.3743C11.1933 17.5021 10.742 18.0351 10.742 18.6726C10.742 19.4047 11.3373 20 12.0694 20C12.8016 20 13.3969 19.4047 13.3969 18.6726C13.3969 18.0358 12.9455 17.5021 12.3463 17.3743V14.9743C13.2224 14.8952 13.9928 14.4451 14.5 13.783L16.5343 15.0041C16.4513 15.2447 16.439 15.5041 16.5019 15.7564C16.5875 16.1007 16.8027 16.3906 17.1062 16.5728C17.3196 16.7012 17.555 16.7621 17.7871 16.7621C18.2385 16.7621 18.6788 16.5326 18.9278 16.1182C19.3046 15.4905 19.1003 14.6734 18.4726 14.2966L18.4731 14.2952ZM17.418 7.93687C17.785 7.71963 18.2604 7.84155 18.4776 8.20858C18.6949 8.57561 18.5729 9.05095 18.2059 9.2682C17.8389 9.48544 17.3635 9.36352 17.1463 8.99584C16.929 8.6288 17.051 8.15347 17.418 7.93622V7.93687ZM6.53716 9.31554C6.33678 9.36547 6.12862 9.335 5.95159 9.228C5.58585 9.00881 5.46717 8.53218 5.68635 8.16644C5.83096 7.92521 6.08777 7.79098 6.35104 7.79098C6.48657 7.79098 6.6234 7.82664 6.74791 7.90122C7.11365 8.1204 7.23233 8.59703 7.01314 8.96277C6.90679 9.13981 6.73753 9.26496 6.53716 9.3149V9.31554ZM7.07669 15.5883C7.02546 15.7886 6.89901 15.9566 6.72133 16.0617C6.3543 16.2789 5.87896 16.157 5.66172 15.7899C5.55666 15.6123 5.52683 15.4041 5.57806 15.2037C5.62929 15.0034 5.75574 14.8354 5.93342 14.7303C6.05404 14.659 6.18893 14.622 6.32576 14.622C6.3906 14.622 6.45545 14.6305 6.51966 14.6467C6.72003 14.6979 6.888 14.8244 6.99305 15.002C7.09811 15.1797 7.12792 15.3879 7.07669 15.5883ZM11.2964 5.3273C11.2964 4.9006 11.6433 4.55366 12.07 4.55366C12.4967 4.55366 12.8436 4.90059 12.8436 5.3273C12.8436 5.754 12.4967 6.10094 12.07 6.10094C11.6433 6.10094 11.2964 5.75401 11.2964 5.3273ZM12.07 14.4313C11.5823 14.4313 11.1271 14.2906 10.7419 14.0487C10.8171 13.587 11.083 13.2316 11.3735 13.2316H12.7911C13.081 13.2316 13.343 13.5753 13.4201 14.0351C13.0304 14.2854 12.5667 14.432 12.07 14.432L12.07 14.4313ZM12.843 18.6711C12.843 19.0978 12.496 19.4447 12.0693 19.4447C11.6426 19.4447 11.2957 19.0978 11.2957 18.6711C11.2957 18.2444 11.6426 17.8974 12.0693 17.8974C12.496 17.8974 12.843 18.2444 12.843 18.6711ZM13.8935 13.6414C13.7061 13.063 13.2865 12.6778 12.7904 12.6778H11.3729C10.8774 12.6778 10.4507 13.0773 10.2653 13.6616C9.83207 13.2109 9.56425 12.5994 9.56425 11.9262C9.56425 10.545 10.6881 9.42114 12.0693 9.42114C13.4506 9.42114 14.5745 10.545 14.5745 11.9262C14.5745 12.5896 14.3151 13.1927 13.8929 13.6415L13.8935 13.6414ZM18.453 15.8313C18.2332 16.197 17.7572 16.3157 17.3914 16.0965C17.2144 15.9902 17.0892 15.8209 17.0393 15.6205C16.9894 15.4202 17.0205 15.212 17.1269 15.035C17.2332 14.8579 17.4025 14.7328 17.6028 14.6829C17.6657 14.6673 17.7286 14.6595 17.7915 14.6595C17.9303 14.6595 18.0665 14.6971 18.1884 14.7704C18.5541 14.9896 18.6728 15.4662 18.4536 15.832L18.453 15.8313Z"
        fill={currentFill}
      />
      <path
        d="M12.0517 9.98242C11.3682 9.98242 10.8125 10.5382 10.8125 11.2217C10.8125 11.9052 11.3683 12.4609 12.0517 12.4609C12.7352 12.4609 13.291 11.9051 13.291 11.2217C13.291 10.5382 12.7352 9.98242 12.0517 9.98242ZM12.0517 11.9072C11.6737 11.9072 11.3663 11.5998 11.3663 11.2217C11.3663 10.8437 11.6737 10.5363 12.0517 10.5363C12.4298 10.5363 12.7372 10.8437 12.7372 11.2217C12.7372 11.5998 12.4298 11.9072 12.0517 11.9072Z"
        fill={currentFill}
      />
      <circle cx="6.35447" cy="15.3916" r="0.913059" fill={currentFill} />
      <circle cx="11.999" cy="18.7119" r="0.913059" fill={currentFill} />
      <circle cx="17.8076" cy="15.3916" r="0.913059" fill={currentFill} />
      <circle cx="17.8076" cy="8.58493" r="0.913059" fill={currentFill} />
      <circle cx="11.999" cy="5.26462" r="0.913059" fill={currentFill} />
      <path
        d="M14.5727 11.9052C14.5727 13.2805 13.4592 14.4784 12.0839 14.4784C10.7086 14.4784 9.59375 13.3635 9.59375 11.9882C9.59375 10.6129 10.7086 9.49805 12.0839 9.49805C13.4592 9.49805 14.5727 10.5299 14.5727 11.9052Z"
        fill="white"
      />
      <circle cx="6.35447" cy="8.58493" r="0.913059" fill={currentFill} />
      <path
        d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default OpportunityIcon;
