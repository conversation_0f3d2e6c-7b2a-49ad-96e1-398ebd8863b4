import { useState } from 'react';
import Box from '@mui/material/Box';
import { IconButton, InputBase } from '@mui/material';
import VoiceIcon from '../Icons/VoiceIcon';

import FilterIcon from '../Icons/FeedIcons/FilterIcon';
import Image from 'next/image';
import { SEARCH_ICON } from '../auth';
import FilterPopup from '../popups/FilterPopup';

export default function TopBar() {
  const [openFilter, setOpenFilter] = useState(false);

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        mt: '40px',
        width: { smd: '100%', lg: '719px' },
        pr: { smd: '20px', lg: '0px' },
      }}
    >
      {/* Search Box */}
      <Box
        sx={{
          width: '100%',
          height: '45px',
          display: 'flex',
          alignItems: 'center',
          gap: '20px',
        }}
      >
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            borderRadius: '8px',
            overflow: 'hidden',
            border: '0.5px solid #A3A3A3',
            backgroundColor: 'white',
          }}
        >
          <Box
            sx={{
              padding: '10px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '46px',
              width: '64px',
              borderRight: '1px solid #A3A3A3',
            }}
          >
            <Image src={SEARCH_ICON} alt="Search" width={20} height={20} />
          </Box>
          <InputBase
            placeholder="Search by keyword, poster, or tag"
            sx={{
              flex: 1,
              paddingLeft: '12px',
              paddingRight: '12px',
              backgroundColor: 'white',
              height: '100%',
            }}
          />
          <IconButton>
            <VoiceIcon fill="#A24295" />
          </IconButton>
        </Box>

        <Box
          sx={{
            border: '1px solid #A24295',
            borderRadius: '8px',
            backgroundColor: 'white',
            p: '8px',
            cursor: 'pointer',
            position: 'relative',
            ':hover': { backgroundColor: '#F6ECF4' },
          }}
          onClick={() => setOpenFilter(!openFilter)}
        >
          <FilterIcon />
          {openFilter && <FilterPopup onClose={() => setOpenFilter(false)} />}
        </Box>
      </Box>
    </Box>
  );
}
