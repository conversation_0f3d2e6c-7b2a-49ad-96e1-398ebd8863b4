import { Stack, CircularProgress, Box } from '@mui/material';
import { useState } from 'react';

import CustomizedSteppers from '../Stepper/Stepper';
import ProfileCard from './ProfileCard';
import { ProfileData } from '../../../../../../apis/src/lib/networking/networking-hooks';
import { Subtitle } from '../../../auth/components/Subtitle';

interface ConnectWithOthersProps {
  profiles?: ProfileData[];
  isLoading?: boolean;
  onConnect?: (profileId: string) => void;
  onFollow?: (profileId: string) => void;
  onUnfollow?: (profileId: string, profileName: string) => void;
  onCancelRequest?: (profileId: string, profileName: string) => void;
  connectionRequests?: Record<string, boolean>;
  followedProfiles?: Record<string, boolean>;
  hideSteppers?: boolean;
}

const ConnectWithOthers = ({
  profiles,
  isLoading = false,
  onConnect,
  onFollow,
  onUnfollow,
  onCancelRequest,
  connectionRequests = {},
  followedProfiles = {},
  hideSteppers = false,
}: ConnectWithOthersProps) => {
  const [activeStep] = useState(2); // Fixed: removed unused setActiveStep

  // No local state needed anymore since we're using Zustand

  // Default profiles to use when no profiles are provided
  const defaultProfiles = [
    {
      id: '1',
      name: 'Dr. Bindiya Sharma',
      qualification: 'MD. PhD. BBS.',
      rating: 4,
      segmentCategory: 'Sr. Cardiologist',
      sponsoredBy: 'SMD Labs',
      worksAt: 'Woodlands Hospital, Kolkata, IN',
      avatarUrl:
        'https://images.pexels.com/photos/3831569/pexels-photo-3831569.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      isConnected: false,
      isFollowing: false,
      connectionRequestSent: false,
    },
    {
      id: '2',
      name: 'Dr. Sanu Sharma',
      qualification: 'MD. Mbbs. BBS.',
      rating: 4,
      segmentCategory: 'Jr. Cardiologist',
      worksAt: 'Almas Hospital, Kerala, IN',
      avatarUrl:
        'https://images.pexels.com/photos/9831613/pexels-photo-9831613.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      isConnected: false,
      isFollowing: false,
      connectionRequestSent: false,
    },
    {
      id: '3',
      name: 'Mr. John Doe',
      qualification: 'MD. LLb.',
      rating: 2,
      segmentCategory: 'Associate Surgeon',
      worksAt: 'KN Hospital, Delhi, IN',
      avatarUrl:
        'https://images.pexels.com/photos/697509/pexels-photo-697509.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      isConnected: false,
      isFollowing: false,
      connectionRequestSent: false,
    },
    {
      id: '4',
      name: 'Mr. Moorthy',
      qualification: 'MD. Mbbs',
      rating: 5,
      segmentCategory: 'Sr Surgeon',
      worksAt: 'JK Hospital, Goa, IN',
      avatarUrl:
        'https://images.pexels.com/photos/3777570/pexels-photo-3777570.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      isConnected: false,
      isFollowing: false,
      connectionRequestSent: false,
    },
  ];

  const handleFollowAction = (
    profileId: string,
    profileName: string,
    isFollowing: boolean
  ) => {
    if (isFollowing) {
      // If already following, trigger unfollow action
      if (onUnfollow) {
        onUnfollow(profileId, profileName);
      }
    } else {
      if (onFollow) {
        onFollow(profileId);
      }
    }
  };

  const handleConnectAction = (
    profileId: string,
    profileName: string,
    connectionRequestSent: boolean
  ) => {
    if (connectionRequestSent) {
      if (onCancelRequest) {
        onCancelRequest(profileId, profileName);
      }
    } else {
      if (onConnect) {
        onConnect(profileId);
      }
    }
  };

  // Use provided profiles or fall back to default profiles
  const displayProfiles = profiles || defaultProfiles;

  const enhancedProfiles = displayProfiles.map((profile) => ({
    ...profile,
    isFollowing: followedProfiles?.[profile.id] || profile.isFollowing,
    connectionRequestSent:
      connectionRequests?.[profile.id] || profile.connectionRequestSent,
  }));

  return (
    <Stack alignItems={'center'} sx={{ marginBottom: '100px' }}>
      {!hideSteppers && (
        <CustomizedSteppers
          activeStep={activeStep}
          steps={['Profile Setup', 'Document Upload', 'Adding Network']}
        />
      )}

      <Subtitle
        text="Start building your network! Here's some accounts you might like to follow:"
        sx={{
          fontSize: {
            xs: '12px',
            sm: '16px',
          },
          mt: '40px',
        }}
      />
      {/* Loading state */}
      {isLoading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            width: '100%',
            py: 4,
          }}
        >
          <CircularProgress />
        </Box>
      ) : (
        /* Profile section */
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',
            gap: { xs: '24px', sm: '40px' },
            width: '100%',
            position: 'relative',
            zIndex: 1,
          }}
        >
          {enhancedProfiles.map((profile) => (
            <Box
              key={profile.id}
              sx={{
                display: 'flex',
                justifyContent: 'center',
                visibility: 'visible !important', // Force visibility
                opacity: 1,
                position: 'relative',
                zIndex: 1,
                minHeight: '359px', // Match card height
                minWidth: '320px', // Match card width
              }}
            >
              <ProfileCard
                id={profile.id}
                name={profile.name}
                qualification={profile.qualification}
                rating={profile.rating}
                segmentCategory={profile.segmentCategory}
                sponsoredBy={profile.sponsoredBy}
                worksAt={profile.worksAt}
                avatarUrl={profile.avatarUrl}
                isConnected={profile.isConnected}
                isFollowing={profile.isFollowing}
                connectionRequestSent={
                  connectionRequests?.[profile.id] ||
                  profile.connectionRequestSent
                }
                onConnect={() =>
                  handleConnectAction(
                    profile.id,
                    profile.name,
                    profile.connectionRequestSent ||
                      connectionRequests?.[profile.id] ||
                      false
                  )
                }
                onFollow={() =>
                  handleFollowAction(
                    profile.id,
                    profile.name,
                    profile.isFollowing ||
                      followedProfiles?.[profile.id] ||
                      false
                  )
                }
              />
            </Box>
          ))}
        </Box>
      )}
    </Stack>
  );
};

export default ConnectWithOthers;
