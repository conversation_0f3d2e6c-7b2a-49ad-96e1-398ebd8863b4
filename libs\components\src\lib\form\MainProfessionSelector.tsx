import {
  Box,
  Typography,
  ToggleButton,
  ToggleButtonGroup,
} from '@mui/material';
import { Controller, FieldErrors, FieldValues } from 'react-hook-form';

type Option = 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH' | null;

interface MainProfessionSelectorProps<T extends FieldValues> {
  control: any;
  errors: FieldErrors<T>;
  mainWork: Option;
  setMainWork: (val: Option) => void;
  isBasicPlan?: boolean;
}

const MainProfessionSelector = <T extends Record<string, any>>({
  control,
  errors,
  mainWork,
  setMainWork,
  isBasicPlan = false,
}: MainProfessionSelectorProps<T>) => {
  const getStyle = (theme: any, active: boolean) => ({
    border: 'none',
    backgroundColor: active ? '#E3C6DFBF' : 'transparent',
    color: active ? theme.palette.neutral[600] : '#1E1E1E',
    fontFamily: "'Plus Jakarta Sans', sans-serif",
    fontWeight: active ? 700 : 300,
    fontSize: '14px',
    width: !isBasicPlan ? 'fit-content' : '100%',
    textTransform: 'none',
    boxShadow: 'none',
    borderRadius: "active ? '8px' : '0px'",
    whiteSpace: 'nowrap',
    padding: '6px 8px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    '&:hover': {
      backgroundColor: active ? '#E3C6DFBF' : 'rgba(0, 0, 0, 0.04)',
    },
    '&.Mui-selected': {
      backgroundColor: '#E3C6DFBF',
      color: theme.palette.secondary.main,
      borderRadius: '8px',
    },
    '&.Mui-selected:hover': {
      backgroundColor: '#E3C6DF',
    },
  });

  return (
    <Controller
      name="mainProfession"
      control={control}
      render={({ field }) => (
        <Box sx={{ width: '100%' }}>
          <Typography
            sx={{
              fontSize: '16px',
              fontWeight: 500,
            }}
          >
            I mainly work with:
          </Typography>
          <Box
            sx={{
              border: mainWork ? '1px solid #A24295' : '1px solid #A3A3A3',
              borderRadius: '8px',
              p: '4px',
            }}
          >
            <ToggleButtonGroup
              fullWidth
              color="secondary"
              exclusive
              value={field.value}
              onChange={(_, newValue: Option) => {
                if (newValue !== null) {
                  field.onChange(newValue);
                  setMainWork(newValue);
                }
              }}
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                gap: '2px',
              }}
            >
              <ToggleButton
                value="CARDIAC_SURGEON"
                size="small"
                sx={(theme) => getStyle(theme, mainWork === 'CARDIAC_SURGEON')}
              >
                Cardiac Surgeons
              </ToggleButton>
              <ToggleButton
                value="CARDIOLOGIST"
                size="small"
                sx={(theme) => getStyle(theme, mainWork === 'CARDIOLOGIST')}
              >
                Cardiologists
              </ToggleButton>
              {!isBasicPlan && (
                <ToggleButton
                  value="BOTH"
                  size="small"
                  sx={(theme) => getStyle(theme, mainWork === 'BOTH')}
                >
                  Both
                </ToggleButton>
              )}
            </ToggleButtonGroup>
          </Box>
          {errors?.mainProfession?.message && (
            <Typography color="error" variant="caption" sx={{ mt: 1 }}>
              {errors.mainProfession.message as string}
            </Typography>
          )}
        </Box>
      )}
    />
  );
};

export default MainProfessionSelector;
