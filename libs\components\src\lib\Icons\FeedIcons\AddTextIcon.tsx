const AddTextIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.9997 5.93103C19.9997 5.41889 19.7963 4.92773 19.4341 4.56559C19.072 4.20345 18.5808 4 18.0687 4H15.5859V5.65517H18.0687C18.1419 5.65517 18.212 5.68424 18.2638 5.73597C18.3155 5.7877 18.3446 5.85787 18.3446 5.93103V8.41379H19.9997V5.93103Z"
        fill={hoverFill}
      />
      <path
        d="M19.9997 18.0692V15.5864H18.3446V18.0692C18.3446 18.1423 18.3155 18.2125 18.2638 18.2642C18.212 18.316 18.1419 18.345 18.0687 18.345H15.5859V20.0002H18.0687C18.5808 20.0002 19.072 19.7968 19.4341 19.4346C19.7963 19.0725 19.9997 18.5813 19.9997 18.0692Z"
        fill={hoverFill}
      />
      <path
        d="M4 18.0692C4 18.5813 4.20345 19.0725 4.56559 19.4346C4.92773 19.7968 5.41889 20.0002 5.93103 20.0002H8.41379V18.345H5.93103C5.85787 18.345 5.7877 18.316 5.73597 18.2642C5.68424 18.2125 5.65517 18.1423 5.65517 18.0692V15.5864H4V18.0692Z"
        fill={hoverFill}
      />
      <path
        d="M4 8.41379H5.65517V5.93103C5.65517 5.85787 5.68424 5.7877 5.73597 5.73597C5.7877 5.68424 5.85787 5.65517 5.93103 5.65517H8.41379V4H5.93103C5.41889 4 4.92773 4.20345 4.56559 4.56559C4.20345 4.92773 4 5.41889 4 5.93103V8.41379Z"
        fill={hoverFill}
      />
      <path
        d="M12.8279 16.1377V10.0687H15.5865V8.41357H8.41406V10.0687H11.1727V16.1377H12.8279Z"
        fill={hoverFill}
      />
    </svg>
  );
};

export default AddTextIcon;
