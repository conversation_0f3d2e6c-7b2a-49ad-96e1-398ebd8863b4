'use client';

import { SignupPage } from '@minicardiac-client/components';
import { useState } from 'react';
import { useAuth } from '@minicardiac-client/apis';
import { useRouter } from '@/apps/public/src/i18n/navigation';
import { useTranslations } from 'next-intl';

export default function SignUpProfessionalPage() {
  const router = useRouter();
  const t = useTranslations('signup');
  const [isRegistering, setIsRegistering] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { registerUser } = useAuth();

  const handleSignUp = async (data: {
    email: string;
    password: string;
    displayName?: string;
    organizationName?: string;
  }) => {
    setIsRegistering(true);
    setError(null);
    try {
      const response = await registerUser({
        email: data.email,
        password: data.password,
        displayName: data.displayName || '',
        accountType: 'PROFESSIONAL',
      });

      if (response.requiresOTP) {
        router.push(
          `/verify-otp?email=${encodeURIComponent(
            data.email
          )}&name=${encodeURIComponent(data.displayName || '')}&accountType=PROFESSIONAL`
        );
      } else {
        router.push('/professional/type-selection');
      }
    } catch (err) {
      const errorMessage = 'Something went wrong. Please try again.';
      setError(errorMessage);
    } finally {
      setIsRegistering(false);
    }
  };

  const backNavigate = () => {
    setTimeout(() => {
      router.back();
    }, 300);
  };

  return (
    <SignupPage
      userType="professional"
      onBack={backNavigate}
      onSignUp={handleSignUp}
      isLoading={isRegistering}
      error={error}
      translations={{
        title: t('title'),
        subtitle: t('subtitle'),
        emailLabel: t('emailLabel'),
        passwordLabel: t('passwordLabel'),
        forgotPassword: t('forgotPassword'),
        continueLabel: t('continue'),
        orLabel: t('or'),
        googleLabel: t('google'),
        appleLabel: t('apple'),
        signIn: t('signIn'),
        signUp: t('signUp'),
      }}
    />
  );
}
