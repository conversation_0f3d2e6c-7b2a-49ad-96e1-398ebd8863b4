import { Editor } from '@tiptap/react';
import { MenuItem, Select } from '@mui/material';
import { useState } from 'react';

export default function MenuSelectHeading({ editor }: { editor: Editor }) {
  const [value, setValue] = useState<'normal' | 'heading' | 'subheading'>(
    'normal'
  );

  if (!editor) return null;

  const handleChange = (newValue: 'normal' | 'heading' | 'subheading') => {
    setValue(newValue);
    editor.chain().focus();

    if (newValue === 'normal') {
      editor.chain().focus().setParagraph().run();
    } else if (newValue === 'heading') {
      editor.chain().focus().toggleHeading({ level: 1 }).run();
    } else if (newValue === 'subheading') {
      editor.chain().focus().toggleHeading({ level: 2 }).run();
    }
  };

  return (
    <Select
      value={value}
      onChange={(e) =>
        handleChange(e.target.value as 'normal' | 'heading' | 'subheading')
      }
      variant="standard"
      disableUnderline
      size="small"
      sx={{
        fontSize: '14px',
        color: '#333',
        background: 'transparent',
        p: 0,
        m: 0,
        '& .MuiSelect-select': {
          padding: '2px 8px',
        },
      }}
    >
      <MenuItem value="normal">Normal</MenuItem>
      <MenuItem value="heading">Heading</MenuItem>
      <MenuItem value="subheading">Subheading</MenuItem>
    </Select>
  );
}
