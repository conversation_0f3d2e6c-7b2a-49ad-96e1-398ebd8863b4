{"name": "@minicardiac-client/public", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/public", "projectType": "application", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/public/jest.config.ts", "passWithNoTests": true}}, "serve": {"executor": "@nx/next:server", "options": {"buildTarget": "@minicardiac-client/public:build"}}}}