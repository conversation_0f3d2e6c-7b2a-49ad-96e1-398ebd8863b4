import React, { useState } from 'react';
import { Menu, Menu<PERSON>tem, Button, Typography, Box } from '@mui/material';
import { useSlate } from 'slate-react';
import { Editor, Element as SlateElement } from 'slate';
import { Iconify } from '../iconify';

const BLOCK_TYPES = [
  {
    label: 'Paragraph',
    format: 'paragraph',
    style: { fontSize: 14, fontWeight: 400 },
  },
  {
    label: 'Heading 1',
    format: 'heading-one',
    style: { fontSize: 18, fontWeight: 700 },
  },
  {
    label: 'Heading 2',
    format: 'heading-two',
    style: { fontSize: 17, fontWeight: 600 },
  },
  {
    label: 'Heading 3',
    format: 'heading-three',
    style: { fontSize: 16, fontWeight: 600 },
  },
  {
    label: 'Heading 4',
    format: 'heading-four',
    style: { fontSize: 15, fontWeight: 500 },
  },
  {
    label: 'Heading 5',
    format: 'heading-five',
    style: { fontSize: 14, fontWeight: 500 },
  },
  {
    label: 'Heading 6',
    format: 'heading-six',
    style: { fontSize: 13, fontWeight: 500 },
  },
];

type BlockTypeDropdownProps = {
  toggleBlock: (format: string) => void;
};

export const BlockTypeDropdown = ({ toggleBlock }: BlockTypeDropdownProps) => {
  const editor = useSlate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  // Get current block type
  const getCurrentBlockType = () => {
    const [match] = Editor.nodes(editor, {
      match: (n) =>
        !Editor.isEditor(n) &&
        SlateElement.isElement(n) &&
        // Check for headings or paragraph
        [
          'paragraph',
          'heading-one',
          'heading-two',
          'heading-three',
          'heading-four',
          'heading-five',
          'heading-six',
        ].includes(n.type as string),
    });
    return match ? (match[0] as SlateElement).type : 'paragraph';
  };

  const currentBlock = getCurrentBlockType();

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSelect = (format: string) => {
    toggleBlock(format);
    handleClose();
  };

  return (
    <Box>
      <Button
        onClick={handleClick}
        sx={{
          minWidth: 100,
          textTransform: 'none',
          border: '1px solid black',
          color: 'black',
          p: '12px',
          ':hover': {
            backgroundColor: '#E3C6DFBF',
          },
        }}
        variant="outlined"
      >
        <Typography
          sx={{
            ...(BLOCK_TYPES.find((b) => b.format === currentBlock)?.style ??
              {}),
          }}
        >
          {BLOCK_TYPES.find((b) => b.format === currentBlock)?.label ||
            'Paragraph'}
        </Typography>
        <Iconify icon={'mdi:chevron-down'} />
      </Button>
      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>
        {BLOCK_TYPES.map(({ label, format, style }) => {
          const isSelected = currentBlock === format;

          return (
            <MenuItem
              key={format}
              selected={isSelected}
              onClick={() => handleSelect(format)}
              sx={{
                ...(isSelected && {
                  backgroundColor: 'red',
                  '&:hover': {
                    backgroundColor: '#e1bee7',
                  },
                }),
              }}
            >
              <Typography sx={{ ...style }}>{label}</Typography>
            </MenuItem>
          );
        })}
      </Menu>
    </Box>
  );
};
