'use client';

import { Card, CardContent, Stack, Typography, Box } from '@mui/material';
import { SignupOptionCardProps } from '../types/auth.types';
import { ExtendedTheme } from '../types/auth.types';
import { useState } from 'react';
import BackgroundShapeIcon from '../../Icons/BackgroundShape';
import CardiacSurgeonIcon from '../../Icons/CardiacSurgeonIcon';
import CardiologistIcon from '../../Icons/CardiologistIcon';
import AlliedCardiacPersonTwo from '../../Icons/AlliedCardiacPersonTwo';
import AlliedCardiacPersonOne from '../../Icons/AlliedCardiacPersonOne';
import CardiacSurgeonTableIcon from '../../Icons/CardiacSurgeonTableIcon';

// Using SignupOptionCardProps from auth.types.ts

export const ProfessionalSignupOptionCard = ({
  option,
  onClick,
  variant,
  width,
  height,
  selected,
  additionalImage,
  titleTypographyProps,
}: SignupOptionCardProps) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Card
      onClick={() => onClick(option.path)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      sx={(theme) => ({
        boxShadow: 'none',
        width: {
          xs: '360px',
          sm: '100%',
        },
        height: { xs: '146px', sm: '180px', xxl: '200px' },
        borderRadius: `${
          (theme as ExtendedTheme).customValues?.signupCard?.borderRadius || 12
        }px`,
        border: selected ? '3px solid #A24295' : '0.5px solid #A24295',
        cursor: 'pointer',
        transition: 'transform 0.2s, box-shadow 0.2s',
        position: 'relative',
        overflow: 'hidden',
        backgroundColor: '#FFFFFF',
        '&:hover': {
          transform: 'translateY(-2px)',
        },
      })}
    >
      <CardContent
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          p: 3,
          position: 'relative',
          height: '100%',
        }}
      >
        {/* Background Shape */}

        <Box
          sx={{
            position: 'absolute',
            width: '200px',
            height: '200px',
            right: '-70px',
            top: { xs: '0px', xxl: '-10px' },
            opacity: 0.7,
            zIndex: 0,
            pointerEvents: 'none',
          }}
        >
          <BackgroundShapeIcon hovered={isHovered || selected} />
        </Box>

        {/* Additional Image (like op-light.svg) */}
        {additionalImage && (
          <Box
            component="img"
            src={additionalImage}
            alt="Additional illustration"
            sx={(theme) => ({
              position: 'absolute',
              width:
                (theme as ExtendedTheme).customValues?.opLights?.width || 85.56,
              height:
                (theme as ExtendedTheme).customValues?.opLights?.height ||
                74.48,
              top: '0px',
              right: 2,
              left: 'auto',
              zIndex: 3,
              pointerEvents: 'none',
              overflow: 'hidden',
            })}
          />
        )}

        {/* Character Image */}
        <Box
          component="div"
          sx={(theme) => {
            const title = option.title.toLowerCase();
            const characterKey =
              title === 'professional'
                ? 'doctorCharacter'
                : title === 'organization'
                ? 'gentlemanCharacter'
                : 'girlManCharacter';

            return {
              position: 'absolute',
              width: (theme as ExtendedTheme).customValues[characterKey]?.width,
              height: (theme as ExtendedTheme).customValues[characterKey]
                ?.height,
              top: {
                xs: '65px',
                sm: '65px',
                md: '65px',
                xl: '64px',
                xxl: '85px',
              },
              right: '0px',
              zIndex: 2,
            };
          }}
        >
          {option.title.toLowerCase() === 'cardiac surgeon' ? (
            <>
              <CardiacSurgeonIcon hovered={isHovered || selected} />
              <Box
                sx={{
                  position: 'inherit',
                  right: '-2px',
                  bottom: { xl: '-31px' },
                }}
              >
                <CardiacSurgeonTableIcon hovered={isHovered || selected} />
              </Box>
            </>
          ) : option.title.toLowerCase() === 'cardiologist' ? (
            <Box
              sx={{
                position: 'inherit',
                top: '-12px',
              }}
            >
              <CardiologistIcon hovered={isHovered || selected} />
            </Box>
          ) : option.title.toLowerCase() === 'allied cardiac' ? (
            <>
              <AlliedCardiacPersonOne hovered={isHovered || selected} />
              <AlliedCardiacPersonTwo hovered={isHovered || selected} />
            </>
          ) : null}
        </Box>

        {/* Content */}
        <Stack
          spacing={1.5}
          sx={{
            position: 'relative',
            zIndex: 2,
            maxWidth: '60%',
          }}
        >
          <Typography
            variant="h5"
            component="span"
            {...titleTypographyProps}
            sx={{
              display: 'inline-block',
              fontFamily: (theme) => theme.typography.fontFamily,
              fontSize: '20px',
              lineHeight: 1.2,
              letterSpacing: 0,
              ...(titleTypographyProps?.sx || {}),
              color: selected
                ? (theme) => (theme.palette as any).secondary.main
                : (theme) => theme.palette.text.primary,
              fontWeight: selected ? 700 : 400,
            }}
          >
            {option.title}
          </Typography>

          <Typography
            variant="body2"
            sx={(theme) => ({
              color: 'neutral.lowEmphasis',
              width:
                (theme as ExtendedTheme).customValues?.signupCard?.content
                  ?.description?.width || 'auto',
              fontFamily: theme.typography.fontFamily,
              fontWeight: 300,
              fontSize: '12px',
              lineHeight: '16px',
              letterSpacing: 0,
            })}
          >
            {option.description}
          </Typography>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default ProfessionalSignupOptionCard;
