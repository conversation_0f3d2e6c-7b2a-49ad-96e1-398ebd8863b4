import { useState } from 'react';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Typography from '@mui/material/Typography';
import {
  alpha,
  Box,
  Button,
  Snackbar,
  Alert,
} from '@mui/material';
import {
  useGetSubscriptionPlans,
  useCreateSubscription,
} from '@minicardiac-client/apis';
import { useRouter } from 'next/navigation';

import { subscriptionFeatures } from '../constants/onboarding.constants';

import ButtonSelect from './ButtonSelect';
import { FeatureItem } from './SubscriptionFeatures';
import { FullPageLoader } from '../../full-page-loader/full-page-loader';

export default function SubscriptionTable({
  userSegment = 'CARDIAC_SPECIALIST',
  billingCycle = 'monthly',
  professionalType,
}: {
  userSegment?: string;
  billingCycle?: 'monthly' | 'yearly';
  professionalType?: string | null;
  // onSwitchToCardView: () => void;
}) {
  const router = useRouter();
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertSeverity, setAlertSeverity] = useState<'success' | 'error'>(
    'success'
  );

  const {
    data: subscriptionPlans,
    isLoading,
    isFetching,
    isError,
  } = useGetSubscriptionPlans(userSegment);
  const createSubscriptionMutation = useCreateSubscription();

  const handleProceedToPayment = async () => {
    if (selectedId !== null && subscriptionPlans) {
      const selectedPlan = subscriptionPlans.find(
        (plan) => plan.id === selectedId
      );
      console.log('Proceeding to payment for plan:', selectedPlan);

      try {
        // Call the subscription API
        await createSubscriptionMutation.mutateAsync({
          subscriptionPlanId: selectedId,
          isYearly: billingCycle === 'yearly',
          ...(professionalType && { professionalCategory: professionalType }),
        });

        // Show success message
        setAlertSeverity('success');
        setAlertMessage('Subscription created successfully!');
        setAlertOpen(true);

        //Navigate based on user segment
        if (userSegment === 'ORGANISATION') {
          if (selectedPlan?.title.toLowerCase() === 'free') {
            router.push('/organisation/subscription/free/profile-setup');
          } else if (
            selectedPlan?.title.toLowerCase() === 'primary' ||
            selectedPlan?.title.toLowerCase() === 'premium'
          ) {
            router.push(
              `/organisation/subscription/paid/profile-setup?plan=${selectedPlan?.title.toLowerCase()}`
            );
          } else if (selectedPlan?.title.toLowerCase() === 'prestige') {
            router.push(
              `/organisation/subscription/prestige/profile-setup?plan=${selectedPlan?.title.toLowerCase()}`
            );
          }

          return;
        }

        // Navigate based on plan type
        if (selectedPlan?.title.toLowerCase() === 'free') {
          router.push(`/professional/free/profile-setup?type=${professionalType || ''}`);
        } else {
          router.push(
            `/professional/paid/profile-setup?plan=${selectedPlan?.title.toLowerCase()}&type=${professionalType || ''}`
          );
        }
      } catch (err: unknown) {
        const error = err as {
          message?: string;
          response?: {
            data?: {
              message?: string;
            };
          };
        };
        console.error('Error creating subscription:', error);

        // Show error message
        setAlertSeverity('error');
        setAlertMessage(
          error?.message ||
            error?.response?.data?.message ||
            'Failed to create subscription. Please try again.'
        );
        setAlertOpen(true);
      }
    }
  };

  // Show full page loader during initial load or retries - no retry messages shown to user
  if (isLoading || isFetching) {
    return <FullPageLoader open={true} />;
  }

  // If all retries fail, still show full page loader to avoid showing error to user
  // The backend team can investigate the API issues while user experience remains smooth
  if (isError || !subscriptionPlans || subscriptionPlans.length === 0) {
    return <FullPageLoader open={true} />;
  }

  // Handle alert close
  const handleAlertClose = () => {
    setAlertOpen(false);
  };

  return (
    <Paper
      elevation={0}
      sx={{
        width: '100%',
        overflow: 'hidden',
        borderRadius: '12px',
        boxShadow: (theme) =>
          `0px 12px 24px 0px ${alpha(
            (theme.palette as any).neutral[500],
            0.25
          )}`,
      }}
    >
      {/* Alert for success/error messages */}
      <Snackbar
        open={alertOpen}
        autoHideDuration={6000}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleAlertClose}
          severity={alertSeverity}
          sx={{ width: '100%' }}
        >
          {alertMessage}
        </Alert>
      </Snackbar>
      <TableContainer
        sx={{
          maxHeight: 'auto',
        }}
      >
        <Table aria-label="subscription compare table">
          <TableHead>
            <TableRow>
              <TableCell
                align={'left'}
                sx={{ width: 'auto', bgcolor: 'neutral.200' }}
              />
              {subscriptionPlans.map((tier) => (
                <TableCell
                  key={tier.id}
                  align={'center'}
                  sx={{
                    width: '189px',
                    bgcolor: 'neutral.200',
                  }}
                >
                  <Typography
                    variant="subtitle1"
                    component="span"
                    sx={(theme) => ({
                      ...(theme.typography as any).subtitle3,
                    })}
                  >
                    {tier.title}
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={(theme) => ({
                      color: (theme) => theme.palette.text.primary,
                      ...(theme.typography as any).subtitle2,
                    })}
                  >
                    $
                    {billingCycle === 'monthly'
                      ? tier.priceMonthly
                      : tier.priceYearly}
                  </Typography>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody
            sx={{
              bgcolor: 'white',
            }}
          >
            {subscriptionFeatures.map((feat) => {
              return (
                <TableRow
                  key={feat.key}
                  sx={{
                    position: 'relative',
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      bottom: 0,
                      left: 0,
                      right: 0,
                      borderBottom: (theme) =>
                        `1px dashed ${alpha(
                          (theme.palette as any).neutral[500],
                          0.25
                        )}`,
                      height: 1,
                    },
                  }}
                  hover
                  tabIndex={-1}
                >
                  <TableCell align={'left'}>{feat.label}</TableCell>
                  {subscriptionPlans.map((tier) => {
                    // Find the feature by name instead of key
                    const value = tier.planFeatures.find(
                      (tierFeat: any) =>
                        tierFeat.subscriptionFeature.name.toLowerCase() ===
                        feat.label.toLowerCase()
                    );

                    return (
                      <TableCell key={tier.id} align="center">
                        {value && (
                          <FeatureItem showText={false} feature={value} />
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              );
            })}
            <TableRow tabIndex={-1}>
              <TableCell align="left"></TableCell>
              {subscriptionPlans.map((sub) => (
                <TableCell align="center" key={sub.id}>
                  <ButtonSelect
                    onClick={() => setSelectedId(sub.id)}
                    isActive={selectedId === sub.id}
                  />
                </TableCell>
              ))}
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>

      {/* Payment button */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4, mb: 2 }}>
        <Button
          variant="contained"
          onClick={handleProceedToPayment}
          disabled={selectedId === null}
          sx={{
            width: '225px',
            height: '48px',
            backgroundColor: '#A24295',
            borderRadius: '8px',
            paddingX: '48px',
            fontFamily: "'Plus Jakarta Sans', sans-serif",
            fontWeight: 700,
            fontSize: '16px',
            lineHeight: '24px',
            letterSpacing: '0px',
            textAlign: 'center',
            whiteSpace: 'nowrap',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            textTransform: 'none',
            boxShadow: 'none',
            '&:hover': {
              backgroundColor: '#B24E9F',
              boxShadow: 'none',
            },
            '&.Mui-disabled': {
              backgroundColor: '#E1E3E6',
              color: '#A3A3A3',
              opacity: 1,
            },
          }}
        >
          Proceed to payment
        </Button>
      </Box>
    </Paper>
  );
}
