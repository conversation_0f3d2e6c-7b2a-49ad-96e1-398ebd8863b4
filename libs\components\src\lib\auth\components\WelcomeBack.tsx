import { Box, Typography } from '@mui/material';

/**
 * WelcomeBack component that displays the welcome back message
 */
export const WelcomeBack = ({ displayText = 'Welcome back to' }) => {
  return (
    <Box
      sx={{
        textAlign: 'center',
        mb: 0.5,
        whiteSpace: 'nowrap',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '4px',
      }}
    >
      <Typography
        sx={(theme) => ({
          fontFamily: "'Plus Jakarta Sans', sans-serif",
          fontWeight: 300,
          fontSize: { xs: '28px', sm: '32px' },
          lineHeight: '100%',
          letterSpacing: '0%',
          color: theme.palette.grey[900],
          display: 'inline-block',
          verticalAlign: 'middle',
        })}
      >
        {displayText}
      </Typography>
      <Typography
        component="span"
        sx={() => ({
          fontFamily: "'Plus Jakarta Sans', sans-serif",
          fontSize: { xs: '24px', sm: '32px' },
          fontWeight: 500,
          lineHeight: '100%',
          display: 'inline-block',
          backgroundImage:
            'linear-gradient(90deg, rgba(249, 34, 67, 1) 0%, rgba(162, 66, 149, 1) 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          color: 'transparent',
          verticalAlign: 'middle',
        })}
      >
        MiniCardiac
      </Typography>
    </Box>
  );
};

export default WelcomeBack;
