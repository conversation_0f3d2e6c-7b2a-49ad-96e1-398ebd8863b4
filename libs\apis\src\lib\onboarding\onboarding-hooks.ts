import { useMutation } from '@tanstack/react-query';
import { axiosInstance } from '../http-client.js';
import { establishSession } from '../auth/auth-utils.js';

// Define query keys for onboarding
export const onboardingQueryKeys = {
  all: ['onboarding'] as const,
};

// Helper function to ensure session before onboarding API calls
export const ensureSessionForOnboarding = async () => {
  // Ensure session is established before making onboarding API calls
  // This is needed because the backend requires a valid session for onboarding endpoints
  await establishSession();
};

// API function to complete networking stage
export const completeNetworkingStage = async (): Promise<string> => {
  await ensureSessionForOnboarding();

  const response = await axiosInstance.post('/onboarding/complete-networking-stage');
  return response.data;
};

// Hook to complete networking stage
export const useCompleteNetworkingStage = () => {
  return useMutation({
    mutationFn: completeNetworkingStage,
  });
};
