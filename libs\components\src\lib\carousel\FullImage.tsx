'use client';

import { Box } from '@mui/material';
import Image from 'next/image';
import { useState } from 'react';

type ImageProps = {
  src: string;
  position?: string;
  zIndex?: number;
};

const FullImage = ({
  src,
  position = 'center center',
  zIndex = 1,
}: ImageProps) => {
  const [isError, setIsError] = useState(false);

  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex,
      }}
    >
      {!isError && (
        <Image
          src={src}
          alt="Background Image"
          layout="fill"
          objectFit="cover"
          objectPosition={position}
          priority
          onError={() => setIsError(true)}
        />
      )}
    </Box>
  );
};

export default FullImage;
