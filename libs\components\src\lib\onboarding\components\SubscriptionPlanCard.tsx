'use client';

import { Card, CardContent, Stack, Typography, Box } from '@mui/material';

import { SubscriptionOptionCardProps } from '../types/onboarding.types';
import { FeatureItem } from './SubscriptionFeatures';
import SubcriptionPlanFreeIcon from '../../Icons/ProfessionalIcons/SubcriptionPlanFreeIcon';
import SubcriptionPlanPremiumIcon from '../../Icons/ProfessionalIcons/SubcriptionPlanPremiumIcon';
import SubcriptionPlanPrestigeIcon from '../../Icons/ProfessionalIcons/SubcriptionPlanPrestigeIcon';
import SubcriptionPlanPrimaryIcon from '../../Icons/ProfessionalIcons/SubcriptionPlanPrimaryIcon';
import { useState } from 'react';
import {
  PRESTIGE_ICON,
  PRESTIGE_ICON_FULL,
} from '../../auth/constants/auth.constants';
import Image from 'next/image';

// const ICONS: Record<string, JSX.Element> = {
//   Free: <SubcriptionPlanFreeIcon hovered />,
//   Premium: <SubcriptionPlanPremiumIcon hovered />,
//   Prestige: <SubcriptionPlanPrestigeIcon hovered />,
//   Primary: <SubcriptionPlanPrimaryIcon hovered />,
// };

export const SubscriptionPlanCard = ({
  option,
  onChoose,
  isActive,
  viewingPlan,
  billingCycle = 'monthly',
}: SubscriptionOptionCardProps & { billingCycle?: 'monthly' | 'yearly' }) => {
  const [hovered, setHovered] = useState(false);
  const showFullDetails = viewingPlan && isActive;

  const getIcon = () => {
    switch (option.title) {
      case 'Free':
        return <SubcriptionPlanFreeIcon hovered={hovered || isActive} />;
      case 'Premium':
        return <SubcriptionPlanPremiumIcon hovered={hovered || isActive} />;
      case 'Prestige':
        return <SubcriptionPlanPrestigeIcon hovered={hovered || isActive} />;
      case 'Primary':
        return <SubcriptionPlanPrimaryIcon hovered={hovered || isActive} />;
      default:
        return null;
    }
  };

  const getPrice =
    billingCycle === 'monthly' ? option.priceMonthly : option.priceYearly;

  return (
    <Card
      onClick={onChoose}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      sx={(theme: any) => {
        const custom = theme.customValues?.subscriptionPlanCard || {};

        return {
          width: showFullDetails ? '470px' : custom.width || '300px',
          height: showFullDetails ? 'auto' : custom.height || '400px',
          borderRadius: `${custom.borderRadius || 8}px`,
          border: `${isActive ? 3 : custom.borderWidth || 1}px solid ${
            isActive ? '#A24295' : custom.borderColor || '#E0E0E0'
          }`,
          cursor: 'pointer',
          overflow: 'hidden',
          backgroundColor: '#FFFFFF',
          boxShadow: 0,
          transition: 'all 0.4s ease-in-out',
          [theme.breakpoints.down('sm')]: {
            height: 'auto', // Make it auto on small screens
            width: '100%', // Optional: full width for better mobile layout
          },
        };
      }}
    >
      <CardContent
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: {
            xs: 0,
            sm: showFullDetails ? 4 : 2.5,
          },
          p: '20px !important',
          transition: 'padding 0.4s ease-in-out',
        }}
      >
        {/* Icon */}
        {!showFullDetails && (
          <Box
            sx={(theme: any) => {
              const iconSize =
                theme.customValues?.subscriptionPlanCard?.icon || {};
              return {
                width: iconSize.width,
                height: iconSize.height,
                alignSelf: 'center',
                display: { xs: 'none', sm: 'block' },
                transition: 'transform 0.5s ease',
              };
            }}
          >
            {getIcon()}
          </Box>
        )}

        {/* Header */}
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Stack gap="4px">
            {option.title === 'Prestige' ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                <Image
                  src={viewingPlan ? PRESTIGE_ICON_FULL : PRESTIGE_ICON}
                  alt={'Prestige icon'}
                  width={24}
                  height={24}
                />
                <Typography
                  variant="h6"
                  sx={(theme) => ({
                    fontFamily: theme.typography.fontFamily,
                    fontWeight: isActive ? 600 : 400,
                    fontSize: {
                      xs: '20px',
                      sm: showFullDetails ? '32px' : '24px',
                    },
                    lineHeight: '100%',
                    letterSpacing: '0px',
                    color: isActive ? '#A24295' : theme.palette.text.primary,
                  })}
                >
                  {option.title}
                </Typography>
              </Box>
            ) : (
              <Typography
                variant="h6"
                sx={(theme) => ({
                  fontFamily: theme.typography.fontFamily,
                  fontWeight: isActive ? 600 : 400,
                  fontSize: {
                    xs: '20px',
                    sm: showFullDetails ? '32px' : '24px',
                  },
                  lineHeight: '100%',
                  letterSpacing: '0px',
                  color: isActive ? '#A24295' : theme.palette.text.primary,
                })}
              >
                {option.title}
              </Typography>
            )}
            <Typography
              variant="body2"
              sx={(theme) => ({
                fontFamily: theme.typography.fontFamily,
                fontWeight: 500,
                fontSize: '12px',
                lineHeight: '100%',
                letterSpacing: '0px',
                display: { xs: 'block', sm: 'none' },
                color: showFullDetails ? theme.palette.text.primary : theme.palette.text.secondary,
              })}
            >
              {option.subtitle}
            </Typography>
          </Stack>

          <Typography
            variant="h6"
            sx={(theme) => ({
              ...(theme.typography as any).doctorName,
              color: isActive ? '#A24295' : '#1E1E1E',
              fontWeight: isActive ? 800 : 600,
              fontSize: {
                xs: '28px',
                sm: showFullDetails ? '40px' : '24px',
              },
            })}
          >
            ${getPrice}
          </Typography>
        </Stack>

        {/* Description (collapsed view) */}
        {!showFullDetails && (
          <Stack gap={0.5}>
            <Typography
              variant="body2"
              sx={(theme) => ({
                fontFamily: theme.typography.fontFamily,
                fontWeight: 500,
                fontSize: '12px',
                lineHeight: '100%',
                letterSpacing: '0px',
                display: { xs: 'none', sm: 'block' },
              })}
            >
              {option.subtitle}
            </Typography>
            <Typography
              variant="body1"
              sx={(theme) => ({
                ...(theme.typography as any).signupOptionDescription,
                color: '#737678',
                fontWeight: 300,
                fontSize: theme.typography.pxToRem(12),
                display: { xs: 'none', sm: '-webkit-box' },
                WebkitLineClamp: 6,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
              })}
            >
              {option.description}
            </Typography>
          </Stack>
        )}

        {/* Features (expanded view) */}
        {showFullDetails && (
          <Stack gap={2} py={'20px'} px={0}>
            {option.planFeatures?.map((feature, index) => (
              <FeatureItem
                key={feature.subscriptionFeature.id || index}
                feature={feature}
              />
            ))}
          </Stack>
        )}
      </CardContent>
    </Card>
  );
};

export default SubscriptionPlanCard;
