'use client';

import React from 'react';
import { Box } from '@mui/material';
import { useRouter } from '@/apps/public/src/i18n/navigation';
import { uploadDocuments, useAuth } from '@minicardiac-client/apis';
import {
  useSnackbar,
  DocumentUploadPageTemplate,
  ActionButtonsTemplate,
} from '@minicardiac-client/components';
import DocumentUploadForm from '@/libs/components/src/lib/onboarding/components/Documents/DocumentUpload';

export default function ProfessionalPaidDocumentUploadPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const { showSuccess, showError } = useSnackbar();

  const { authState } = useAuth();

  const handleDoThisLater = () => {
    // Navigate to landing page
    router.push('/feed?fromSignup=true');
  };

  const handleContinue = async () => {
    // Set submitting state
    setIsSubmitting(true);
    setError(null);

    try {
      // Session is already managed by SessionVerifier and middleware
      // uploadDocuments() already calls establishSession() internally
      await uploadDocuments();

      // Show success message
      showSuccess('Documents saved successfully!');

      // Navigate to the next step in the onboarding flow after a short delay
      setTimeout(() => {
        router.push('/professional/paid/add-network');
      }, 1000);
    } catch (err: any) {
      console.error('Error saving document data:', err);
      let userFriendlyError =
        'An unexpected error occurred. Please try again later.';
      if (
        err.response?.data?.message?.includes(
          'values() must be called with at least one value'
        )
      ) {
        userFriendlyError = 'Something went wrong. Please try again.';
      } else if (err.response?.status === 403) {
        userFriendlyError =
          'Access denied. Your session may have expired. Please try again.';
      }
      setError(userFriendlyError);
      showError(userFriendlyError);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DocumentUploadPageTemplate
      userName={authState.user?.displayName || ''}
      subtitleText="Let's set up your Professional Account!"
      showBackButton={true}
      onBack={() => router.back()}
      currentStep={1}
      steps={['Profile Setup', 'Document Upload', 'Adding Network']}
    >
      <DocumentUploadForm hideSteppers={true} />

      {/* Action Buttons using shared template */}
      <ActionButtonsTemplate
        onSave={handleContinue}
        onSkip={handleDoThisLater}
        isSubmitting={isSubmitting}
        isValid={true}
        saveButtonText="Save and Continue"
        skipButtonText="Do this later"
        variant="professional"
      />

      {/* Error Display */}
      {error && (
        <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
          {error}
        </Box>
      )}
    </DocumentUploadPageTemplate>
  );
}
