'use client';

import React from 'react';
import { Box, Container, useMediaQuery } from '@mui/material';
import { useRouter } from 'next/navigation';
import {
  OrganisationProfileSetupForm,
  OrganisationProfileFormData,
  CustomizedSteppers,
  PatientProfileWelcome,
  BackButton,
  Subtitle,
} from '@minicardiac-client/components';
import { axiosInstance, useAuth } from '@minicardiac-client/apis';
import { useSnackbar } from '@minicardiac-client/components';
import { useTheme } from '@emotion/react';

export default function OrganisationFreeProfileSetupPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const { showSuccess, showError } = useSnackbar();

  const { authState } = useAuth();
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const [formData, setFormData] = React.useState<OrganisationProfileFormData>({
    category: '',
    location: '',
    parentOrganisation: '',
    introductoryStatement: '',
    mainProfession: 'CARDIAC_SURGEON',
    mapLink: '',
    profileImageUrl: '',
    profileImageUrlThumbnail: '',
  });
  const [error, setError] = React.useState<string | null>(null);
  // We need setIsFormValid for the handleFormChange function
  const [, setIsFormValid] = React.useState<boolean>(false);

  // Session is already managed by SessionVerifier and middleware
  // No need for manual session refresh on component mount

  // Handle form changes
  const handleFormChange = (data: OrganisationProfileFormData) => {
    setFormData(data);
    console.log('Form data:', data);
    // Check if required fields are filled
    const isValid = !!(
      data.category &&
      data.introductoryStatement &&
      data.location &&
      data.mainProfession
    );
    setIsFormValid(isValid);
  };

  // Handle "Do this later" button click
  const handleDoThisLater = () => {
    // Navigate to dashboard
    router.push('/feed?fromSignup=true');
  };

  const handleContinue = async () => {
    // Set submitting state
    setIsSubmitting(true);
    setError(null);

    try {
      // Generate a unique identifier for the profile image if none is provided
      const uniqueImageId = Date.now().toString();
      const defaultImageUrl = `default-profile-${uniqueImageId}.jpg`;

      // Post the profile data to the API
      await axiosInstance.post('onboarding/profile-setup/organisation', {
        introductoryStatement: formData.introductoryStatement || '',
        profileImageUrl: formData.profileImageUrl || defaultImageUrl,
        profileImageUrlThumbnail:
          formData.profileImageUrlThumbnail || defaultImageUrl,
        segmentCategoryId: formData.category || '',
        location: formData.location || '',
        // parentOrganisationId: formData.parentOrganisation || null,
        primarySpeciality: formData.mainProfession || 'CARDIAC_SURGEON',
        mapLink: formData.mapLink || '',
      });

      // Show success message and navigate
      showSuccess('Profile saved successfully!');
      setTimeout(() => {
        router.push('/organisation/subscription/free/document-upload');
      }, 1000);
    } catch (err: any) {
      console.error('Error saving profile data:', err);

      // Check for the specific unique constraint error
      if (
        err.response?.data?.message?.includes(
          'unique constraint "users_profile_image_url_unique"'
        )
      ) {
        const errorMsg =
          'This profile image is already in use. Please choose a different image.';
        setError(errorMsg);
        showError(errorMsg);
      } else {
        setError(err.message || 'Failed to save profile data');
        showError(err.message || 'Failed to save profile data');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add custom CSS to fix the layout
  React.useEffect(() => {
    // Create a style element
    const style = document.createElement('style');

    // Add CSS to fix the Introductory Statement position

    //Removing mt, UI breaking. Don't use this type of css
    style.innerHTML = `
      /* Move Introductory Statement below Title */
      .MuiGrid-container > .MuiGrid-root:last-child {
        order: 2 !important;
        margin-top: 0px !important;
        width: 100% !important;
      }

      /* Ensure the Introductory Statement has proper width */
      .MuiGrid-container > .MuiGrid-root:last-child > div {
        width: 100% !important;
      }
    `;

    // Append the style to the document head
    document.head.appendChild(style);

    // Clean up on unmount
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <Container maxWidth="lg">
      <Box mt={'20px'}>
        {!isSmallScreen && (
          <PatientProfileWelcome
            patientName={authState.user?.displayName || ''}
            subtitle={''}
          />
        )}

        <BackButton handleBackButton={() => router.back()} />

        <Subtitle
          text={'Let’s set up your Organisation Account!'}
          sx={{ fontSize: { xs: '12px', sm: '16px' } }}
          marginBottom={'34px'}
        />

        <CustomizedSteppers
          activeStep={0}
          steps={['Profile Setup', 'Document Upload', 'Adding Network']}
        />

        {/* Use the ProfileSetupForm component with isBasicPlan=true */}
        <Box sx={{ mt: { xs: '30px', sm: '40px', md: '50px' } }}>
          <OrganisationProfileSetupForm
            isBasicPlan={true}
            onChange={handleFormChange}
            onSave={handleContinue}
            onSkip={handleDoThisLater}
            isSubmitting={isSubmitting}
          />
          {error && (
            <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
              {error}
            </Box>
          )}
        </Box>
      </Box>
    </Container>
  );
}
