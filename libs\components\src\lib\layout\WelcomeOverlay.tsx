'use client';
import { Box } from '@mui/material';
import PatientWelcomeCard from '../patient/PatientWelcomeCard';

interface Props {
  open: boolean;
  onClose: () => void;
}

export default function WelcomeOverlay({ open, onClose }: Props) {
  if (!open) return null;

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        zIndex: 100,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        px: '20px',
      }}
      onClick={onClose}
    >
      <Box onClick={(e) => e.stopPropagation()}>
        <PatientWelcomeCard onClose={onClose} />
      </Box>
    </Box>
  );
}
