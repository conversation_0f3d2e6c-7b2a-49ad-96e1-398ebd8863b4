import { SubscriptionOption } from '../types/onboarding.types';

/**
 * Subscriptoin options for different user types
 */
export const SUBSCRIPTION_OPTIONS: SubscriptionOption[] = [
  {
    id: '1',
    title: 'Free',
    subtitle: 'Your first step into MiniCardiac',
    description:
      'Check out the platform and connect with your peers, with limited posting ability and no public community access.',
    priceMonthly: 0,
    priceYearly: 0,
    assetUri:
      'https://assets.dev.minicardiac.com/assets/subscription/basic.svg',
    planFeatures: [
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '4847075e-6aa6-43a7-87cd-0701247c9eff',
          key: 'access_to_new_feed',
          name: 'Access to news feed',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '1c7bddfc-7288-4295-bcb8-dbe9a5a5ea21',
          key: 'network_with_professionals_and_organisations',
          name: 'Network with Professionals & Organisations',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: null,
        integerValue: 3,
        textValue: null,
        subscriptionFeature: {
          id: 'ed8a1e19-2738-4aac-886c-badaffdb9ef8',
          key: 'posts_per_month',
          name: 'Posts per month',
          type: 2,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'ae87681a-8130-4878-95c3-38ce413f26ea',
          key: 'post_to_public_and_appear_in_public_search_results',
          name: 'Post to the Public community & appear in Public search results',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'd1868b2f-c412-45f4-a732-ac65c254f76e',
          key: 'access_to_cardiology_and_cardiac_surgery_audiences',
          name: 'Dual-access to Cardiology and Cardiac Surgery Audiences',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'f7b16fb6-74f8-4343-b36a-45a945a6e5ff',
          key: 'discover_and_apply_for_professional_opportunities',
          name: 'Discover and apply for professional opportunities',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '59047d1b-b444-475e-9bb9-46b2aab0d09e',
          key: 'advanced_content_creation_tools_and_analytics',
          name: 'Advanced content creation tools & analytics',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'b574023b-2de6-431f-841a-f7e4f500cf21',
          key: 'team_access_for_account_management',
          name: 'Team access for account management',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'ed11e253-76c1-43bc-a458-116d17ff47dc',
          key: 'priority_listing_in_search_results_and_on_the_news_feed',
          name: 'Priority listing in search results and on the news feed',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '6621d545-29f0-4654-a0a5-09bb166fa58e',
          key: 'business_account_with_the_ability_to_advertise_and_sponsor_content',
          name: 'Business account with the ability to advertise and sponsor content',
          type: 1,
          description: null,
        },
      },
    ],
  },
  {
    id: '2',
    title: 'Primary',
    subtitle: 'Build your professional presence',
    description:
      'Appear in public search results and post to the public community to showcase your work to a wider audience and connect with patients.',
    priceMonthly: 12,
    priceYearly: 120,
    assetUri:
      'https://assets.dev.minicardiac.com/assets/subscription/primary.svg',
    planFeatures: [
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '4847075e-6aa6-43a7-87cd-0701247c9eff',
          key: 'access_to_new_feed',
          name: 'Access to news feed',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '1c7bddfc-7288-4295-bcb8-dbe9a5a5ea21',
          key: 'network_with_professionals_and_organisations',
          name: 'Network with Professionals & Organisations',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: null,
        integerValue: 9,
        textValue: null,
        subscriptionFeature: {
          id: 'ed8a1e19-2738-4aac-886c-badaffdb9ef8',
          key: 'posts_per_month',
          name: 'Posts per month',
          type: 2,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'ae87681a-8130-4878-95c3-38ce413f26ea',
          key: 'post_to_public_and_appear_in_public_search_results',
          name: 'Post to the Public community & appear in Public search results',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'd1868b2f-c412-45f4-a732-ac65c254f76e',
          key: 'access_to_cardiology_and_cardiac_surgery_audiences',
          name: 'Dual-access to Cardiology and Cardiac Surgery Audiences',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'f7b16fb6-74f8-4343-b36a-45a945a6e5ff',
          key: 'discover_and_apply_for_professional_opportunities',
          name: 'Discover and apply for professional opportunities',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: null,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '59047d1b-b444-475e-9bb9-46b2aab0d09e',
          key: 'advanced_content_creation_tools_and_analytics',
          name: 'Advanced content creation tools & analytics',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'b574023b-2de6-431f-841a-f7e4f500cf21',
          key: 'team_access_for_account_management',
          name: 'Team access for account management',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'ed11e253-76c1-43bc-a458-116d17ff47dc',
          key: 'priority_listing_in_search_results_and_on_the_news_feed',
          name: 'Priority listing in search results and on the news feed',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '6621d545-29f0-4654-a0a5-09bb166fa58e',
          key: 'business_account_with_the_ability_to_advertise_and_sponsor_content',
          name: 'Business account with the ability to advertise and sponsor content',
          type: 1,
          description: null,
        },
      },
    ],
  },
  {
    id: '3',
    title: 'Premium',
    subtitle: 'For senior professionals',
    description:
      'Have your team manage your correspondence, and enjoy advanced posting capabilities and analytic insights.',
    priceMonthly: 123,
    priceYearly: 1230,
    assetUri:
      'https://assets.dev.minicardiac.com/assets/subscription/premium.svg',
    planFeatures: [
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '4847075e-6aa6-43a7-87cd-0701247c9eff',
          key: 'access_to_new_feed',
          name: 'Access to news feed',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '1c7bddfc-7288-4295-bcb8-dbe9a5a5ea21',
          key: 'network_with_professionals_and_organisations',
          name: 'Network with Professionals & Organisations',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: null,
        integerValue: 27,
        textValue: null,
        subscriptionFeature: {
          id: 'ed8a1e19-2738-4aac-886c-badaffdb9ef8',
          key: 'posts_per_month',
          name: 'Posts per month',
          type: 2,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'ae87681a-8130-4878-95c3-38ce413f26ea',
          key: 'post_to_public_and_appear_in_public_search_results',
          name: 'Post to the Public community & appear in Public search results',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'd1868b2f-c412-45f4-a732-ac65c254f76e',
          key: 'access_to_cardiology_and_cardiac_surgery_audiences',
          name: 'Dual-access to Cardiology and Cardiac Surgery Audiences',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'f7b16fb6-74f8-4343-b36a-45a945a6e5ff',
          key: 'discover_and_apply_for_professional_opportunities',
          name: 'Discover and apply for professional opportunities',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '59047d1b-b444-475e-9bb9-46b2aab0d09e',
          key: 'advanced_content_creation_tools_and_analytics',
          name: 'Advanced content creation tools & analytics',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'b574023b-2de6-431f-841a-f7e4f500cf21',
          key: 'team_access_for_account_management',
          name: 'Team access for account management',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'ed11e253-76c1-43bc-a458-116d17ff47dc',
          key: 'priority_listing_in_search_results_and_on_the_news_feed',
          name: 'Priority listing in search results and on the news feed',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: false,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '6621d545-29f0-4654-a0a5-09bb166fa58e',
          key: 'business_account_with_the_ability_to_advertise_and_sponsor_content',
          name: 'Business account with the ability to advertise and sponsor content',
          type: 1,
          description: null,
        },
      },
    ],
  },
  {
    id: '4',
    title: 'Prestige',
    subtitle: 'The ultimate business account',
    description:
      'Enjoy all premium features, as well as priority listing, unlimited posting, and the ability to advertise and sponsor content.',
    priceMonthly: 1234,
    priceYearly: 12340,
    assetUri:
      'https://assets.dev.minicardiac.com/assets/subscription/prestige.svg',
    planFeatures: [
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '4847075e-6aa6-43a7-87cd-0701247c9eff',
          key: 'access_to_new_feed',
          name: 'Access to news feed',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '1c7bddfc-7288-4295-bcb8-dbe9a5a5ea21',
          key: 'network_with_professionals_and_organisations',
          name: 'Network with Professionals & Organisations',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: null,
        integerValue: 1000,
        textValue: null,
        subscriptionFeature: {
          id: 'ed8a1e19-2738-4aac-886c-badaffdb9ef8',
          key: 'posts_per_month',
          name: 'Posts per month',
          type: 2,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'ae87681a-8130-4878-95c3-38ce413f26ea',
          key: 'post_to_public_and_appear_in_public_search_results',
          name: 'Post to the Public community & appear in Public search results',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'd1868b2f-c412-45f4-a732-ac65c254f76e',
          key: 'access_to_cardiology_and_cardiac_surgery_audiences',
          name: 'Dual-access to Cardiology and Cardiac Surgery Audiences',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'f7b16fb6-74f8-4343-b36a-45a945a6e5ff',
          key: 'discover_and_apply_for_professional_opportunities',
          name: 'Discover and apply for professional opportunities',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '59047d1b-b444-475e-9bb9-46b2aab0d09e',
          key: 'advanced_content_creation_tools_and_analytics',
          name: 'Advanced content creation tools & analytics',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'b574023b-2de6-431f-841a-f7e4f500cf21',
          key: 'team_access_for_account_management',
          name: 'Team access for account management',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: 'ed11e253-76c1-43bc-a458-116d17ff47dc',
          key: 'priority_listing_in_search_results_and_on_the_news_feed',
          name: 'Priority listing in search results and on the news feed',
          type: 1,
          description: null,
        },
      },
      {
        booleanValue: true,
        integerValue: null,
        textValue: null,
        subscriptionFeature: {
          id: '6621d545-29f0-4654-a0a5-09bb166fa58e',
          key: 'business_account_with_the_ability_to_advertise_and_sponsor_content',
          name: 'Business account with the ability to advertise and sponsor content',
          type: 1,
          description: null,
        },
      },
    ],
  },
];

export const subscriptionFeatures = [
  {
    key: 'access_to_new_feed',
    label: 'Access to news feed',
  },
  {
    key: 'network_with_professionals_and_organisations',
    label: 'Network with Professionals & Organisations',
  },
  {
    key: 'posts_per_month',
    label: 'Posts per month',
  },
  {
    key: 'post_to_public_and_appear_in_public_search_results',
    label: 'Post to Public community & appear in Public search results',
  },
  {
    key: 'access_to_cardiology_and_cardiac_surgery_audiences',
    label: 'Access to Cardiology and Cardiac Surgery Audiences',
  },
  {
    key: 'discover_and_apply_for_professional_opportunities',
    label: 'Discover and apply for professional opportunities',
  },
  {
    key: 'advanced_content_creation_tools_and_analytics',
    label: 'Advanced content creation tools & analytics',
  },
  {
    key: 'team_access_for_account_management',
    label: 'Team access for account management',
  },
  {
    key: 'priority_listing_in_search_results_and_on_the_news_feed',
    label: 'Priority listing in search results and on the news feed',
  },
  {
    key: 'business_account_with_the_ability_to_advertise_and_sponsor_content',
    label: 'Business account with the ability to advertise and sponsor content',
  },
];

export const DOCUMENT_REQUIREMENTS = [
  {
    id: 'ece24f1c-59b0-4e13-aefd-f017ddea13a5',
    isRequired: true,
    sortOrder: 1,
    maxCount: 1,
    instructions:
      "Upload a valid government-issued photo ID (passport, driver's license, etc.)",
    documentType: {
      id: '9ad6fb7e-45e9-4a0a-a775-7384e6edee09',
      name: 'Verification Photo ID',
    },
  },
  {
    id: '1cf60153-2ddc-4ffc-a635-309a79c7e8a1',
    isRequired: true,
    sortOrder: 2,
    maxCount: 1,
    instructions: 'Upload your primary medical degree certificate',
    documentType: {
      id: '477daf01-515a-465d-bcc3-a48b5ce50134',
      name: 'Primary Degree',
    },
  },
  {
    id: '5fbe1e5f-885d-47c5-b71f-337db1629678',
    isRequired: false,
    sortOrder: 3,
    maxCount: 3,
    instructions: 'Upload any additional academic qualifications (optional)',
    documentType: {
      id: 'ac3d8e9e-5532-40c2-ad57-b59bb5450a85',
      name: 'Academic Degree',
    },
  },
  {
    id: 'cb0bba48-ecdc-4ee1-bd18-7e197794db20',
    isRequired: true,
    sortOrder: 4,
    maxCount: 3,
    instructions: 'Upload your professional certifications or licenses',
    documentType: {
      id: 'ab6f53e7-7c50-4406-8cd1-fa5037d79c54',
      name: 'Professional Certification',
    },
  },
  {
    id: 'cb0bba48-ecdc-4ee1-bd18-7e197794db21',
    isRequired: true,
    sortOrder: 4,
    maxCount: 3,
    instructions: 'Upload your professional certifications or licenses',
    documentType: {
      id: 'ab6f53e7-7c50-4406-8cd1-fa5037d79c55',
      name: 'Professional Registration',
    },
  },
];
