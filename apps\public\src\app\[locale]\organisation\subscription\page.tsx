'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Box, Button, Container } from '@mui/material';

import {
  BackButton,
  PatientProfileWelcome,
  PrestigeSubscription,
  SubscriptionList,
  Subtitle,
} from '@minicardiac-client/components';
import SubscriptionTable from '@/libs/components/src/lib/onboarding/components/SubscriptionTable';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAuth } from '@minicardiac-client/apis';

export default function SubscriptionPage() {
  const router = useRouter();
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>(
    'monthly'
  );
  const [isShowingTable, setIsShowingTable] = useState(false);
  const [isCardExpanded, setIsCardExpanded] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const { authState } = useAuth();

  // Create a client
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 5 * 60 * 1000, // 5 minutes
            refetchOnWindowFocus: false,

            // retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
          },
        },
      })
  );
  const searchParams = useSearchParams();
  const professionalType = searchParams?.get('type');

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <Container
      sx={{
        maxWidth: '1192px !important',
        mx: 'auto',
        pl: '0 !important',
        pr: '0 !important',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {isClient ? (
        <QueryClientProvider client={queryClient}>
          <React.Fragment>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                width: '100%',

                pb: { xs: 2, sm: 0 },
                px: { xs: 2, sm: 0 },
              }}
            >
              <BackButton handleBackButton={() => router.back()} />

              <PatientProfileWelcome
                patientName={authState.user?.displayName || ''}
              />

              <Subtitle
                text="For access to the full MiniCardiac experience, pick a subscription plan that works best for you"
                sx={{
                  fontWeight: 400,
                  color: '#737678',
                  mt: { xs: '24px', smd: '8px' },
                  fontSize: { xs: '12px', smd: '16px' },
                }}
              />

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  width: '100%',
                  mt: '20px',
                  px: '16px',
                }}
              >
                <Box
                  sx={(theme) => ({
                    display: 'flex',
                    border: '0.5px solid',
                    borderColor: theme.palette.secondary.main,
                    borderRadius: '8px',
                    height: 44,
                    padding: '4px 5px',
                    gap: '4px',
                    backgroundColor: '#FFFFFF',
                    overflow: 'hidden',
                  })}
                >
                  <Button
                    onClick={() => setBillingCycle('monthly')}
                    sx={(theme) => ({
                      width: 68,
                      height: 36,
                      minWidth: 68,
                      borderRadius: '8px',
                      backgroundColor:
                        billingCycle === 'monthly'
                          ? '#E3C6DFBF'
                          : 'transparent',
                      color:
                        billingCycle === 'monthly'
                          ? theme.palette.secondary.main
                          : '#1E1E1E',
                      fontFamily: "'Plus Jakarta Sans', sans-serif",
                      fontWeight: billingCycle === 'monthly' ? 600 : 300,
                      fontSize: '14px',
                      textTransform: 'none',
                      padding: '0 6px',
                      '&:hover': {
                        backgroundColor:
                          billingCycle === 'monthly'
                            ? '#E3C6DFBF'
                            : 'rgba(0, 0, 0, 0.04)',
                      },
                    })}
                  >
                    Monthly
                  </Button>
                  <Button
                    onClick={() => setBillingCycle('yearly')}
                    sx={(theme) => ({
                      width: 68,
                      height: 36,
                      minWidth: 68,
                      borderRadius: '8px',
                      backgroundColor:
                        billingCycle === 'yearly' ? '#E3C6DFBF' : 'transparent',
                      color:
                        billingCycle === 'yearly'
                          ? theme.palette.secondary.main
                          : '#1E1E1E',
                      fontFamily: "'Plus Jakarta Sans', sans-serif",
                      fontWeight: billingCycle === 'yearly' ? 600 : 300,
                      fontSize: '14px',
                      textTransform: 'none',
                      padding: '0 6px',
                      '&:hover': {
                        backgroundColor:
                          billingCycle === 'yearly'
                            ? '#E3C6DFBF'
                            : 'rgba(0, 0, 0, 0.04)',
                      },
                    })}
                  >
                    Yearly
                  </Button>
                </Box>
                {/* 
                <Box sx={{ position: 'relative' }}>
                  <Typography
                    variant="body2"
                    sx={{
                      position: 'absolute',
                      top: -10,
                      right: 80,
                      fontFamily: "'Plus Jakarta Sans', sans-serif",
                      fontSize: '12px',
                      fontWeight: 400,
                      color: '#6F6F6F',
                      lineHeight: 1,
                    }}
                  >
                    Currency
                  </Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      width: 136,
                      height: 45,
                      border: '1px solid #A3A3A3',
                      borderRadius: 0,
                      padding: '0 16px',
                      gap: '12px',
                      backgroundColor: '#FFFFFF',
                      cursor: 'pointer',
                    }}
                  >
                    <Box
                      component="img"
                      src="/assets/flags/united-states-of-america-1.svg"
                      alt="US Flag"
                      sx={{ width: 32, height: 24 }}
                    />
                    <Typography
                      sx={{
                        fontFamily: "'Plus Jakarta Sans', sans-serif",
                        fontWeight: 400,
                        fontSize: '16px',
                        lineHeight: '22px',
                        color: '#1E1E1E',
                      }}
                    >
                      $ - USD
                    </Typography>
                  </Box>
                </Box> */}
              </Box>
            </Box>

            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                width: '100%',
                mb: 4,
                position: 'relative',
              }}
            >
              {/* SubscriptionList component */}
              <Box sx={{ width: '100%' }}>
                {isShowingTable ? (
                  <SubscriptionTable
                    userSegment={'ORGANISATION'}
                    billingCycle={billingCycle}
                    professionalType={professionalType}
                  />
                ) : (
                  <SubscriptionList
                    userSegment="ORGANISATION"
                    onExpandChange={(expanded) => setIsCardExpanded(expanded)}
                    billingCycle={billingCycle}
                    professionalType={professionalType}
                    onClickSwitchToTableButton={() => setIsShowingTable(true)}
                  />
                )}
              </Box>

              {/* Prestige subscription section - only show when cards are not expanded */}
              {!isCardExpanded && !isShowingTable && <PrestigeSubscription />}

              {/* View Plan button width override and positioning */}
              <style jsx global>{`
                .embla + .MuiButton-root.MuiButton-contained {
                  width: 156px !important;
                  position: relative !important;
                  z-index: 5 !important;
                }
              `}</style>

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  width: '100%',
                  maxWidth: '1200px',
                  mt: 2,
                  px: 3,
                  position: 'relative',
                }}
              ></Box>
            </Box>
          </React.Fragment>
        </QueryClientProvider>
      ) : (
        <div>Loading...</div>
      )}
    </Container>
  );
}
