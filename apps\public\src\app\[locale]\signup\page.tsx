'use client';

import { SignInPage } from '@minicardiac-client/components';
import { useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from '../../../i18n/navigation';

export default function SignUpPage() {
  const router = useRouter();
  const t = useTranslations('signup');

  useEffect(() => {
    document.title = 'Sign Up | MiniCardiac';
  }, []);

  const handleNavigate = (path: string) => {
    if (path === '/signin') {
      router.push('/signin');
    } else if (path === '/signup') {
      // Already on signup page
      return;
    } else {
      router.push(path);
    }
  };

  return (
    <SignInPage
      initialActiveTab={1}
      onNavigate={handleNavigate}
      onSignIn={() => {
        // No-op: Handled by the sign-in page
      }}
      onForgotPassword={() => {
        // No-op: Handled by the sign-in page
      }}
      translations={{
        title: t('title'),
        subtitle: t('subtitle'),
        emailLabel: t('emailLabel'),
        passwordLabel: t('passwordLabel'),
        forgotPassword: t('forgotPassword'),
        continueLabel: t('continue'),
        orLabel: t('or'),
        googleLabel: t('google'),
        appleLabel: t('apple'),
        signIn: t('signIn'),
        signUp: t('signUp'),
      }}
    />
  );
}
