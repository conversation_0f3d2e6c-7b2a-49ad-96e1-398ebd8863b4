import { useEffect, useState } from 'react';

const BackgroundShapeIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
  hovered = false,
}) => {
  const currentFill = hovered ? hoverFill : fill;
  const gradientId = `gradient-${hovered ? 'hover' : 'default'}`;

  const [svgSize, setSvgSize] = useState({ width: 148, height: 180 });

  useEffect(() => {
    const updateSize = () => {
      if (window.innerWidth >= 1924) {
        setSvgSize({ width: 200, height: 240 });
      } else {
        setSvgSize({ width: 148, height: 180 });
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  return (
    <svg
      width={svgSize.width}
      height={svgSize.height}
      viewBox="0 0 148 180"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        opacity="0.15"
        d="M6.42691 129.76C-2.09314 119.606 -0.768725 104.468 9.38507 95.9481L143.481 -16.5721C153.635 -25.0921 168.773 -23.7677 177.293 -13.6139L289.814 120.482C298.334 130.636 297.009 145.774 286.855 154.294L152.759 266.815C142.605 275.335 127.467 274.01 118.947 263.856L6.42691 129.76Z"
        fill={`url(#${gradientId})`}
      />
      <defs>
        <linearGradient
          id={gradientId}
          x1="64.376"
          y1="51.6453"
          x2="201.669"
          y2="188.939"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={currentFill} />
          <stop offset="1" stopColor={currentFill} stopOpacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default BackgroundShapeIcon;
