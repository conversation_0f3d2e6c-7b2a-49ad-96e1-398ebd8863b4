import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ProfileData } from './networking-hooks.js';

/**
 * Interface for the networking state store
 */
interface NetworkingState {
  // UI state
  connectionRequests: Record<string, boolean>;
  followedProfiles: Record<string, boolean>;
  
  // Actions
  setConnectionRequest: (profileId: string, status: boolean) => void;
  setFollowedProfile: (profileId: string, status: boolean) => void;
  
  // Helper methods to enhance profiles with UI state
  enhanceProfiles: (profiles: ProfileData[]) => ProfileData[];
  
  // Reset state (useful for logout)
  resetState: () => void;
}

/**
 * Zustand store for networking UI state
 * Uses persist middleware to save state to localStorage
 */
export const useNetworkingStore = create<NetworkingState>()(
  persist(
    (set, get) => ({
      // Initial state
      connectionRequests: {},
      followedProfiles: {},
      
      // Set connection request status for a profile
      setConnectionRequest: (profileId: string, status: boolean) => 
        set((state) => ({
          connectionRequests: {
            ...state.connectionRequests,
            [profileId]: status
          }
        })),
      
      // Set followed status for a profile  
      setFollowedProfile: (profileId: string, status: boolean) => 
        set((state) => ({
          followedProfiles: {
            ...state.followedProfiles,
            [profileId]: status
          }
        })),
      
      // Enhance profiles with UI state from the store
      enhanceProfiles: (profiles: ProfileData[]) => {
        const { connectionRequests, followedProfiles } = get();
        return profiles.map(profile => ({
          ...profile,
          connectionRequestSent: connectionRequests[profile.id] || profile.connectionRequestSent,
          isFollowing: followedProfiles[profile.id] || profile.isFollowing
        }));
      },
      
      // Reset state (useful for logout)
      resetState: () => set({ connectionRequests: {}, followedProfiles: {} })
    }),
    {
      name: 'networking-storage', // localStorage key
      // You can customize storage here if needed
    }
  )
);

export default useNetworkingStore;
