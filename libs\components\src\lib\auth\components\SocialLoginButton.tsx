import { SocialLoginButtonProps } from '../types/auth.types';
import { SOCIAL_PROVIDERS } from '../constants/auth.constants';
import { ExtendedTheme } from '../types/auth.types';
import Image from 'next/image';
import { useState } from 'react';
import { LoadingButton } from '../../loading-button/loading-button';
import { useMediaQuery, useTheme } from '@mui/material';

export const SocialLoginButton = ({
  provider,
  onClick,
  disabled = false,
  label,
}: SocialLoginButtonProps) => {
  const [imageError, setImageError] = useState(false);
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const providerData =
    provider === 'google' ? SOCIAL_PROVIDERS.GOOGLE : SOCIAL_PROVIDERS.APPLE;

  return (
    <LoadingButton
      variant="outlined"
      fullWidth={!isSmallScreen} // Make square on small screen
      startIcon={
        !imageError && (
          <Image
            src={providerData.icon}
            alt={providerData.name}
            width={20}
            height={20}
            onError={() => setImageError(true)}
          />
        )
      }
      onClick={onClick}
      sx={{
        height: {
          xs: '56px',
          sm: (theme as ExtendedTheme).customValues.button?.height,
        },
        width: {
          xs: '56px',
          sm: '100%',
        },
        border: '1px solid #A24295',
        color: theme.palette.secondary.main,
        fontFamily: "'Plus Jakarta Sans', sans-serif",
        backgroundColor: 'white',
        fontWeight: (theme as ExtendedTheme).customValues.typography?.button
          ?.medium?.fontWeight,
        fontSize: {
          xs: '0px', // Hide text on small screen
          sm: (theme as ExtendedTheme).customValues.typography?.button?.medium
            ?.fontSize,
          xxl: '18px',
        },
        lineHeight: {
          xs: '0px',
          sm: (theme as ExtendedTheme).customValues.typography?.button?.medium
            ?.lineHeight,
          xxl: '28px',
        },
        letterSpacing: {
          xs: '0px',
          sm: (theme as ExtendedTheme).customValues.typography?.button?.medium
            ?.letterSpacing,
          xxl: '0.5px',
        },
        px: {
          xs: 0,
          sm: 2,
          xxl: 4,
        },
        py: {
          xs: 0,
          sm: 1,
          xxl: 2,
        },
        borderRadius: '8px',
        minWidth: 'unset',
        ...(isSmallScreen ? { minWidth: '56px' } : {}),
        '&:hover': {
          backgroundColor: '#A242951F', // 12% opacity of primary purple
          borderColor: theme.palette.secondary.main,
        },
        '&.Mui-disabled': {
          backgroundColor: 'transparent',
          color: theme.palette.secondary.main,
          border: '1px solid #A24295',
          opacity: 0.5,
        },
      }}
    >
      {label || (!isSmallScreen && providerData.name)}
    </LoadingButton>
  );
};

export default SocialLoginButton;
