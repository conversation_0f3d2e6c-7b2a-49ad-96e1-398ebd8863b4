const BookmarkIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M25 7V27.4C25 27.6427 24.8542 27.8614 24.6298 27.9544C24.5554 27.985 24.4774 28 24.4 28C24.244 28 24.0907 27.9391 23.9758 27.8242L16 19.8484L8.0242 27.8242C7.8526 27.9958 7.5952 28.0468 7.3702 27.9544C7.1458 27.8614 7 27.6427 7 27.4V7C7 5.3458 8.3458 4 10 4H22C23.6542 4 25 5.3458 25 7Z"
        fill="url(#paint0_linear_2909_71353)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2909_71353"
          x1="16"
          y1="4"
          x2="16"
          y2="28"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F92243" />
          <stop offset="1" stopColor="#A24295" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default BookmarkIcon;
