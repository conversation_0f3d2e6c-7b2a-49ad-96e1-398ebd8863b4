import { TextField as <PERSON><PERSON><PERSON>ext<PERSON><PERSON>, TextFieldProps as MuiTextFieldProps } from '@mui/material';
import { forwardRef } from 'react';

export type TextFieldProps = MuiTextFieldProps;

export const TextField = forwardRef<HTMLDivElement, TextFieldProps>(
  ({ variant = 'outlined', fullWidth = true, ...props }, ref) => {
    return (
      <MuiTextField
        ref={ref}
        variant={variant}
        fullWidth={fullWidth}
        {...props}
        InputLabelProps={{
          shrink: true,
          sx: {
            fontSize: '20px',
            fontWeight: 600,
            fontFamily: 'Plus Jakarta Sans, sans-serif',
          },
        }}
        inputProps={{
          style: {
            height: '45px',
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 400,
            fontSize: '16px',
            letterSpacing: '0%',
          },
        }}
        sx={{
          '& .MuiOutlinedInput-root': {
            borderRadius: '8px',
            '& fieldset': {
              borderColor: '#9CA3AF',
              borderWidth: '1.5px',
            },
            '&:hover fieldset': {
              borderColor: '#9CA3AF',
              borderWidth: '1.5px',
            },
            '&.Mui-focused fieldset': {
              borderColor: '#9CA3AF',
              borderWidth: '1.5px',
            },
            '& input': {
              padding: '12px 16px',
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 400,
              fontSize: '16px',
              letterSpacing: '0%',
              '&::placeholder': {
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 400,
                fontSize: '16px',
                letterSpacing: '0%',
                color: '#A3A3A3',
                opacity: 1,
              },
            },
          },
          '& .MuiFormHelperText-root': {
            fontFamily: 'Plus Jakarta Sans, sans-serif',
          },
          '& .MuiInputLabel-root.Mui-error': {
            color: '#FF5C5C',
          },
          ...props.sx,
        }}
      />
    );
  }
);

TextField.displayName = 'TextField';
export default TextField;