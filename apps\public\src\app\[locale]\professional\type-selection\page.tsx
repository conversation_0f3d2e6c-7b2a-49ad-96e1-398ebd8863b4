'use client';

import React, { useState, useEffect } from 'react';
import { useAuth, useAuthStore } from '@minicardiac-client/apis';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';

import { useRouter } from '@/apps/public/src/i18n/navigation';
import {
  AlliedCardiacSelection,
  FullPageLoader,
  Iconify,
  LoadingButton,
  PatientProfileWelcome,
  ProfessionalSignupOptionCard,
  StudentSignupModal,
} from '@minicardiac-client/components';
import { getCdnUrl } from '@minicardiac-client/utilities';

interface ProfessionalType {
  title: string;
  key: string;
  description: string;
  icon: string;
  character: string;
  characterFemale?: string;
  path: string;
}

const PROFESSIONAL_TYPES: ProfessionalType[] = [
  {
    title: 'Cardiac Surgeon',
    key: 'CARDIAC_SURGEON',
    description: '',
    icon: '',
    character:
      'https://assets.dev.minicardiac.com/assets/gray-coat-surgeon.svg',
    path: '/professional/cardiac-surgeon',
  },
  {
    title: 'Cardiologist',
    key: 'CARDIOLOGIST',
    description: '',
    icon: '',
    character:
      'https://assets.dev.minicardiac.com/assets/white-coat-doctor.svg',
    path: '/professional/cardiologist',
  },
  {
    title: 'Allied Cardiac',
    description:
      'This is the category for all other professions who make up the wider cardiac healthcare ecosystem: medical specialists, general practitioners (GPs), perfusionists, nurses, surgical practitioners, researchers, administrators, and more. This is the category for you!',
    icon: '',
    character: 'https://assets.dev.minicardiac.com/assets/3.svg',
    key: 'ALLIED_CARDIAC',
    characterFemale: 'https://assets.dev.minicardiac.com/assets/2.svg',
    path: '/professional/allied-cardiac',
  },
];

export default function ProfessionalTypeSelectionPage() {
  const router = useRouter();
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [studentModalOpen, setStudentModalOpen] = useState(false);
  const { authState } = useAuth();
  const [isLoading, setIsloading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(false);

  const { setProcessing, setSelectedProfessionalType, startNavigation } =
    useAuthStore();

  useEffect(() => {
    setIsClient(true);
    // Reset processing state when component mounts
    setProcessing(false);
  }, [setProcessing]);

  const handleTypeSelect = (path: string) => {
    const type = PROFESSIONAL_TYPES.find((t) => t.path === path)?.key || null;
    setSelectedType(type);
    setSelectedProfessionalType(type);
  };

  const handleProceed = () => {
    setIsloading(true);
    if (selectedType) {
      // Set processing state before navigation
      startNavigation({ isProcessing: true });
      setIsloading(false);
      router.push('/professional/subscription?type=' + selectedType);
    }
    setIsloading(false);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        minHeight: '100vh',
        width: '100%',
        px: 3,
        maxWidth: '800px',
        mx: 'auto',
      }}
    >
      {isClient ? (
        <>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: { xs: '24px', sm: '40px' },
              mb: { xs: '24px', sm: '40px' },
            }}
          >
            <PatientProfileWelcome
              patientName={authState.user?.displayName || ''}
              subtitle={"Let's set up your Professional Account!"}
            />

            <Typography
              sx={(theme) => ({
                fontWeight: 400,
                fontSize: { xs: '12px', md: '16px', xxl: '18px' },
                lineHeight: '100%',
                letterSpacing: '0%',
                textAlign: 'center',
                color: (theme.palette as any).neutral?.[600] || '#737678',
                whiteSpace: 'wrap',
              })}
            >
              First things first, pick the category that best describes your
              professional role in the cardiac world:
            </Typography>
          </Box>

          <Box sx={{ width: '100%', maxWidth: '700px' }}>
            {/* First row - Cardiac Surgeon and Cardiologist side by side */}
            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              gap={{ xs: '16px', sm: '24px' }}
              sx={{ mb: 2, justifyContent: 'center', alignItems: 'center' }}
            >
              {PROFESSIONAL_TYPES.slice(0, 2).map((type, index) => {
                const isSelected = selectedType === type.key;
                return (
                  <ProfessionalSignupOptionCard
                    key={index}
                    option={{
                      title: type.title,
                      description: type.description,
                      icon: type.icon,
                      character: type.character,
                      path: type.path,
                      shape: 'rectangle',
                    }}
                    onClick={() => handleTypeSelect(type.path)}
                    variant="professional"
                    selected={isSelected}
                    additionalImage={
                      type.title === 'Cardiac Surgeon'
                        ? getCdnUrl('/assets/op-light.svg')
                        : undefined
                    }
                    titleTypographyProps={{
                      sx: {
                        fontFamily: "'Plus Jakarta Sans', sans-serif",
                        fontWeight: 400,
                        fontSize: '20px',
                        lineHeight: '36px',
                        letterSpacing: '0px',
                      },
                    }}
                  />
                );
              })}
            </Stack>

            {/* Second row - Allied Cardiac below */}
            <AlliedCardiacSelection
              selectedType={selectedType}
              handleTypeSelect={handleTypeSelect}
            />
          </Box>

          <Box
            sx={{
              position: { xs: 'fixed', sm: 'static' },
              bottom: { xs: 0, sm: 'auto' },
              left: 0,
              right: 0,
              width: '100%',
              height: '80px',
              backgroundColor: { xs: '#fff', sm: 'transparent' },
              py: { xs: '20px', sm: '40px' },
              // pt:{ xs: '20px', sm: '40px' },
              // pb:{ xs: '20px', sm: '0px' }
              px: { xs: 3, sm: 0 },
              boxShadow: { xs: '0 -4px 20px rgba(0, 0, 0, 0.05)', sm: 'none' },
              zIndex: 1000,
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <LoadingButton
              variant="contained"
              onClick={handleProceed}
              loading={isLoading}
              sx={(theme: any) => ({
                width: theme.customValues.proceedButton?.width,
                height: theme.customValues.proceedButton?.height,
                borderRadius: theme.customValues.proceedButton?.borderRadius,
                backgroundColor:
                  selectedType && (theme.palette as any).secondary.main,
                '&:hover': {
                  backgroundColor:
                    selectedType && (theme.palette as any).secondary.dark,
                },
                color: 'white',
                fontWeight: 500,
                fontSize: '14px',
                textTransform: 'none',
                boxShadow: 'none',
              })}
              disabled={!selectedType}
            >
              Proceed
            </LoadingButton>
          </Box>

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              mt: { xs: '20px', sm: '24px' },
              textAlign: 'center',
              cursor: 'pointer',
              pb: '10px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              mb: '100px',
            }}
            onClick={() => setStudentModalOpen(true)}
          >
            <Typography
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 600,
                fontSize: '16px',
                color: '#A24295',
                lineHeight: '24px',
                wordBreak: 'break-word',
              }}
            >
              Alternatively, sign up for a student account
            </Typography>
            <Iconify
              icon="material-symbols:arrow-right-alt"
              sx={{
                color: '#A24295',
                mt: '4px',
              }}
            />
          </Box>

          {/* Student Signup Modal */}
          <StudentSignupModal
            open={studentModalOpen}
            onClose={() => !isPageLoading && setStudentModalOpen(false)}
            onContinue={(_) => {
              setIsPageLoading(true);
              setStudentModalOpen(false);
              // After creating the subscription, redirect to the student profile setup page
              router.push('/student/profile-setup');
            }}
          />
          <FullPageLoader open={isPageLoading} />
        </>
      ) : (
        <div>Loading...</div>
      )}
    </Box>
  );
}
