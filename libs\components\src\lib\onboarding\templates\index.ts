// Shared onboarding templates - exact CSS copied from existing components
export { default as DocumentUploadPageTemplate } from './DocumentUploadPageTemplate';
export { default as NetworkingPageTemplate } from './NetworkingPageTemplate';
export { default as FormContainerTemplate } from './FormContainerTemplate';
export { default as ActionButtonsTemplate } from './ActionButtonsTemplate';

// Re-export types
export type { DocumentUploadPageTemplateProps } from './DocumentUploadPageTemplate';
export type { NetworkingPageTemplateProps } from './NetworkingPageTemplate';
export type { FormContainerTemplateProps } from './FormContainerTemplate';
export type { ActionButtonsTemplateProps } from './ActionButtonsTemplate';
