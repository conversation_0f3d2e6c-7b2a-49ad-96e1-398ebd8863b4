import { Box, IconButton } from '@mui/material';
import Image from 'next/image';
import { BOOKMARK, FILTER_ICON, SEARCH_ICON, TAGS_ICON } from '../auth';

export const MobileSearchbar = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        px: '16px',
        width: '100%',
        backgroundColor: 'transparent',
        marginTop: '16px',
        marginBottom: '16px',
      }}
    >
      {/* Left Search Button */}
      <Box
        sx={{
          width: '64px',
          height: '45px',
          backgroundColor: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '8px',
        }}
      >
        <Image src={SEARCH_ICON} alt="Search" width={32} height={32} />
      </Box>

      {/* Right Icons */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: '20px',
        }}
      >
        <IconButton
          sx={{
            width: 40,
            height: 40,
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: 0,
          }}
        >
          <Image src={FILTER_ICON} alt="Search" width={24} height={24} />
        </IconButton>
        <IconButton
          sx={{
            width: 40,
            height: 40,
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: 0,
          }}
        >
          <Image src={BOOKMARK} alt="Search" width={24} height={24} />
        </IconButton>
        <IconButton
          sx={{
            width: 40,
            height: 40,
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: 0,
          }}
        >
          <Image src={TAGS_ICON} alt="Search" width={24} height={24} />
        </IconButton>
      </Box>
    </Box>
  );
};
