import { Snackbar, Alert } from '@mui/material';
import { useState } from 'react';

const useCreateToast = () => {
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [severity, setSeverity] = useState<
    'success' | 'error' | 'info' | 'warning'
  >('success');

  const showToast = (msg: string, type: typeof severity) => {
    setMessage(msg);
    setSeverity(type);
    setOpen(true);
  };

  const Toast = (
    <Snackbar
      open={open}
      autoHideDuration={3000}
      onClose={() => setOpen(false)}
    >
      <Alert severity={severity} onClose={() => setOpen(false)}>
        {message}
      </Alert>
    </Snackbar>
  );

  return { Toast, showToast };
};

export default useCreateToast;
