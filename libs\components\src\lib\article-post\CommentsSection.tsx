import { Box, Typography, Divider, useMediaQuery } from '@mui/material';
import LikeIcon from '../Icons/ContentPostIcons/LikeIcon';
import { Iconify } from '../iconify';
import CommentsList from '../comments/CommentsList';
import { useTheme } from '@emotion/react';

const CommentsSection = ({ comments }: { comments: any[] }) => {
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box mb={8}>
      <Divider sx={{ my: 2, borderColor: '#A3A3A3', opacity: 0.5 }} />

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: '20px',
        }}
      >
        <Box
          display={{ xs: 'none', sm: 'block' }}
          fontWeight={600}
          fontSize={12}
        >
          Comments(1)
        </Box>
        {/* ACTION BUTTONS */}
        <Box
          display="flex"
          justifyContent={isSmallScreen ? 'space-between' : 'flex-end'}
          alignItems="center"
          gap={isSmallScreen ? 0 : '28px'}
          width={'100%'}
        >
          {isSmallScreen ? (
            // Mobile / small screen layout
            <>
              <ActionButton icon={<LikeIcon />} label="(10)" />
              <ActionButton
                icon={
                  <Iconify
                    icon="ic:round-chat-bubble-outline"
                    sx={{ fontSize: 20, color: '#A24295' }}
                  />
                }
                label={`(${comments.length})`}
              />
              <ActionButton
                icon={
                  <Iconify
                    icon="garden:arrow-retweet-stroke-12"
                    sx={{ fontSize: 20, color: '#A24295' }}
                  />
                }
                label="(5)"
              />
              <ActionButton
                icon={
                  <Iconify
                    icon="mdi:share"
                    sx={{ fontSize: 20, color: '#A24295' }}
                  />
                }
                label="(3)"
              />
            </>
          ) : (
            <>
              <ActionButton icon={<LikeIcon />} label="Like (10)" />
              <ActionButton
                icon={
                  <Iconify
                    icon="garden:arrow-retweet-stroke-12"
                    sx={{ fontSize: 20, color: '#A24295' }}
                  />
                }
                label="Repost (5)"
              />
              <ActionButton
                icon={
                  <Iconify
                    icon="mdi:share"
                    sx={{ fontSize: 20, color: '#A24295' }}
                  />
                }
                label="Share (3)"
              />
            </>
          )}
        </Box>
      </Box>
      <CommentsList comments={comments} />
    </Box>
  );
};

const ActionButton = ({
  icon,
  label,
}: {
  icon: React.ReactNode;
  label: string;
}) => (
  <Box
    display="flex"
    alignItems="center"
    gap="4px"
    sx={{ cursor: 'pointer', color: '#A24295' }}
  >
    {icon}
    <Typography fontSize="12px" fontWeight={600}>
      {label}
    </Typography>
  </Box>
);

export default CommentsSection;
