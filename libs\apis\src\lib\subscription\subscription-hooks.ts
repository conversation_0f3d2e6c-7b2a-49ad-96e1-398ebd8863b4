import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { axiosInstance } from '../http-client.js';
import { SubscriptionOption } from '@minicardiac-client/types';
import { auth } from '../firebase/firebase-client.js';

// Define query keys for subscription plans
export const subscriptionQueryKeys = {
  all: ['subscription'] as const,
  plans: (userSegment?: string) => [...subscriptionQueryKeys.all, 'plans', userSegment] as const,
};

// Define subscription request type
export interface CreateSubscriptionRequest {
  subscriptionPlanId: string;
  isYearly: boolean;
  professionalCategory?: string
}

// Define subscription response type
export interface CreateSubscriptionResponse {
  id: string;
  planId: number;
  status: string;
  // Add other fields as needed
}

// API function to fetch subscription plans
export const fetchSubscriptionPlans = async (userSegment: string): Promise<SubscriptionOption[]> => {
  const response = await axiosInstance.get(`/subscription-plans/${userSegment}`);
  return response.data.data || [];
};

// API function to create a subscription
export const createSubscription = async (data: CreateSubscriptionRequest): Promise<CreateSubscriptionResponse> => {
  console.log('Creating subscription with data:', data);
  try {
    const response = await axiosInstance.post('/subscription-plans', data);
    console.log('Create subscription response:', response.data);

    // After successful subscription, refresh the Firebase token to get updated claims
    if (auth?.currentUser) {
      console.log('Refreshing Firebase token to get updated claims...');
      await auth.currentUser.getIdToken(true);
      console.log('Firebase token refreshed successfully');
    }

    return response.data.data || response.data;
  } catch (error: any) {
    console.error('Error creating subscription:', error);

    // Extract and log detailed error information
    if (error.response) {
      console.error('Error response:', {
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers
      });
    }

    throw error;
  }
};

// Hook to get subscription plans
export const useGetSubscriptionPlans = (userSegment = 'CARDIAC_SPECIALIST', options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: subscriptionQueryKeys.plans(userSegment),
    queryFn: () => fetchSubscriptionPlans(userSegment),
    staleTime: Infinity, // Prevent automatic refetching
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: options?.enabled !== false, // Default to true unless explicitly disabled
    retry: 3, // Explicit retry count
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
};

// Hook to create a subscription
export const useCreateSubscription = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createSubscription,
    onSuccess: () => {
      // Invalidate relevant queries after successful subscription
      queryClient.invalidateQueries({ queryKey: subscriptionQueryKeys.all });
    },
  });
};

