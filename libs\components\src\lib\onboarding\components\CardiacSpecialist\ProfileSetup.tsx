import {
  <PERSON>complete,
  Box,
  Button,
  Grid,
  <PERSON>ack,
  <PERSON><PERSON>ield,
  Typography,
  alpha,
} from '@mui/material';
import React, { useState } from 'react';

import IntroductoryStatement from './IntroductoryStatement';
import CustomizedSteppers from '../Stepper/Stepper';
import ProfileUpload from '../ProfileUpload';
import CreateEmployerDialog from './CreateEmployerDialog';

const ProfileSetup = () => {
  const [activeStep] = useState(0);
  const [showCreateEmployerModal, setShowCreateEmployerModal] = useState(false);

  const handleClose = () => setShowCreateEmployerModal(false);

  const isBasicPlan = false;

  return (
    <Box>
      <CustomizedSteppers
        activeStep={activeStep}
        steps={['Profile Setup', 'Document Upload', 'Adding Network']}
      />
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          mt: { xs: '60px', sm: '90px', md: '120px' },
          py: { xs: 3, sm: 5, md: 7 },
          px: { xs: 2, sm: 3, md: 5 },
          borderRadius: 1,
          boxShadow: (theme) =>
            `0px 12px 24px 0px ${alpha(
              (theme.palette as any).neutral[500],
              0.25
            )}`,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: { xs: 'center', md: 'start' },
            justifyContent: 'space-between',
            width: '100%',
            gap: { xs: 3, md: 5 },
          }}
        >
          {/* Profile Picture Upload Section */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: { xs: 'center', md: 'flex-start' },
            }}
          >
            <ProfileUpload />
          </Box>

          {/* Introductory Statement Section */}
          <Box flexGrow={1}>
            <Grid
              container
              columns={{ xs: 4, sm: 8, md: 2 }}
              rowSpacing={{ xs: 3, md: 7 }}
              columnSpacing={{ xs: 3, md: 5 }}
              gridTemplateColumns={2}
              position={'relative'}
            >
              <Grid
                size={{ xs: 12, md: 1 }}
                gap={2}
                alignItems="center"
                display="flex"
                alignContent="center"
                sx={{
                  flexDirection: { xs: 'column', sm: 'row' },
                  justifyContent: { xs: 'center', sm: 'flex-start' },
                  width: '100%',
                }}
              >
                <TextField
                  sx={{
                    width: { xs: '100%', sm: '40%' },
                    mb: { xs: 1, sm: 0 },
                  }}
                  label="Title"
                  placeholder="Dr./Ms."
                  variant="outlined"
                  size="small"
                  slotProps={{
                    inputLabel: {
                      shrink: true,
                    },
                  }}
                />
                <Typography
                  sx={(theme) => ({
                    ...(
                      theme.typography as unknown as {
                        subtitle3: React.CSSProperties;
                      }
                    ).subtitle3,
                    width: { xs: '100%', sm: '60%' },
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                    textAlign: { xs: 'center', sm: 'left' },
                  })}
                >
                  User Name
                </Typography>
              </Grid>
              {!isBasicPlan && (
                <>
                  <Grid
                    size={{ xs: 12, md: 1 }}
                    display="flex"
                    justifyContent={{ xs: 'center', md: 'flex-end' }}
                    sx={{ width: '100%' }}
                  >
                    <TextField
                      sx={{
                        width: { xs: '100%', md: '80%' },
                      }}
                      label="Qualifications"
                      placeholder="PhD, MD"
                      variant="outlined"
                      size="small"
                      slotProps={{
                        inputLabel: {
                          shrink: true,
                        },
                      }}
                    />
                  </Grid>
                  <Grid
                    size={{ xs: 12, md: 1 }}
                    sx={{
                      width: '100%',
                      display: 'flex',
                      justifyContent: { xs: 'center', md: 'flex-start' },
                    }}
                  >
                    <TextField
                      sx={{
                        width: { xs: '100%', md: '80%' },
                      }}
                      label="Job Title"
                      placeholder="Sr. Cardiologist"
                      variant="outlined"
                      size="small"
                      slotProps={{
                        inputLabel: {
                          shrink: true,
                        },
                      }}
                    />
                  </Grid>
                  <Grid
                    size={{ xs: 12, md: 1 }}
                    display="flex"
                    justifyContent={{ xs: 'center', md: 'flex-end' }}
                    alignItems={{ xs: 'center', md: 'end' }}
                    flexDirection="column"
                    sx={{ width: '100%' }}
                  >
                    <Autocomplete
                      disablePortal
                      options={['helo']}
                      sx={{
                        width: { xs: '100%', md: 1 },
                        display: 'flex',
                        justifyContent: { xs: 'center', md: 'flex-end' },
                      }}
                      freeSolo
                      renderInput={(params) => (
                        <TextField
                          sx={{
                            width: { xs: '100%', md: '80%' },
                            '& .MuiOutlinedInput-root': {
                              '& fieldset>legend': {
                                fontSize: '0.65em',
                              },
                            },
                          }}
                          label={
                            <>
                              Employer{' '}
                              <span
                                style={{
                                  fontSize: '0.7rem',
                                  fontWeight: 'normal',
                                  color: '#666',
                                }}
                              >
                                &nbsp;(optional)
                              </span>
                            </>
                          }
                          helperText={
                            <Stack
                              alignItems={'center'}
                              flexDirection={'row'}
                              justifyContent={'space-between'}
                              sx={{
                                flexWrap: { xs: 'wrap', sm: 'nowrap' },
                              }}
                            >
                              <Typography
                                variant={'prestigeSubscriptionTitle' as any}
                                sx={(theme) => ({
                                  color: (theme.palette as any).neutral[900],
                                  fontSize: { xs: '0.75rem', md: 'inherit' },
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap',
                                })}
                              >
                                https://www.organisation.com
                              </Typography>
                              <Button
                                onClick={(e) => e.stopPropagation()}
                                variant="text"
                                disableRipple
                                sx={{
                                  padding: 0,
                                  minWidth: 'fit-content',
                                  height: 'auto',
                                }}
                              >
                                Edit
                              </Button>
                            </Stack>
                          }
                          placeholder="Start typing to search"
                          variant="outlined"
                          slotProps={{
                            inputLabel: {
                              shrink: true,
                            },
                          }}
                          {...params}
                        />
                      )}
                    />
                  </Grid>
                </>
              )}
              <Grid size={2} sx={{ width: '100%' }}>
                <IntroductoryStatement />
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Box>
      <CreateEmployerDialog
        onClose={handleClose}
        showCreateEmployerModal={showCreateEmployerModal}
      />
    </Box>
  );
};

export default ProfileSetup;
