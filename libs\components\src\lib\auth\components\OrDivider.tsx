import { useTheme } from '@emotion/react';
import { Box, Typography, useMediaQuery } from '@mui/material';

interface OrDividerProps {
  text?: string;
}

/**
 * OrDivider component for separating form and social login options
 */
export const OrDivider = ({ text = 'OR' }: OrDividerProps) => {
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        my: { xs: '32px', sm: '40px' },
      }}
    >
      <Typography
        sx={{
          color: 'neutral.500',
          fontWeight: 500,
          fontSize: '16px',
        }}
      >
        {isSmallScreen ? 'OR sign in with' : 'OR'}
      </Typography>
    </Box>
  );
};

export default OrDivider;
