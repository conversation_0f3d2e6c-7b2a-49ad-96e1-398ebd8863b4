import { Box, Stack, TextField, Typography } from '@mui/material';
import { useState, useRef, useEffect } from 'react';
import MediaPreview from './MediaPreview';

interface MediaFileCardBackSideProps {
  file: File;
  altText: string;
  setAltText: (value: string) => void;
  setIsFlipped: (value: boolean) => void;
  handleSave: () => void;
}

export const MediaFileCardBackSide: React.FC<MediaFileCardBackSideProps> = ({
  file,
  altText,
  setAltText,
  setIsFlipped,
  handleSave,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const inputWrapperRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputWrapperRef.current &&
        !inputWrapperRef.current.contains(event.target as Node)
      ) {
        setIsEditing(false);
      }
    };

    if (isEditing) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isEditing]);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  return (
    <Box
      sx={{
        width: '100%',
        height: { xs: '361px', sm: '100%' },
        position: 'absolute',
        borderRadius: '8px',
        backfaceVisibility: 'hidden',
        backgroundColor: 'white',
        transform: 'rotateY(180deg)',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        p: '20px',
        border: '1px solid #A24295',
      }}
    >
      {/* Thumbnail */}
      <Box
        sx={{
          width: { xs: '120px', sm: '56px' },
          height: { xs: '120px', sm: '56px' },
          borderRadius: '8px',
          overflow: 'hidden',
          alignSelf: 'center',
        }}
      >
        <MediaPreview file={file} />
      </Box>

      {/* Alt text area */}
      <Box sx={{ mt: '12px', width: '100%' }} ref={inputWrapperRef}>
        {!isEditing ? (
          <Typography
            onClick={() => setIsEditing(true)}
            sx={{
              fontSize: '12px',
              fontWeight: 400,
              color: '#737678',
              textAlign: 'center',
              cursor: 'pointer',
              minHeight: '91px',
              display: 'flex',
              justifyContent: 'center',

              borderRadius: '4px',
            }}
          >
            Add alt text here
          </Typography>
        ) : (
          <TextField
            fullWidth
            multiline
            rows={4}
            inputRef={inputRef}
            variant="standard"
            value={altText}
            onChange={(e) => setAltText(e.target.value)}
            InputProps={{
              disableUnderline: true,
            }}
            sx={{
              '& .MuiInputBase-root': {
                padding: 0,
              },
              '& .MuiInputBase-inputMultiline': {
                fontSize: '12px',
                fontWeight: 400,
                padding: '10px',
                lineHeight: '1.5',
                resize: 'none',
              },
            }}
          />
        )}
      </Box>

      {/* Action buttons */}
      <Stack
        direction="row"
        spacing={'20px'}
        mt={'8px'}
        justifyContent="center"
      >
        <Typography
          onClick={() => setIsFlipped(false)}
          sx={{
            color: '#A24295',
            fontSize: '16px',
            fontWeight: 600,
            cursor: 'pointer',
            userSelect: 'none',
            width: '64px',
            height: '30px',
            textAlign: 'center',
          }}
        >
          Cancel
        </Typography>
        <Typography
          onClick={handleSave}
          sx={{
            color: '#A24295',
            fontSize: '16px',
            fontWeight: 600,
            cursor: 'pointer',
            userSelect: 'none',
            width: '64px',
            height: '30px',
            textAlign: 'center',
          }}
        >
          Save
        </Typography>
      </Stack>
    </Box>
  );
};
