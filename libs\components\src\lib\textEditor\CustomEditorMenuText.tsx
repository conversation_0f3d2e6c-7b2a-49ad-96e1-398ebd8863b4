import {
  MenuControlsContainer,
  MenuDivider,
  MenuButtonBold,
  MenuButtonItalic,
  MenuButtonUnderline,
  MenuButtonStrikethrough,
  MenuButtonBulletedList,
  <PERSON>uButton<PERSON>rderedList,
  <PERSON>u<PERSON>uttonBlockquote,
} from 'mui-tiptap';

import { MenuButtonLink } from './MenuButtonLink';
import { Editor } from '@tiptap/react';

export function CustomEditorMenuText({ editor }: { editor: Editor }) {
  return (
    <MenuControlsContainer>
      <MenuButtonBold />
      <MenuButtonItalic />
      <MenuButtonUnderline />
      <MenuButtonStrikethrough />
      <MenuDivider />
      <MenuButtonBulletedList />
      <MenuButtonOrderedList />
      <MenuDivider />
      <MenuButtonBlockquote />
      <MenuButtonLink editor={editor} />
    </MenuControlsContainer>
  );
}
