const PollIcon = ({ fill = '#A3A3A3', hoverFill = '#A24295', size = 24 }) => {
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.001 6.56436V28.4307C21.001 29.2965 20.31 29.995 19.4643 29.995H16.5357C15.6852 29.995 14.9989 29.2916 14.9989 28.4307V6.56436C14.9989 5.69856 15.69 5 16.5357 5H19.4643C20.3148 5 21.001 5.69856 21.001 6.56436ZM8.74066 13.0579H5.26615C4.56544 13.0579 4 13.6334 4 14.3467V28.7111C4 29.4244 4.5654 30 5.26615 30H8.74066C9.44138 30 10.0068 29.4244 10.0068 28.7111V14.3467C10.0068 13.6334 9.43655 13.0579 8.74066 13.0579ZM30.5889 8.93539H27.4091C26.6311 8.93539 25.998 9.57983 25.998 10.3718V28.5587C25.998 29.3507 26.6311 29.9951 27.4091 29.9951H30.5889C31.3669 29.9951 32 29.3507 32 28.5587V10.3769C32 9.57995 31.3669 8.93539 30.5889 8.93539Z"
        fill="url(#paint0_linear_1757_106369)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1757_106369"
          x1="18"
          y1="5"
          x2="18"
          y2="30"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F92243" />
          <stop offset="1" stopColor="#A24295" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default PollIcon;
