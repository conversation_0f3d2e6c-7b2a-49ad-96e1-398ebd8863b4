import React from 'react';
import { Theme, SxProps } from '@mui/material/styles';
import Box from '@mui/material/Box';
import TableRow from '@mui/material/TableRow';
import MuiTableHead from '@mui/material/TableHead';
import TableCell from '@mui/material/TableCell';
import TableSortLabel from '@mui/material/TableSortLabel';

const visuallyHidden = {
  border: 0,
  margin: -1,
  padding: 0,
  width: '1px',
  height: '1px',
  overflow: 'hidden',
  position: 'absolute',
  whiteSpace: 'nowrap',
  clip: 'rect(0 0 0 0)',
} as const;

// ----------------------------------------------------------------------

type HeadLabel = {
  field: string;
  title: string;
  minWidth?: number;
  align?: 'left' | 'right' | 'center' | 'inherit' | 'justify';
  width?: number;
};

type Props = {
  order?: 'asc' | 'desc';
  orderBy?: string;
  headLabel: HeadLabel[];
  onSort?: (id: string) => void;
  sx?: SxProps<Theme>;
};

export default function TableHead({
  order,
  orderBy,
  headLabel,
  onSort,
  sx,
}: Props): React.ReactElement {
  return (
    <MuiTableHead
      sx={{
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        background: '#F1E3EF',
        ...sx,
      }}
    >
      <TableRow>
        {headLabel.map((headCell) => (
          <TableCell
            key={headCell.field}
            align={headCell.align || 'left'}
            sortDirection={orderBy === headCell.field ? order : false}
            sx={{
              width: headCell.width,
              minWidth: headCell.minWidth,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              fontWeight: 'bold',
              color: '#A24295',
            }}
          >
            {onSort ? (
              <TableSortLabel
                hideSortIcon
                active={orderBy === headCell.field}
                direction={orderBy === headCell.field ? order : 'asc'}
                onClick={() => onSort(headCell.field)}
                sx={{
                  color: orderBy === headCell.field ? '#871d78' : '#A24295',
                  '&.Mui-active': {
                    color: '#871d78',
                  },
                  '&:hover': {
                    color: '#6b165f',
                    '& .MuiTableSortLabel-icon': {
                      color: '#6b165f !important',
                    },
                  },
                  '& .MuiTableSortLabel-icon': {
                    color:
                      orderBy === headCell.field
                        ? '#871d78 !important'
                        : '#A24295 !important',
                  },
                }}
              >
                {headCell.title}

                {orderBy === headCell.field ? (
                  <Box sx={{ ...visuallyHidden }}>
                    {order === 'desc'
                      ? 'sorted descending'
                      : 'sorted ascending'}
                  </Box>
                ) : null}
              </TableSortLabel>
            ) : (
              headCell.title
            )}
          </TableCell>
        ))}
      </TableRow>
    </MuiTableHead>
  );
}
