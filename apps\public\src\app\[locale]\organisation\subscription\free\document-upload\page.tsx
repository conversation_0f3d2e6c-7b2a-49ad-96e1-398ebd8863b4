'use client';

import React from 'react';
import {
  Box,
} from '@mui/material';
import {
  useSnackbar,
  DocumentUploadPageTemplate,
  ActionButtonsTemplate,
} from '@minicardiac-client/components';
import DocumentUploadForm from '@/libs/components/src/lib/onboarding/components/Documents/DocumentUpload';
import {
  useAuth,
  refreshSession,
  uploadDocuments,
} from '@minicardiac-client/apis';
import { useRouter } from '@/apps/public/src/i18n/navigation';

export default function OrganisationFreeDocumentUploadPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const { showSuccess, showError } = useSnackbar();

  const { authState } = useAuth();

  const handleDoThisLater = () => {
    // Navigate to landing page
    router.push('/feed?fromSignup=true');
  };

  const handleContinue = async () => {
    // Set submitting state
    setIsSubmitting(true);
    setError(null);

    try {
      // Post the document data to the API with the correct structure
      // Using the correct endpoint path
      // await axiosInstance.post('/onboarding/document-upload', {
      //   documents: []
      // });

      const sessionRefreshed = await refreshSession();

      if (!sessionRefreshed) {
        console.warn('Failed to refresh session, but will try to continue');
      }

      await uploadDocuments();

      // Show success message
      showSuccess('Documents saved successfully!');

      // Navigate to the next step in the onboarding flow after a short delay
      setTimeout(() => {
        router.push('/organisation/subscription/free/add-network');
      }, 1000);
    } catch (err: any) {
      console.error('Error saving document data:', err);
      setError(err.message || 'Failed to save document data');
      showError(err.message || 'Failed to save document data');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DocumentUploadPageTemplate
      userName={authState.user?.displayName || ''}
      subtitleText="Let's set up your Organisation Account!"
      showBackButton={true}
      onBack={() => router.back()}
      currentStep={1}
      steps={['Profile Setup', 'Document Upload', 'Adding Network']}
    >
      <DocumentUploadForm hideSteppers={true} />

      {/* Action Buttons using shared template */}
      <ActionButtonsTemplate
        onSave={handleContinue}
        onSkip={handleDoThisLater}
        isSubmitting={isSubmitting}
        saveButtonText="Save and Continue"
        skipButtonText="Do this later"
        variant="organization"
      />

      {/* Error Display */}
      {error && (
        <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
          {error}
        </Box>
      )}
    </DocumentUploadPageTemplate>
  );
}
