import { useState } from 'react';
import {
  Box,
  Typography,
  Stack,
  Menu,
  MenuItem,
  useMediaQuery,
} from '@mui/material';
import CustomDialog from '../CustomDialog';
import { LoadingButton } from '../../loading-button';
import { Iconify } from '../../iconify';
import { PostButton } from '../../buttons/PostButton';
import { toast } from 'react-toastify';
import PostToast from '../../toast/PostToast';
import { isContentEmpty } from '@minicardiac-client/utilities';
import { CustomizedSteppers } from '../../onboarding';
import ArticlePostDetails from './ArticlePostDetails';
import { CustomEditorBase } from '../../textEditor/CustomEditorBase';
import { BackButton } from '../../buttons/Backbutton';
import { useTheme } from '@emotion/react';

interface ArticlePostDialogProps {
  open: boolean;
  onClose: () => void;
  setOpenScheduleDialog: () => void;
  content: string;
  setContent: (content: string) => void;
}

const ArticlePostDialog = ({
  open,
  onClose,
  setOpenScheduleDialog,
  content,
  setContent,
}: ArticlePostDialogProps) => {
  const [tags, setTags] = useState('');
  const [activeStep, setActiveStep] = useState(0);
  const [thumbnail, setThumbnail] = useState<File | null>(null);
  const [audience, setAudience] = useState('PROFESSIONAL');
  const [speciality, setSpeciality] = useState('CARDIAC_SURGERY');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const openMenu = Boolean(anchorEl);

  const [title, setTitle] = useState('');
  const [summary, setSummary] = useState('');

  const theme: any = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));

  const canPost = Boolean(title.trim() && summary.trim());

  const handleNextStep = () => {
    setActiveStep((prev) => prev + 1);
  };

  const handlePost = () => {
    onClose();

    toast(<PostToast value={'Article Posted'} />, {
      position: 'bottom-right',
      autoClose: 5000,
      hideProgressBar: true,
      closeButton: false,
      style: {
        padding: 0,
        width: 'fit-content',
        background: 'white',
        boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)',
      },
    });
  };

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title=""
      sx={{
        p: 0,
        px: { xs: 0, sm: '80px' },
        pt: { xs: 0, sm: '50px' },
        alignItems: { xs: 'stretch', sm: 'start' },
        '.MuiDialog-paper': {
          maxHeight: { xs: '100%', sm: 'calc(100% - 64px' },
        },
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        padding={{ xs: '16px', sm: '40px' }}
        width="100%"
        sx={{ backgroundColor: 'white' }}
      >
        {/* Heading */}
        <Box
          position="relative"
          display="flex"
          justifyContent={{ xs: 'space-between', sm: 'center' }}
          alignItems="center"
          height="35px"
          mb={'20px'}
        >
          <Box sx={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
            {screenBelowSM && <BackButton onClick={onClose} />}
            <Typography
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 500,
                fontSize: { xs: '20px', sm: '28px' },
                color: '#1E1E1E',
              }}
            >
              New Article Post
            </Typography>
          </Box>

          <Typography
            sx={{
              position: 'absolute',
              right: 0,
              top: { xs: 10, sm: 0 },
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 600,
              fontSize: '16px',
              color: '#A24295',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            Drafts <Iconify icon="solar:arrow-right-linear" />
          </Typography>
        </Box>

        <CustomizedSteppers
          activeStep={activeStep}
          steps={['Compose Article', 'Add Details']}
        />

        {activeStep === 0 ? (
          <CustomEditorBase
            value={content}
            onChange={setContent}
            label={'Your article'}
            sx={{
              // height: { xs: '589px', sm: '420px' },
              maxHeight: { xs: '589px', sm: '420px' },

              mt: '20px',
              mb: { xs: '140px', sm: '0px' },
            }}
            menuType={'article'}
          />
        ) : (
          <ArticlePostDetails
            tags={tags}
            setTags={setTags}
            audience={audience}
            setAudience={setAudience}
            speciality={speciality}
            setSpeciality={setSpeciality}
            thumbnail={thumbnail}
            setThumbnail={setThumbnail}
            title={title}
            setTitle={setTitle}
            summary={summary}
            setSummary={setSummary}
          />
        )}

        {/* Footer Buttons */}
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={{ xs: '10px', sm: '20px' }}
          justifyContent={{ xs: 'center', sm: 'center' }}
          sx={{
            position: { xs: 'fixed', sm: 'static' },
            bottom: 0,
            left: 0,
            mt: { xs: '0px', sm: '40px' },
            width: { xs: '100%', sm: 'auto' },
            background: { xs: 'rgba(255,255,255,0.8)', sm: 'transparent' },
            backdropFilter: { xs: 'blur(20px)', sm: 'none' },
            padding: { xs: '20px', sm: 0 },
            boxShadow: { xs: '0 -4px 20px 0 rgba(0,0,0,0.1)', sm: 'none' },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: '20px',
            }}
          >
            <LoadingButton
              variant="outlined"
              onClick={onClose}
              sx={{
                width: '156px',
                height: '40px',
                backgroundColor: 'white',
                border: { xs: 'none', sm: '1px solid #A24295' },
                color: '#A24295',
                '&:hover': {
                  backgroundColor: 'secondary.light',
                },
                fontSize: '16px',
                fontWeight: 700,
              }}
            >
              Cancel
            </LoadingButton>

            {activeStep === 0 ? (
              <LoadingButton
                onClick={handleNextStep}
                variant="contained"
                disabled={isContentEmpty(content)}
                sx={{
                  width: '156px',
                  height: '40px',
                  backgroundColor: isContentEmpty(content) ? '#ccc' : '#A24295',
                  color: 'white',
                  fontSize: '16px',
                  fontWeight: 700,
                  '&:hover': {
                    backgroundColor: isContentEmpty(content)
                      ? '#ccc'
                      : '#8d2a7b',
                  },
                }}
              >
                Next
              </LoadingButton>
            ) : (
              <>
                <PostButton
                  setAnchorEl={setAnchorEl}
                  handlePost={handlePost}
                  disabled={!canPost}
                  isOpen={openMenu}
                />
                <Menu
                  anchorEl={anchorEl}
                  open={openMenu}
                  onClose={() => setAnchorEl(null)}
                >
                  <MenuItem
                    onClick={() => {
                      setOpenScheduleDialog();
                    }}
                  >
                    Schedule
                  </MenuItem>
                  <MenuItem onClick={() => setAnchorEl(null)}>
                    Save Draft
                  </MenuItem>
                  <MenuItem onClick={() => setAnchorEl(null)}>
                    Add to Sponsorship Queue
                  </MenuItem>
                </Menu>
              </>
            )}
          </Box>
        </Stack>
      </Box>
    </CustomDialog>
  );
};

export default ArticlePostDialog;
