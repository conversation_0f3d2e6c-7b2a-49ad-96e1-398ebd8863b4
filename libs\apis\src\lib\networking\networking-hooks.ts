import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { axiosInstance } from '../http-client.js';
import { establishSession } from '../auth/auth-utils.js';

// Define profile types
export interface ProfileData {
  id: string;
  name: string;
  qualification: string;
  segmentCategory: string;
  worksAt: string;
  rating: number;
  avatarUrl: string;
  sponsoredBy?: string;
  isConnected?: boolean;
  isFollowing?: boolean;
  connectionRequestSent?: boolean;
}

// Define query keys for networking
export const networkingQueryKeys = {
  all: ['networking'] as const,

  discoverProfiles: (accountType?: string) =>
    accountType
      ? [...networkingQueryKeys.all, 'discover-profiles', accountType] as const
      : [...networkingQueryKeys.all, 'discover-profiles'] as const,
};

// API function to fetch discover profiles
export const fetchDiscoverProfiles = async (accountType?: string): Promise<ProfileData[]> => {
  const response = await axiosInstance.get('/networking/discover-profiles', {
    params: {
      accountType,
    }
  });
  console.log('Discover profiles response:', response.data);

  // Map the API response to our ProfileData structure
  if (response.data?.data?.length) {
    return response.data.data.map((user: any) => {
      const specialist = user.createdWorkspaces?.[0]?.specialists || {};
      
      // Get job title with fallback
      const jobTitle = specialist.jobTitle || specialist.title || '';
      
      // Get employer name
      const employerName = specialist.employer?.name || '';
      
      // Get qualifications (can be string or array)
      const qualifications = specialist.qualifications;
      const qualificationText = Array.isArray(qualifications) 
        ? qualifications.join(', ') 
        : qualifications || '';
      
      // Format the location information
      const location = [employerName, user.city, user.country]
        .filter(Boolean)
        .join(', ');
      
      return {
        id: user.id,
        name: user.displayName || user.name || user.email || 'User',
        qualification: qualificationText,
        segmentCategory: jobTitle,
        worksAt: location,
        rating: 4, // Default rating as per design
        avatarUrl: user.profileImageUrlThumbnail || user.profileImageUrl || '',
        sponsoredBy: user.sponsoredBy,
        // Connection states should be managed by the UI state
        isConnected: false,
        isFollowing: false,
        connectionRequestSent: false
      };
    });
  }

  // If the API returns unexpected data format, return an empty array
  return [];
};

export const uploadDocuments = async () => {
  try {
    // Ensure session is established before making this onboarding API call
    // The HTTP client interceptor will handle 403 errors and retry with fresh tokens
    await establishSession();

    const response = await axiosInstance.post('/onboarding/document-upload', {
      documents: [] // Reverting to empty array as per backend developer's suggestion
    });

    return response.data;
  } catch (error: any) {
    console.error('Error uploading documents:', error);

    if (error.response) {
      console.error('Response error headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request error:', error.request);
    } else {
      console.error('Error message:', error.message);
    }

    throw error;
  }
};

// Hook to get discover profiles
export const useGetDiscoverProfiles = (accountType?: string) => {
  return useQuery({
    queryKey: networkingQueryKeys.discoverProfiles(accountType),
    queryFn: () => fetchDiscoverProfiles(accountType),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
};

// Hook to upload documents
export const useUploadDocuments = () => {
  return useMutation({
    mutationFn: uploadDocuments,
  });
};

// API function to send connection request
export const sendConnectionRequest = async (profileId: string) => {
  try {
    const response = await axiosInstance.post('/networking/connect/request', {
      recipientId: profileId
    });
    return response.data;
  } catch (error) {
    console.error('Error sending connection request:', error);
    throw error;
  }
};

// Hook to send connection request
export const useSendConnectionRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: sendConnectionRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: networkingQueryKeys.discoverProfiles() });
    },
  });
};

// API function to follow a profile
export const followProfile = async (profileId: string) => {
  try {
    console.log('Following profile with ID:', profileId);
    const response = await axiosInstance.post('/networking/follower', {
      targetUserId: profileId
    });
    console.log('Follow response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error following profile:', error);
    throw error;
  }
};

// Hook to follow a profile
export const useFollowProfile = () => {
  

  return useMutation({
    mutationFn: followProfile,
    onSuccess: () => {
      // Don't invalidate the query cache automatically
      // This prevents the UI from resetting after a successful follow
      // queryClient.invalidateQueries({ queryKey: networkingQueryKeys.discoverProfiles() });
    },
  });
};

// API function to unfollow a profile
export const unfollowProfile = async (profileId: string) => {
  try {
    // The correct endpoint should be /networking/follower with targetUserId in the request body
    const response = await axiosInstance.delete('/networking/follower', {
      data: {
        targetUserId: profileId
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error unfollowing profile:', error);
    throw error;
  }
};

// Hook to unfollow a profile
export const useUnfollowProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: unfollowProfile,
    onSuccess: () => {
      // Invalidate discover profiles query to refresh the data
      queryClient.invalidateQueries({ queryKey: networkingQueryKeys.discoverProfiles() });
    },
  });
};

// API function to cancel connection request
export const cancelConnectionRequest = async (profileId: string) => {
  try {
    console.log('Canceling connection request for profile ID:', profileId);
    // Use the correct endpoint and method for withdrawing a connection request
    const response = await axiosInstance.post('/networking/connect/request/withdraw', {
      recipientId: profileId  // Using recipientId as shown in the Swagger documentation
    });
    console.log('Cancel connection request response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error canceling connection request:', error);
    throw error;
  }
};

// Hook to cancel connection request
export const useCancelConnectionRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: cancelConnectionRequest,
    onSuccess: () => {
      // Invalidate discover profiles query to refresh the data
      queryClient.invalidateQueries({ queryKey: networkingQueryKeys.discoverProfiles() });
    },
  });
};





