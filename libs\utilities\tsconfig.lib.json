{"extends": "../../tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "rootDir": "src", "outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo", "emitDeclarationOnly": true, "forceConsistentCasingInFileNames": true, "types": ["node", "vite/client"]}, "include": ["src/**/*.ts", "src/lib/CarouselContext.tsx"], "references": [{"path": "../apis/tsconfig.lib.json"}], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}