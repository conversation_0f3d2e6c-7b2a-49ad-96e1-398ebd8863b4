import {
  MenuControlsContainer,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MenuButtonBold,
  MenuButtonItalic,
  MenuButtonUnderline,
  MenuButtonS<PERSON>ket<PERSON><PERSON>,
  Menu<PERSON>uttonB<PERSON><PERSON><PERSON>ist,
  <PERSON>u<PERSON><PERSON>on<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>onCodeBlock,
  MenuButtonHorizontalRule,
  MenuButtonCode,
  MenuButtonImageUpload,
  MenuButtonAlignLeft,
  MenuButtonAlignCenter,
  MenuButtonAlignRight,
  MenuButtonAlignJustify,
} from 'mui-tiptap';
import { Editor } from '@tiptap/react';
import { MenuButtonLink } from './MenuButtonLink';
import MenuSelectHeading from './MenuSelectHeading';

export function CustomEditorMenuArticle({ editor }: { editor: Editor }) {
  const handleUploadFiles = (files: File[]) => {
    const attributes = Array.from(files).map((file) => ({
      src: URL.createObjectURL(file),
      alt: file.name,
    }));

    return attributes;
  };
  return (
    <MenuControlsContainer>
      {/* Style Dropdown */}
      <MenuSelectHeading editor={editor} />
      <MenuDivider />
      {/* Text Formatting */}
      <MenuButtonBold />
      <MenuButtonItalic />
      <MenuButtonUnderline />
      <MenuButtonStrikethrough />
      <MenuDivider />
      {/* Text Alignment */}
      <MenuButtonAlignLeft />
      <MenuButtonAlignCenter />
      <MenuButtonAlignRight />
      <MenuButtonAlignJustify />
      <MenuDivider />
      {/* Lists */}
      <MenuButtonBulletedList />
      <MenuButtonOrderedList />
      <MenuDivider />
      {/* Other */}
      <MenuButtonBlockquote />
      <MenuButtonCode />
      <MenuButtonCodeBlock />
      <MenuDivider />
      <MenuButtonHorizontalRule />
      <MenuButtonLink editor={editor} />
      <MenuButtonImageUpload onUploadFiles={handleUploadFiles} />
    </MenuControlsContainer>
  );
}
