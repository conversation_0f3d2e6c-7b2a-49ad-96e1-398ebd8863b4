import { SxProps, Theme } from '@mui/material/styles';
import { Typography } from '@mui/material';

/**
 * Profile setup form data interface
 */
export interface ProfileFormData {
  title?: string;
  qualifications?: string;
  jobTitle?: string;
  employerId?: string;
  introductoryStatement?: string;
  category?: string;
  mainProfession?: string;
  profileImageUrl?: string | null;
  profileImageUrlThumbnail?: string | null;
}

export interface OrganisationProfileFormData {
  category: string;
  location: string;
  parentOrganisation: string | null;
  introductoryStatement: string;
  mainProfession: 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH';
  mapLink?: string;
  profileImageUrl?: string;
  profileImageUrlThumbnail?: string;
}

/**
 * Extended Theme type that includes customValues
 */
export interface ExtendedTheme extends Theme {
  customValues: {
    [x: string]: unknown;
    gradient?: {
      primary?: string;
      neutral?: string;
    };
    opLights?: {
      width: number;
      height: number;
      top: number;
      left: number;
    };
    button?: {
      minWidth?: string | number;
      width?: number;
      height?: number;
      radius?: number;
      spacing?: number;
      smHeight?: number;
      textVariantSmPx?: number;
      padding?: {
        x?: number;
      };
      border?: {
        width?: number;
      };
    };
    tabs?: {
      width?: number;
      height?: number;
      gap?: number;
    };
    backButton?: {
      width?: number;
      height?: number;
      top?: number;
      left?: number;
      angle?: number;
    };
    typography?: {
      button?: {
        small?: {
          lineHeight?: string | number;
          fontSize?: string;
          fontWeight?: number;
          letterSpacing?: string;
        };
        medium?: {
          lineHeight?: string | number;
          fontSize?: string;
          fontWeight?: number;
          letterSpacing?: string;
        };
      };
    };
    signupCard?: {
      width: number;
      height: number;
      borderRadius: number;
      borderWidth: number;
      borderColor: string;
      shape?: {
        width: number;
        height: number;
        top: number;
        left: number;
        angle: number;
        opacity: number;
        borderRadius: string;
      };
      content: {
        width: number;
        height: number;
        top: number;
        left: number;
        gap: number;
        description: {
          width: number;
          height: number;
        };
      };
    };
    doctorCharacter: {
      width: number;
      height: number;
      top: number;
      left: number;
      color: string;
    };
    gentlemanCharacter: {
      width: number;
      height: number;
      top: number;
      left: number;
    };
    girlManCharacter: {
      width: number;
      height: number;
      top: number;
      left: number;
    };
    subscriptionPlanCard: {
      width: number;
      height: number;
      borderRadius: number;
      borderWidth: number;
      p: number;
      gap: number;
      borderColor: string;
      icon: {
        width: number;
        height: number;
      };
    };
  };
}

/**
 * Signup option type for different user types
 */
export interface SignupOption {
  title: string;
  description: string;
  icon: string;
  path: string;
  character: string;
  shape: string;
}

export interface AuthLayoutProps {
  children: React.ReactNode;
  showCarousel?: boolean;
  showBackButton?: boolean;
  onBackClick?: () => void;
  sx?: SxProps<Theme>;
  activeTab?: number;
}

/**
 * Props for the AuthCarousel component
 */
export interface AuthCarouselProps {
  images: string[];
}

export interface AuthTabsProps {
  value: number;
  onChange: (event: React.SyntheticEvent, newValue: number) => void;
}

export interface SignupOptionCardProps {
  option: SignupOption;
  onClick: (path: string) => void;
  variant?: 'signup' | 'professional';
  width?: number | string;
  height?: number | string;
  selected?: boolean;
  additionalImage?: string;
  titleTypographyProps?: React.ComponentProps<typeof Typography>;
}

export interface SocialLoginButtonProps {
  provider: 'google' | 'apple';
  onClick?: () => void;
  disabled?: boolean;
  label?: string;
}

export interface AuthFormProps {
  type: 'signin' | 'signup';
  userType?: 'professional' | 'organization' | 'public';
  onSubmit?: (data: Record<string, unknown>) => void;
}
