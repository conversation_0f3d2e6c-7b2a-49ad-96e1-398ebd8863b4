import { Box, Button, Stack } from '@mui/material';
import { Iconify } from '../iconify';

export const PostButton = ({
  setAnchorEl,
  handlePost,
  disabled = false,
  isOpen = false,
}: {
  setAnchorEl: (el: HTMLElement) => void;
  handlePost: () => void;
  disabled?: boolean;
  isOpen?: boolean;
}) => {
  return (
    <Stack
      direction="row"
      sx={{
        borderRadius: '8px',
        overflow: 'hidden',
        width: '156px',
        pointerEvents: disabled ? 'none' : 'auto',
      }}
    >
      {/* Post Button */}
      <Button
        variant="contained"
        onClick={handlePost}
        disabled={disabled}
        sx={{
          width: '116px',
          height: '40px',
          minWidth: 'unset',
          minHeight: 'unset',
          padding: 0,
          backgroundColor: '#A24295',
          color: 'white',
          borderRadius: 0,
          fontWeight: 700,
          fontSize: '16px',
          '&:hover': {
            backgroundColor: '#8C3680',
          },
          '&.Mui-disabled': {
            backgroundColor: '#A3A3A3',
            color: 'white',
          },
        }}
      >
        Post
      </Button>

      {/* Divider */}
      <Box
        sx={{
          width: '1px',
          backgroundColor: 'white',
          opacity: disabled ? 0.3 : 1,
        }}
      />

      {/* Dropdown Button */}
      <Button
        variant="contained"
        onClick={(e) => setAnchorEl(e.currentTarget)}
        disabled={disabled}
        sx={{
          width: '40px',
          height: '40px',
          minWidth: 'unset',
          minHeight: 'unset',
          padding: 0,
          backgroundColor: '#A24295',
          color: 'white',
          borderRadius: 0,
          '&:hover': {
            backgroundColor: '#8C3680',
          },
          '&.Mui-disabled': {
            backgroundColor: '#A3A3A3',
            color: 'white',
          },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
            transition: 'transform 0.3s ease',
            width: '40px',
            justifyContent: 'center',
          }}
        >
          <Iconify icon="solar:alt-arrow-down-linear" />
        </Box>
      </Button>
    </Stack>
  );
};
