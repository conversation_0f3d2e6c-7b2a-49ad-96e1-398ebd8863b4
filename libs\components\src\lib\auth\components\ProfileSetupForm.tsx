'use client';

import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  TextField,
  Typography,
  Grid,
  Autocomplete,
  CircularProgress,
  ToggleButtonGroup,
  ToggleButton,
  useMediaQuery,
} from '@mui/material';
import IntroductoryStatement from '../../onboarding/components/CardiacSpecialist/IntroductoryStatement';
import CreateEmployerDialog from '../../onboarding/components/CardiacSpecialist/CreateEmployerDialog';
import {
  useGetEmployers,
  Employer,
  useGetCategories,
  Categories,
} from '@minicardiac-client/apis';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useTheme } from '@emotion/react';
import { INTRODUCTORY_STATEMENT_LIMITS } from '@minicardiac-client/constants';

import { ProfilePicture } from '../../common/ProfilePicture';
import { useProfilePicture } from '../../common/ProfilePicture';
import { ExtendedTheme } from '../../auth/types/auth.types';
import { removeAWSS3DomainFromURL } from '@minicardiac-client/utilities';
import { ActionButtonsTemplate } from '../../onboarding/templates';

interface ProfileSetupFormProps {
  isBasicPlan?: boolean;
  onChange?: (formData: ProfileFormData) => void;
  onSave?: (formData: ProfileFormData) => void;
  onSkip?: () => void;
  isSubmitting?: boolean;
  userData?: any;
  professionalType?: string;
}

export interface ProfileFormData {
  title: string;
  qualifications?: string;
  jobTitle?: string;
  employerId?: string;
  introductoryStatement: string;
  category?: string;
  mainProfession?: string;
  profileImageUrl?: string | null;
  profileImageUrlThumbnail?: string | null;
}

// Define validation schema for the form
const validationSchema = yup.object().shape({
  title: yup.string().required('Title is required'),
  qualifications: yup.string().when('isBasicPlan', {
    is: false,
    then: (schema) => schema.required('Qualifications are required'),
    otherwise: (schema) => schema.optional(),
  }),
  jobTitle: yup.string().when('isBasicPlan', {
    is: false,
    then: (schema) => schema.required('Job title is required'),
    otherwise: (schema) => schema.optional(),
  }),
  category: yup.string().when('professionalType', {
    is: (val: string) => val === 'ALLIED_CARDIAC',
    then: (schema) => schema.required('Category is required'),
    otherwise: (schema) => schema.optional(),
  }),
  mainProfession: yup.string().when('professionalType', {
    is: (val: string) => val === 'ALLIED_CARDIAC',
    then: (schema) => schema.required('Main profession is required'),
    otherwise: (schema) => schema.optional(),
  }),
  introductoryStatement: yup
    .string()
    .required('Introductory statement is required')
    .max(
      INTRODUCTORY_STATEMENT_LIMITS.MAX,
      `Maximum ${INTRODUCTORY_STATEMENT_LIMITS.MAX} characters only`
    ),
  profileImageUrl: yup.string().nullable(),
  profileImageUrlThumbnail: yup.string().nullable(),
});

const ProfileSetupForm: React.FC<ProfileSetupFormProps> = ({
  isBasicPlan = true,
  onChange,
  onSave,
  onSkip,
  isSubmitting = false,
  userData,
  professionalType,
}) => {
  // Form validation with react-hook-form and yup
  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    watch,
    formState: { errors, isValid },
  } = useForm({
    resolver: yupResolver(validationSchema),
    mode: 'onChange',
    context: { isBasicPlan },
    defaultValues: {
      title: '',
      qualifications: '',
      jobTitle: '',
      introductoryStatement: '',
      profileImageUrl: '',
      profileImageUrlThumbnail: '',
    },
  });

  // Debug log for form validation context
  console.log('Form validation context - isBasicPlan:', isBasicPlan);
  console.log('Form errors:', errors);
  console.log('Form isValid:', isValid);

  // Watch form values
  const formValues = watch();

  const displayName = userData?.displayName || '';
  const theme = useTheme() as ExtendedTheme;
  const isXsScreen = useMediaQuery(theme.breakpoints.down('sm'));

  // Register onChange callback for field changes
  const handleFieldChange = useCallback(
    (name: any, value: any) => {
      console.log('Field change:', name, value); // Debug log
      setValue(name, value, { shouldValidate: true });

      // Only call onChange when explicitly updating a field
      if (onChange) {
        const currentValues = getValues();
        const updatedValues = { ...currentValues, [name]: value };
        console.log('Updated form values:', updatedValues); // Debug log
        onChange(updatedValues);
      }
    },
    [setValue, getValues, onChange]
  );

  // Form submission handler
  const onSubmit = (data: ProfileFormData) => {
    console.log('Form submission data:', data); // Debug log
    if (onSave) {
      onSave(data);
    }
  };

  // Skip handler
  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    }
  };

  // Employer search state
  const [employerSearchTerm, setEmployerSearchTerm] = useState<string>('');
  const [selectedEmployer, setSelectedEmployer] = useState<Employer | null>(
    null
  );
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>('');
  const [showCreateEmployerModal, setShowCreateEmployerModal] =
    useState<boolean>(false);
  const [employerName, setEmployerName] = useState<string>('');
  const [showNotFoundMessage, setShowNotFoundMessage] =
    useState<boolean>(false);

  const { data: categories, isLoading: isLoadingCategories } =
    useGetCategories(professionalType === 'ALLIED_CARDIAC' ? 'ALLIED_CARDIAC' : undefined);
  const { data: employers, isLoading: isLoadingEmployers } =
    useGetEmployers(debouncedSearchTerm);

  const [selectedCategory, setSelectedCategory] = useState<Categories | null>(
    null
  );

  const [mainWork, setMainWork] = useState<string | null>(null);

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(employerSearchTerm);
    }, 500); // Increased from 300ms to 500ms

    return () => clearTimeout(timer);
  }, [employerSearchTerm]);

  // Set the "not found" message to always be visible after searching
  useEffect(() => {
    if (employerSearchTerm && !isLoadingEmployers) {
      if (!employers || employers.length === 0) {
        setShowNotFoundMessage(true);
      }
    }
  }, [employerSearchTerm, isLoadingEmployers, employers]);

  const updateFormField = useCallback(
    (field: any, value: any) => {
      handleFieldChange(field, value);
    },
    [handleFieldChange]
  );

  // Handle employer selection
  useEffect(() => {
    if (selectedEmployer) {
      updateFormField('employerId', selectedEmployer.id);
    } else {
      updateFormField('employerId', undefined);
    }
  }, [updateFormField, selectedEmployer]);

  // Handle opening the create employer dialog
  const handleOpenCreateEmployerDialog = useCallback(() => {
    if (employerSearchTerm.trim()) {
      setEmployerName(employerSearchTerm.trim());
      setShowCreateEmployerModal(true);
    } else if (employerName.trim()) {
      setShowCreateEmployerModal(true);
    } else {
      setEmployerName('');
      setShowCreateEmployerModal(true);
    }
  }, [employerSearchTerm, employerName]);

  // Handle closing the create employer dialog
  const handleCloseCreateEmployerDialog = () => {
    setShowCreateEmployerModal(false);
    setShowNotFoundMessage(false); // Hide the "not found" message
  };

  const [savedEmployerUrl, setSavedEmployerUrl] = useState<string | null>(null);

  const handleEmployerSave = useCallback(
    (employerId: string) => {
      if (!employerId) {
        return;
      }

      // Update the form with the new employer ID
      updateFormField('employerId', employerId);
      // Set the selected employer
      setSelectedEmployer({
        id: employerId,
        name: employerName || employerSearchTerm,
      });

      // Close the dialog
      setShowCreateEmployerModal(false);
    },
    [employerName, employerSearchTerm, updateFormField]
  );

  // Handle the saved employer URL and update the form
  useEffect(() => {
    if (!savedEmployerUrl || !employerName) return;

    // Create a temporary employer object
    const tempEmployer = {
      id: 'temp-id',
      name: employerName,
    };

    setSelectedEmployer(tempEmployer);

    updateFormField('employerId', 'temp-id');

    setEmployerSearchTerm(employerName);

    // Refresh the employers list after a delay
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(employerName);
    }, 500);

    return () => {
      clearTimeout(timer);
      setSavedEmployerUrl(null);
    };
  }, [savedEmployerUrl, employerName, updateFormField]);

  const { previewUrl, isUploading, error, handleImageChange, handleRemove } =
    useProfilePicture({
      initialImageUrl: formValues?.profileImageUrl || null,
      onImageUpload: (imageUrl, thumbnailUrl) => {
        updateFormField('profileImageUrl', imageUrl);
        const cleanedThumbnailUrl = removeAWSS3DomainFromURL(thumbnailUrl);
        updateFormField('profileImageUrlThumbnail', cleanedThumbnailUrl);
      },
    });

  return (
    <>
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          px: { xs: 0, sm: 3, md: 5 },
          pb: { xs: '100px' },
          borderRadius: 1,
          boxShadow: { xs: 'none', sm: '0 12px 24px #A3A3A31F' },
          mb: 4,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: { xs: 'center', md: 'start' },
            justifyContent: 'space-between',
            width: '100%',
            gap: { xs: '40px', md: '72px' },
          }}
        >
          {/* Profile Picture Upload Section */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: { xs: 'center', md: 'flex-start' },
            }}
          >
            <ProfilePicture
              theme={theme}
              previewUrl={previewUrl}
              displayName={userData?.displayName || ''}
              onImageChange={handleImageChange}
              onRemove={handleRemove}
              isUploading={isUploading}
              error={error}
              disabled={isSubmitting}
            />
          </Box>

          {/* Form Fields Section */}
          <Box flexGrow={1}>
            <Grid
              container
              columns={{ xs: 4, sm: 8, md: 2 }}
              rowSpacing={{ xs: 3, md: 7 }}
              columnSpacing={{ xs: 3, md: 5 }}
              gridTemplateColumns={2}
              position={'relative'}
            >
              <Grid
                size={{ xs: 12, md: 1 }}
                gap={2}
                alignItems="center"
                display="flex"
                alignContent="center"
                sx={{
                  flexDirection: { sm: 'row' },
                  justifyContent: { xs: 'center', sm: 'flex-start' },
                  width: '100%',
                }}
              >
                <Controller
                  name="title"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      sx={{
                        width: { xs: '100%', sm: '100%', md: '360px' },
                        mb: { xs: 1, sm: 0 },
                      }}
                      label="Title"
                      placeholder="Dr./Ms."
                      variant="outlined"
                      size="small"
                      error={!!errors.title}
                      helperText={errors.title?.message}
                      slotProps={{
                        inputLabel: {
                          shrink: true,
                        },
                      }}
                    />
                  )}
                />
                <Typography
                  sx={(theme) => ({
                    ...(
                      theme.typography as unknown as {
                        subtitle3: React.CSSProperties;
                      }
                    ).subtitle3,
                    width: { xs: '100%', sm: '60%' },
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                    textAlign: { xs: 'center', sm: 'left' },
                    fontWeight: '400',
                  })}
                >
                  {displayName}
                </Typography>
              </Grid>

              {/* Additional fields for non-basic plan */}
              {!isBasicPlan && (
                <>
                  <Grid
                    size={{ xs: 12, md: 1 }}
                    display="flex"
                    justifyContent={{ xs: 'center', md: 'flex-end' }}
                    sx={{ width: '100%' }}
                  >
                    <Controller
                      name="qualifications"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          sx={{
                            width: { xs: '100%', sm: '100%', md: '360px' },
                          }}
                          label="Qualifications"
                          placeholder="PhD, MD"
                          variant="outlined"
                          size="small"
                          error={!!errors.qualifications}
                          helperText={errors.qualifications?.message}
                          slotProps={{
                            inputLabel: {
                              shrink: true,
                            },
                          }}
                        />
                      )}
                    />
                  </Grid>
                  {professionalType === 'ALLIED_CARDIAC' && (
                    <>
                      <Grid
                        size={{ xs: 12, md: 1 }}
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                        justifyContent={{ xs: 'center', md: 'flex-start' }}
                        sx={{ width: '100%' }}
                      >
                        <Autocomplete
                          sx={{
                            width: '100%',
                            my: 'auto',
                          }}
                          options={categories || []}
                          getOptionLabel={(option) => option.name}
                          loading={isLoadingCategories}
                          value={selectedCategory}
                          onChange={(_, newValue) => {
                            setSelectedCategory(newValue);
                            updateFormField('category', newValue?.id);
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Category"
                              variant="outlined"
                              size="small"
                              placeholder="Choose from Options"
                              sx={{
                                width: { xs: '100%', sm: '100%', md: '360px' },
                              }}
                              // error={!!errors.category}
                              // helperText={errors.category?.message}
                              InputLabelProps={{
                                shrink: true,
                              }}
                              InputProps={{
                                ...params.InputProps,
                                endAdornment: (
                                  <>
                                    {isLoadingCategories ? (
                                      <CircularProgress
                                        color="inherit"
                                        size={20}
                                      />
                                    ) : null}
                                    {params.InputProps.endAdornment}
                                  </>
                                ),
                              }}
                            />
                          )}
                        />
                      </Grid>
                      <Grid
                        size={{ xs: 12, md: 1 }}
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                        justifyContent={{ xs: 'center', md: 'flex-end' }}
                        sx={{ width: { xs: '100%', sm: '100%', md: '360px' } }}
                      >
                        <Box
                          sx={{
                            margin: 'auto',
                            width: { xs: '100%', sm: '100%', md: '290px' },
                          }}
                        >
                          <Controller
                            name="mainProfession"
                            control={control}
                            render={({ field }) => (
                              <>
                                <Typography
                                  sx={{ mt: 0, fontWeight: 600 }}
                                  variant="caption"
                                >
                                  I mainly work with:
                                </Typography>
                                <ToggleButtonGroup
                                  color="secondary"
                                  exclusive
                                  value={field.value}
                                  onChange={(_, value) => {
                                    if (value) field.onChange(value);
                                  }}
                                  sx={{
                                    width: '100%',
                                  }}
                                  // error={!!errors.mainProfession}
                                >
                                  <ToggleButton
                                    value="CARDIAC_SURGEON"
                                    onClick={() =>
                                      setMainWork('CARDIAC_SURGEON')
                                    }
                                    size="small"
                                    sx={(theme) => ({
                                      // width: 68,
                                      // height: 36,
                                      // minWidth: 68,
                                      borderRadius: '8px',
                                      backgroundColor:
                                        mainWork === 'CARDIAC_SURGEON'
                                          ? '#E3C6DFBF'
                                          : 'transparent',
                                      color:
                                        mainWork === 'CARDIAC_SURGEON'
                                          ? theme.palette.secondary.main
                                          : '#1E1E1E',
                                      fontFamily:
                                        "'Plus Jakarta Sans', sans-serif",
                                      fontWeight:
                                        mainWork === 'CARDIAC_SURGEON'
                                          ? 600
                                          : 300,
                                      fontSize: '14px',
                                      textTransform: 'none',
                                      // padding: '2 4px',
                                      '&:hover': {
                                        backgroundColor:
                                          mainWork === 'CARDIAC_SURGEON'
                                            ? '#E3C6DFBF'
                                            : 'rgba(0, 0, 0, 0.04)',
                                      },
                                    })}
                                  >
                                    Cardiac Surgeons
                                  </ToggleButton>
                                  <ToggleButton
                                    value="CARDIOLOGIST"
                                    onClick={() => setMainWork('CARDIOLOGIST')}
                                    size="small"
                                    sx={(theme) => ({
                                      // width: 68,
                                      // height: 36,
                                      // minWidth: 68,
                                      borderRadius: '8px',
                                      backgroundColor:
                                        mainWork === 'CARDIOLOGIST'
                                          ? '#E3C6DFBF'
                                          : 'transparent',
                                      color:
                                        mainWork === 'CARDIOLOGIST'
                                          ? theme.palette.secondary.main
                                          : '#1E1E1E',
                                      fontFamily:
                                        "'Plus Jakarta Sans', sans-serif",
                                      fontWeight:
                                        mainWork === 'CARDIOLOGIST' ? 600 : 300,
                                      fontSize: '14px',
                                      textTransform: 'none',
                                      // padding: '2 4px',
                                      '&:hover': {
                                        backgroundColor:
                                          mainWork === 'CARDIOLOGIST'
                                            ? '#E3C6DFBF'
                                            : 'rgba(0, 0, 0, 0.04)',
                                      },
                                    })}
                                  >
                                    Cardiologists
                                  </ToggleButton>
                                  {!isBasicPlan && (
                                    <ToggleButton
                                      value="BOTH"
                                      onClick={() => setMainWork('BOTH')}
                                      size="small"
                                      sx={(theme) => ({
                                        // width: 68,
                                        // height: 36,
                                        // minWidth: 68,
                                        borderRadius: '8px',
                                        backgroundColor:
                                          mainWork === 'BOTH'
                                            ? '#E3C6DFBF'
                                            : 'transparent',
                                        color:
                                          mainWork === 'BOTH'
                                            ? theme.palette.secondary.main
                                            : '#1E1E1E',
                                        fontFamily:
                                          "'Plus Jakarta Sans', sans-serif",
                                        fontWeight:
                                          mainWork === 'BOTH' ? 600 : 300,
                                        fontSize: '14px',
                                        textTransform: 'none',
                                        // padding: '2 4px',
                                        '&:hover': {
                                          backgroundColor:
                                            mainWork === 'BOTH'
                                              ? '#E3C6DFBF'
                                              : 'rgba(0, 0, 0, 0.04)',
                                        },
                                      })}
                                    >
                                      Both
                                    </ToggleButton>
                                  )}
                                </ToggleButtonGroup>
                                {errors.mainProfession && (
                                  <Typography
                                    color="error"
                                    variant="caption"
                                    sx={{ mt: 1 }}
                                  >
                                    {errors.mainProfession.message}
                                  </Typography>
                                )}
                              </>
                            )}
                          />
                        </Box>
                      </Grid>
                    </>
                  )}
                  <Grid
                    size={{ xs: 12, md: 1 }}
                    sx={{
                      width: '100%',
                      display: 'flex',
                      justifyContent: { xs: 'center', md: 'flex-start' },
                    }}
                  >
                    <Controller
                      name="jobTitle"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          sx={{
                            width: { xs: '100%', sm: '100%', md: '360px' },
                          }}
                          label="Job Title"
                          placeholder="Sr. Cardiologist"
                          variant="outlined"
                          size="small"
                          error={!!errors.jobTitle}
                          helperText={errors.jobTitle?.message}
                          slotProps={{
                            inputLabel: {
                              shrink: true,
                            },
                          }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid
                    size={{ xs: 12, md: 1 }}
                    display="flex"
                    justifyContent={{ xs: 'center', md: 'flex-end' }}
                    sx={{ width: '100%' }}
                  >
                    <Box
                      sx={{
                        width: { xs: '100%', sm: '100%', md: '360px' },
                        display: 'flex',
                        flexDirection: 'column',
                      }}
                    >
                      <Autocomplete
                        sx={{
                          width: '100%',
                        }}
                        options={employers || []}
                        getOptionLabel={(option) => option.name}
                        loading={isLoadingEmployers}
                        value={selectedEmployer}
                        inputValue={employerSearchTerm}
                        onBlur={() => {
                          // Don't hide the not found message when clicking outside
                          if (
                            employerSearchTerm &&
                            !isLoadingEmployers &&
                            (!employers || employers.length === 0)
                          ) {
                            setShowNotFoundMessage(true);
                          }
                        }}
                        onChange={(_, newValue) => {
                          setSelectedEmployer(newValue);
                          // Reset the "not found" message when an employer is selected
                          if (newValue) {
                            setShowNotFoundMessage(false);
                          }
                        }}
                        onInputChange={(_, newInputValue) => {
                          setEmployerSearchTerm(newInputValue);
                          // Only update employerName if we have a value to prevent overwriting
                          if (newInputValue) {
                            setEmployerName(newInputValue);
                          }
                          // Show not found message when input changes and no results
                          if (newInputValue) {
                            // We'll set this to true and let the effect handle showing the message
                            // when the employers data is loaded
                            if (
                              !isLoadingEmployers &&
                              (!employers || employers.length === 0)
                            ) {
                              setShowNotFoundMessage(true);
                            }
                          } else {
                            setShowNotFoundMessage(false);
                          }
                        }}
                        noOptionsText="No employers found"
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label={
                              <>
                                Employer{' '}
                                <span
                                  style={{
                                    fontSize: '0.7rem',
                                    fontWeight: 'normal',
                                    color: '#666',
                                  }}
                                >
                                  &nbsp;(optional)
                                </span>
                              </>
                            }
                            placeholder="Start typing to search"
                            variant="outlined"
                            size="small"
                            InputLabelProps={{
                              shrink: true,
                            }}
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: (
                                <>
                                  {isLoadingEmployers ? (
                                    <CircularProgress
                                      color="inherit"
                                      size={20}
                                    />
                                  ) : null}
                                  {params.InputProps.endAdornment}
                                </>
                              ),
                            }}
                          />
                        )}
                      />
                      {showNotFoundMessage && (
                        <Typography
                          onClick={handleOpenCreateEmployerDialog}
                          sx={{
                            fontFamily: "'Plus Jakarta Sans', sans-serif",
                            fontWeight: 600,
                            fontSize: 'caption.fontSize',
                            lineHeight: 'caption.lineHeight',
                            letterSpacing: 'caption.letterSpacing',
                            color: '#A24295',
                            mt: 1,
                            cursor: 'pointer',
                            '&:hover': {
                              textDecoration: 'underline',
                            },
                          }}
                        >
                          Employer not found. Would you like to add a link?
                        </Typography>
                      )}
                    </Box>
                  </Grid>
                </>
              )}

              {/* Introductory Statement - for both plans */}
              <Box sx={{ width: '100%', pb: { sm: '50px' } }}>
                <IntroductoryStatement
                  initialValue={formValues.introductoryStatement}
                  onChange={(text) =>
                    updateFormField('introductoryStatement', text)
                  }
                />
                {errors.introductoryStatement && (
                  <Typography color="error" variant="caption" sx={{ mt: 1 }}>
                    {errors.introductoryStatement.message}
                  </Typography>
                )}
              </Box>
            </Grid>
          </Box>
        </Box>
      </Box>

      {/* Form Action Buttons using shared template */}
      <ActionButtonsTemplate
        onSave={handleSubmit(onSubmit)}
        onSkip={handleSkip}
        isSubmitting={isSubmitting}
        isValid={isValid}
        saveButtonText="Save and Continue"
        skipButtonText="Do this later"
        variant="professional"
        isXsScreen={isXsScreen}
      />

      {/* Create Employer Dialog */}
      <CreateEmployerDialog
        showCreateEmployerModal={showCreateEmployerModal}
        onClose={handleCloseCreateEmployerDialog}
        onSave={handleEmployerSave}
        employerName={employerName}
      />
    </>
  );
};

export default ProfileSetupForm;
