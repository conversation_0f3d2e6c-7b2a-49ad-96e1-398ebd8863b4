import { Box } from '@mui/material';
import { LoadingButton } from '../loading-button';
import { ExtendedTheme } from '../auth';

type Props = {
  onClose: () => void;
};

const WelcomeCardButtons = ({ onClose }: Props) => {
  return (
    <Box
      sx={{
        mt: '40px',
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        gap: 2,
        flexWrap: 'wrap',
        justifyContent: 'center',
        mb: '40px',
      }}
    >
      {/* First Button */}
      <LoadingButton
        onClick={onClose}
        sx={(theme: any) => ({
          width: {
            xs: '280px',
            sm: '320px',
            lg: '400px',
          },
          height: (theme as ExtendedTheme).customValues.button?.height,
          gap: (theme as ExtendedTheme).customValues.button?.spacing,
          backgroundColor: '#FFFFFF',
          color: (theme.palette as any).secondary?.main,
          border: '1px solid',
          borderColor: (theme.palette as any).secondary?.main,
          '&:hover': {
            backgroundColor: '#FFFFFF',
            color: (theme.palette as any).secondary?.main,
            border: '1px solid',
            borderColor: (theme.palette as any).secondary?.main,
          },
          borderRadius: '8px',
        })}
      >
        Leave it to me!
      </LoadingButton>

      {/* Second Button */}
      <LoadingButton
        onClick={onClose}
        sx={(theme: any) => ({
          width: {
            xs: '280px',
            sm: '320px',
            lg: '400px',
          },
          height: (theme as ExtendedTheme).customValues.button?.height,
          gap: (theme as ExtendedTheme).customValues.button?.spacing,
          backgroundColor: (theme.palette as any).secondary?.main,
          color: '#FFFFFF',
          '&:hover': {
            backgroundColor: (theme.palette as any).secondary?.main,
            color: '#FFFFFF',
            border: '1px solid',
            borderColor: (theme.palette as any).secondary?.main,
          },
          borderRadius: '8px',
        })}
      >
        I'd like a walkthrough
      </LoadingButton>
    </Box>
  );
};

export default WelcomeCardButtons;
