import { useState } from 'react';
import {
  Box,
  Typography,
  Checkbox,
  FormControlLabel,
  ClickAwayListener,
} from '@mui/material';
import { LoadingButton } from '../loading-button';

const filterOptions = [
  'Text posts',
  'Poll posts',
  'Media posts',
  'Article posts',
  'Question posts',
];

export default function FilterPopup({ onClose }: { onClose: () => void }) {
  const [selected, setSelected] = useState<string[]>([]);

  const handleToggle = (option: string) => {
    setSelected((prev) =>
      prev.includes(option)
        ? prev.filter((item) => item !== option)
        : [...prev, option]
    );
  };

  return (
    <ClickAwayListener onClickAway={onClose}>
      <Box
        onClick={(e) => e.stopPropagation()}
        sx={{
          position: 'absolute',
          top: '40px',
          right: 0,
          width: '374px',
          height: '337px',
          border: '1px solid #A24295',
          borderRadius: '8px',
          backgroundColor: 'white',
          boxShadow: '0px 2px 12px rgba(0,0,0,0.1)',
          zIndex: 9999,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
        }}
      >
        <Box sx={{ p: '20px' }}>
          <Typography
            sx={{
              fontWeight: 500,
              fontSize: '20px',
              marginBottom: '16px',
              color: '#1E1E1E',
            }}
          >
            Filter by:
          </Typography>

          <Box
            sx={{
              display: 'flex',
              gap: '40px',
              alignItems: 'flex-start',
            }}
          >
            {/* First column */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              {filterOptions
                .slice(0, Math.ceil(filterOptions.length / 2))
                .map((option) => (
                  <FormControlLabel
                    key={option}
                    sx={{
                      height: '40px',
                      alignItems: 'center',
                      margin: 0,
                      padding: 0,
                    }}
                    control={
                      <Box
                        sx={{
                          width: '40px',
                          height: '40px',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                      >
                        <Checkbox
                          checked={selected.includes(option)}
                          onChange={() => handleToggle(option)}
                          sx={{
                            width: '20px',
                            height: '20px',

                            borderRadius: '8px',
                            color: '#A3A3A3',
                            '&:hover': {
                              backgroundColor: 'transparent',
                            },
                            '&.Mui-checked': {
                              color: 'white',
                              backgroundColor: 'white',
                              '& svg': {
                                fill: '#A24295',
                              },
                            },
                          }}
                        />
                      </Box>
                    }
                    label={
                      <Typography
                        sx={{
                          fontSize: '16px',
                          fontWeight: 400,
                          color: '#1E1E1E',
                        }}
                      >
                        {option}
                      </Typography>
                    }
                  />
                ))}
            </Box>

            {/* Second column */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              {filterOptions
                .slice(Math.ceil(filterOptions.length / 2))
                .map((option) => (
                  <FormControlLabel
                    key={option}
                    sx={{
                      height: '40px',
                      alignItems: 'center',
                      margin: 0,
                      padding: 0,
                    }}
                    control={
                      <Box
                        sx={{
                          width: '40px',
                          height: '40px',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                      >
                        <Checkbox
                          checked={selected.includes(option)}
                          onChange={() => handleToggle(option)}
                          sx={{
                            width: '20px',
                            height: '20px',
                            p: '20px',
                            borderRadius: '8px',
                            color: '#A3A3A3',
                            '&:hover': {
                              backgroundColor: 'transparent',
                            },
                            '&.Mui-checked': {
                              color: 'white',
                              backgroundColor: 'white',
                              '& svg': {
                                fill: '#A24295',
                              },
                            },
                          }}
                        />
                      </Box>
                    }
                    label={
                      <Typography
                        sx={{
                          fontSize: '16px',
                          fontWeight: 400,
                          color: '#1E1E1E',
                        }}
                      >
                        {option}
                      </Typography>
                    }
                  />
                ))}
            </Box>
          </Box>
        </Box>

        {/* Footer Buttons */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            gap: '20px',
            boxShadow: '0 -4px 20px rgba(0,0,0,0.1)',
            p: '20px',
          }}
        >
          <LoadingButton
            variant="outlined"
            onClick={onClose}
            // disabled={isSubmitting}
            sx={{
              width: { xs: '156px' },
              height: '40px',
              backgroundColor: 'white',
              border: '1px solid #A24295',
              color: '#A24295',
              '&:hover': {
                backgroundColor: 'secondary.light',
              },
              fontWeight: 700,
              fontSize: '16px',
            }}
          >
            Cancel
          </LoadingButton>
          <LoadingButton
            variant="contained"
            disabled={selected.length === 0}
            sx={{
              width: { xs: '156px' },
              height: '40px',
              borderRadius: '4px',
              backgroundColor: 'secondary.main',
              color: 'white',
              '&:hover': {
                backgroundColor: 'secondary.dark',
              },
              '&.Mui-disabled': {
                backgroundColor: 'neutral.300',
                color: 'neutral.700',
              },
              fontWeight: 700,
              fontSize: '16px',
            }}
          >
            Apply
          </LoadingButton>
        </Box>
      </Box>
    </ClickAwayListener>
  );
}
