'use client';
import { Box } from '@mui/material';
import MobileTopBar from './MobileTopBar';
import { SearchBar } from './SearchBar';
import BottomNavBar from './BottomNavBar';

export default function MobileLayout() {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        backgroundColor: '#FFFFFF',
        position: 'relative',
      }}
    >
      <Box
        sx={{
          position: 'sticky',
          top: 0,
          zIndex: 10,
          backgroundColor: 'white',
          paddingTop: '16px',
          paddingInline: '16px',
          pb: '16px',
        }}
      >
        <MobileTopBar />
        <SearchBar />
      </Box>

      <Box
        sx={{
          flex: 1,
          overflowY: 'auto',
          paddingX: '16px',
          paddingBottom: '80px',
          paddingTop: '16px',
        }}
      >
        {[...Array(5)].map((_, idx) => (
          <Box
            key={idx}
            sx={{
              backgroundColor: 'white',
              borderRadius: '8px',
              boxShadow: '0px 4px 20px rgba(30, 30, 30, 0.1)',
              height: '280px',
              mb: 2,
            }}
          />
        ))}
      </Box>

      <Box
        sx={{
          position: 'sticky',
          bottom: 0,
          zIndex: 10,
          backgroundColor: 'white',
        }}
      >
        <BottomNavBar />
      </Box>
    </Box>
  );
}
