/**
 * Utility functions for handling asset URLs
 */

/**
 * Converts relative asset paths to absolute CDN URLs
 * @param assetPath - The relative path or full URL of an asset
 * @returns The full CDN URL for the asset
 */
export const getAssetUrl = (assetPath: string | null | undefined): string => {
  const CDN_BASE_URL = 'https://assets.dev.minicardiac.com';
  
  if (!assetPath) {
    // Return a default image from CDN if no path provided
    return `${CDN_BASE_URL}/public/default-avatar.jpg`;
  }

  // If it's already a full URL, check if it needs to be converted from S3 to CDN
  if (assetPath.startsWith('http')) {
    // Convert S3 URLs to CDN URLs
    if (assetPath.includes('s3-minicardiac-dev-assets.s3.amazonaws.com')) {
      return assetPath.replace('s3-minicardiac-dev-assets.s3.amazonaws.com', 'assets.dev.minicardiac.com');
    }
    return assetPath;
  }

  // For relative paths, add the CDN_BASE_URL
  const path = assetPath.startsWith('/') ? assetPath.substring(1) : assetPath;
  return `${CDN_BASE_URL}/${path}`;
}; 