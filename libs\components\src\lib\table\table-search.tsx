import React, { useCallback } from 'react';
// @mui
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
// components
import { Iconify } from '../iconify';
// ----------------------------------------------------------------------

type Props<T> = {
  filters?: T;
  onFilters?: (name: string, value: string) => void;
  loading: boolean;
};

export default function TableSearch<T extends { searchText?: string }>({
  filters,
  onFilters,
  loading,
}: Props<T>): React.ReactElement {
  const handleFilterSearchText = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      onFilters?.('searchText', event.target.value);
    },
    [onFilters]
  );

  return (
    <Stack
      spacing={2}
      alignItems={{ xs: 'flex-end', md: 'flex-start' }}
      direction={{
        xs: 'column',
        md: 'row',
      }}
      sx={{
        pr: { xs: 2.5, md: 1 },
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        flexGrow={1}
        sx={{ width: 1 }}
      >
        {filters && (
          <TextField
            fullWidth
            value={filters?.searchText || ''}
            onChange={handleFilterSearchText}
            placeholder="Search"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="eva:search-fill" sx={{ color: '#A24295' }} />
                </InputAdornment>
              ),
              endAdornment: loading && (
                <Iconify icon="svg-spinners:8-dots-rotate" />
              ),
            }}
          />
        )}
      </Stack>
    </Stack>
  );
}
