const TextIcon = ({ fill = '#A3A3A3', hoverFill = '#A24295', size = 24 }) => {
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M29.2 4H6.8C6.05739 4 5.3452 4.295 4.8201 4.8201C4.295 5.3452 4 6.05739 4 6.8V29.2C4 29.9426 4.295 30.6548 4.8201 31.1799C5.3452 31.705 6.05739 32 6.8 32H29.2C29.9426 32 30.6548 31.705 31.1799 31.1799C31.705 30.6548 32 29.9426 32 29.2V6.8C32 6.05739 31.705 5.3452 31.1799 4.8201C30.6548 4.295 29.9426 4 29.2 4ZM18.7933 24.4773C18.6887 24.5153 18.578 24.5343 18.4667 24.5333C18.276 24.534 18.0897 24.4761 17.9328 24.3677C17.7759 24.2592 17.6561 24.1053 17.5893 23.9267L16.5907 21.248H11.924L10.944 23.9267C10.9011 24.0419 10.8359 24.1475 10.7522 24.2375C10.6685 24.3276 10.5678 24.4002 10.456 24.4514C10.3442 24.5025 10.2235 24.5311 10.1006 24.5356C9.97775 24.54 9.85521 24.5202 9.74 24.4773C9.62479 24.4344 9.51915 24.3693 9.42913 24.2855C9.3391 24.2018 9.26644 24.1012 9.21531 23.9894C9.16418 23.8776 9.13556 23.7568 9.13111 23.6339C9.12665 23.5111 9.14643 23.3885 9.18933 23.2733L13.3893 12.0733C13.4547 11.8932 13.574 11.7375 13.731 11.6275C13.888 11.5174 14.075 11.4584 14.2667 11.4584C14.4583 11.4584 14.6454 11.5174 14.8023 11.6275C14.9593 11.7375 15.0786 11.8932 15.144 12.0733L19.344 23.2733C19.3873 23.3885 19.4074 23.5111 19.4031 23.6341C19.3988 23.7571 19.3703 23.878 19.3191 23.9899C19.2679 24.1018 19.1951 24.2024 19.1049 24.2861C19.0147 24.3698 18.9088 24.4347 18.7933 24.4773ZM26.8667 23.6C26.8667 23.8475 26.7683 24.0849 26.5933 24.26C26.4183 24.435 26.1809 24.5333 25.9333 24.5333C25.777 24.5295 25.6242 24.4865 25.4888 24.4082C25.3535 24.33 25.2399 24.2189 25.1587 24.0853C24.6935 24.3826 24.152 24.5383 23.6 24.5333H23.2453C22.4433 24.5333 21.6742 24.2147 21.107 23.6476C20.5399 23.0805 20.2213 22.3113 20.2213 21.5093C20.2213 20.7073 20.5399 19.9382 21.107 19.371C21.6742 18.8039 22.4433 18.4853 23.2453 18.4853H25V17.776C25.0126 17.3542 24.8576 16.9446 24.5691 16.6368C24.2805 16.329 23.8817 16.148 23.46 16.1333C23.1475 16.1366 22.8439 16.2378 22.592 16.4227C22.4941 16.5043 22.3805 16.5649 22.2582 16.601C22.136 16.637 22.0076 16.6476 21.8811 16.6321C21.7546 16.6166 21.6326 16.5754 21.5226 16.5109C21.4126 16.4465 21.317 16.3602 21.2417 16.2574C21.1663 16.1546 21.1128 16.0375 21.0845 15.9132C21.0562 15.7889 21.0536 15.6602 21.0769 15.5349C21.1002 15.4095 21.149 15.2903 21.2201 15.1846C21.2913 15.0789 21.3834 14.9888 21.4907 14.92C22.0593 14.4952 22.7502 14.266 23.46 14.2667C24.3768 14.2814 25.2502 14.6591 25.8889 15.317C26.5275 15.9749 26.8791 16.8592 26.8667 17.776V23.6Z"
        fill={hoverFill}
      />
      <path
        d="M25.0012 20.3519H23.2745C22.9676 20.3519 22.6732 20.4739 22.4562 20.6909C22.2391 20.9079 22.1172 21.2023 22.1172 21.5093C22.1172 21.8162 22.2391 22.1106 22.4562 22.3276C22.6732 22.5447 22.9676 22.6666 23.2745 22.6666H23.6012C23.875 22.6642 24.1368 22.5534 24.3292 22.3586L25.0012 21.7333V20.3519Z"
        fill={hoverFill}
      />
      <path
        d="M12.6406 19.3813H15.8886L14.2646 15.0599L12.6406 19.3813Z"
        fill={hoverFill}
      />
    </svg>
  );
};

export default TextIcon;
