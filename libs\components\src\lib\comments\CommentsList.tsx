import { Box, Typography, Avatar, Button } from '@mui/material';

interface Comment {
  id: string | number;
  user: {
    name: string;
    profilePic: string;
  };
  postedAgo: string;
  content: string;
}

interface CommentsListProps {
  comments: Comment[];
}

const CommentsList = ({ comments }: CommentsListProps) => {
  return (
    <Box
      flex={1}
      overflow="auto"
      display="flex"
      flexDirection="column"
      gap="8px"
      pr="8px"
    >
      {comments.map((comment) => (
        <Box key={comment.id} display="flex" gap="12px">
          <Avatar
            src={comment.user.profilePic}
            alt={comment.user.name}
            sx={{ width: 40, height: 40 }}
          />
          <Box flex={1} bgcolor="#F3F4F6" p="12px" borderRadius="8px">
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography fontSize="16px" fontWeight={600}>
                {comment.user.name}
              </Typography>
              <Typography
                fontSize="16px"
                fontWeight={300}
                color="text.secondary"
              >
                {comment.postedAgo}
              </Typography>
            </Box>

            <Typography mt="8px" fontSize="16px" fontWeight={400}>
              {comment.content}
            </Typography>

            <Box
              mt="8px"
              display="flex"
              gap="16px"
              width="138px"
              height="30px"
              alignItems="center"
              ml="8px"
            >
              <ActionButton label="Like" />
              <Dot />
              <ActionButton label="Reply" />
            </Box>
          </Box>
        </Box>
      ))}
    </Box>
  );
};

// Reusable Action Button
const ActionButton = ({ label }: { label: string }) => (
  <Button
    variant="text"
    sx={{
      p: 0,
      minWidth: 'auto',
      fontSize: '12px',
      fontWeight: 600,
      color: '#A24295',
      textTransform: 'none',
    }}
  >
    {label}
  </Button>
);

// Dot separator between Like and Reply
const Dot = () => (
  <Box
    sx={{
      width: '2px',
      height: '2px',
      backgroundColor: '#A3A3A3',
      borderRadius: '50%',
    }}
  />
);

export default CommentsList;
