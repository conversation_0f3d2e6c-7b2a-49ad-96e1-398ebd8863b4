'use client';

import { Box, useTheme } from '@mui/material';
import {
  PatientProfileWelcome,
  useSnackbar,
  FullPageLoader,
  SuccessAnimation,
  LoadingButton,
} from '@minicardiac-client/components';
import { ExtendedTheme } from '@minicardiac-client/components';
import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  useAuth,
  auth,
  useVerifySession,
} from '@minicardiac-client/apis';

import { useMutation } from '@tanstack/react-query';
import { axiosInstance } from '@minicardiac-client/apis';
import PatientProfileTextBox from '@/libs/components/src/lib/patient/PatientProfileTextBox';
import PatientProfilePic from '@/libs/components/src/lib/patient/PatientProfilePic';
import { PatientProfileRedirect } from '../../../../components/protected-route';
import { jwtDecode } from 'jwt-decode';
import { useTranslations } from 'next-intl';

// Custom hooks for profile operations
const useGenerateUploadUrl = () => {
  return useMutation({
    mutationFn: async ({
      mediaType,
      entityType,
    }: {
      mediaType: string;
      entityType: string;
    }) => {
      const response = await axiosInstance.post('/utils/generate-upload-url', {
        mediaType,
        entityType,
      });
      return response.data;
    },
  });
};

const useUploadFile = () => {
  return useMutation({
    mutationFn: async ({
      url,
      formData,
    }: {
      url: string;
      formData: FormData;
    }) => {
      // Log form data contents (excluding file binary data)
      const formDataEntries: Record<string, any> = {};
      formData.forEach((value, key) => {
        if (key === 'file' && value instanceof File) {
          formDataEntries[key] = {
            name: value.name,
            type: value.type,
            size: value.size,
          };
        } else {
          formDataEntries[key] = value;
        }
      });
    },
  });
};

const useUpdateProfile = () => {
  return useMutation({
    mutationFn: async (profileData: {
      introductoryStatement?: string;
      profileImageUrl?: string;
      profileImageUrlThumbnail?: string;
    }) => {
      const response = await axiosInstance.post(
        '/onboarding/profile-setup/public',
        profileData
      );
      return response.data;
    },
  });
};

export default function PatientProfilePage() {
  const theme = useTheme() as ExtendedTheme;
  const { showSuccess, showError } = useSnackbar();
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionQuery = useVerifySession();
  const t = useTranslations('signup')

  // Use the imported useAuth hook directly
  const { authState } = useAuth();
  const { isAuthenticated, user } = authState;

  const [introText, setIntroText] = useState('');
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false);
  const [isVerifyingSession, setIsVerifyingSession] = useState(false);

  // TanStack Query mutations
  const generateUploadUrlMutation = useGenerateUploadUrl();
  const uploadFileMutation = useUploadFile();
  const updateProfileMutation = useUpdateProfile();

  // Check if any mutation is loading
  const isUploading =
    generateUploadUrlMutation.isPending ||
    uploadFileMutation.isPending ||
    updateProfileMutation.isPending;

  // Users display name
  const displayName = searchParams?.get('name');

 
  const handleProfileImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleIntroTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setIntroText(e.target.value);
  };

  // Handle form submission with image upload and profile update
  const handleSubmit = async () => {
    if (typeof window === 'undefined') return;

    if (!introText.trim() && !selectedFile) {
      showError(
        'Please enter an introductory statement or select a profile image'
      );
      return;
    }

    if (!isAuthenticated || !user) {
      showError('You must be signed in to update your profile');
      return;
    }

    try {

      let profileImageUrl = '';

      if (selectedFile) {
        try {
          let retryCount = 0;
          const maxRetries = 3; // Increased from 2 to 3 retries
          let uploadSuccess = false;

          while (!uploadSuccess && retryCount <= maxRetries) {
            try {
              const uploadUrlResponse =
                await generateUploadUrlMutation.mutateAsync({
                  mediaType: selectedFile.type,
                  entityType: 'profile',
                });

              const uploadData = uploadUrlResponse.data;

              // 2. If the server already provided a fileUrl, use it directly
              if (uploadData && uploadData.fileUrl) {
                profileImageUrl = uploadData.fileUrl;
                uploadSuccess = true;
                break;
              }

              // 3. Validate the response
              if (!uploadData) {
                throw new Error(
                  'Failed to get upload URL from server: Empty response'
                );
              }

              if (!uploadData.url) {
                throw new Error(
                  'Failed to get upload URL from server: Missing URL'
                );
              }

              // 4. Create a FormData object for the upload
              const formData = new FormData();

              // Add all fields from the pre-signed URL response
              if (uploadData.fields) {
                Object.entries(uploadData.fields).forEach(([key, value]) => {
                  formData.append(key, value as string);
                });
              } else {
                console.warn(
                  '🔍 Profile Update - No fields provided in pre-signed URL response'
                );
              }

              // Then add the file as the last field
              formData.append('file', selectedFile);

              const key = uploadData.fields?.key || '';
              const bucket = uploadData.fields?.bucket || '';

              if (!key || !bucket) {
                throw new Error(
                  'Failed to get valid upload parameters from server'
                );
              }

              // Use fetch API for more direct upload with fewer overheads
              const uploadResponse = await fetch(uploadData.url, {
                method: 'POST',
                body: formData,
                // No custom headers - let the browser handle it
              });

              if (!uploadResponse.ok) {
                throw new Error(
                  `S3 upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`
                );
              }

              // Construct the final image URL
              profileImageUrl = `https://${bucket}.s3.amazonaws.com/${key}`;

              uploadSuccess = true;
              break;
            } catch (error: any) {
              retryCount++;

              // Check if the error is due to policy expiration
              const isPolicyExpired =
                error?.response?.data?.includes('Policy expired') ||
                error?.message?.includes('Policy expired') ||
                (typeof error === 'string' &&
                  error.includes('Policy expired')) ||
                (error instanceof Error &&
                  error.message.includes('Policy expired')) ||
                (error instanceof Error &&
                  error.toString().includes('Policy expired'));

              if (isPolicyExpired && retryCount <= maxRetries) {
                await new Promise((resolve) => setTimeout(resolve, 500));
              } else {
                throw error;
              }
            }
          }

          if (!uploadSuccess) {
            throw new Error('Failed to upload image after multiple attempts');
          }
        } catch (error) {
          showError('Failed to upload image. Please try again later.');
          return;
        }
      }

      // Prepare profile data
      const profileData = {
        introductoryStatement: introText,
        ...(profileImageUrl && {
          profileImageUrl,
          profileImageUrlThumbnail: profileImageUrl,
        }),
      };

      // Update profile using TanStack Query
      await updateProfileMutation.mutateAsync(profileData);
      await sessionQuery.refetch();

      const user = auth?.currentUser;
      let decodedToken: any = null;

      if (user) {
        const token = await user.getIdToken(true); // Force refresh token
        decodedToken = jwtDecode(token || '');
      }

      if (decodedToken?.currentStage === 'completed') {
        // Show success message
        setShowSuccessAnimation(true);
        showSuccess('Profile updated successfully!');

        setTimeout(() => {
          setShowSuccessAnimation(false);
          router.push('/feed?fromSignup=true');
        }, 1000);
      } else {
        console.warn(
          'Token did not reflect currentStage = completed. Staying on profile page.'
        );
      }
    } catch (error) {
      console.error('Error during profile update:', error);

      // Simplified error handling
      const err = error as {
        response?: {
          status: number;
          data?: { message?: string };
        };
        message?: string;
      };

      if (
        err.response &&
        (err.response.status === 401 || err.response.status === 440)
      ) {
        showError('Your session has expired. Please sign in again.');
      } else if (err.response && err.response.status === 400) {
        const errorMessage =
          err.response.data?.message ||
          'Invalid profile data. Please check your inputs.';
        showError(errorMessage);
      } else if (err.response && err.response.status >= 500) {
        showError('Server error. Please try again later.');
      } else {
        showError('Something went wrong. Please try again later.');
      }
    }
  };

  const clearPreview = () => {
    setPreviewUrl(null);
    setSelectedFile(null);
  };

  const isLoading = isUploading || isVerifyingSession;
  const loadingMessage = isUploading
    ? 'Updating your profile...'
    : isVerifyingSession
    ? 'Verifying your session...'
    : '';

  return (
    <PatientProfileRedirect>
      <Box>
        {showSuccessAnimation && <SuccessAnimation show={true} />}
        {isLoading && <FullPageLoader open={true} message={loadingMessage} />}

        <Box
          sx={{
            position: 'relative',
            width: '100%',
            minHeight: '100vh',
            backgroundColor: theme.palette.background.default,
            pb: '20px',
          }}
        >
          {/* Welcome message */}
          <PatientProfileWelcome
            title={t('title')}
            patientName={displayName || ''}
            subtitle={t('subtitle') || ''}
          />

          {/* Introductory Statement Section */}
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              mt: { xs: '40px', md: '112px', xxl: '120px' },
              px: { xs: '16px', md: '70px', xxl: '100px' },
              gap: { xs: '40px', md: '74px', xxl: '90px' },
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                alignItems: { xs: 'center', md: 'flex-start' },
                justifyContent: { md: 'center' },
                width: '100%',
                position: 'relative',
                zIndex: 1,
                gap: { xs: '56px', md: '74px', xxl: '90px' },
                paddingX: { xs: '0px', md: '40px', xxl: '60px' },
                paddingY: { xs: 0, md: '56px', xxl: '70px' },
                backgroundColor: '#FFFFFF',
                borderRadius: '12px',
                boxShadow: {
                  xs: 'none',
                  md: '0px 4px 12px rgba(0, 0, 0, 0.08)',
                },
              }}
            >
              <PatientProfilePic
                theme={theme}
                previewUrl={previewUrl}
                handleProfileImageChange={handleProfileImageChange}
                clearPreview={clearPreview}
                patientName={displayName || ''}
                changePhotoLabel={t('profileSetupForm.changePhotoLabel')}
                addProfilePicture={t('profileSetupForm.addProfilePicture')}
                removePhotoLabel={t('profileSetupForm.removePhotoLabel')}
              />
              <PatientProfileTextBox
                introText={introText}
                introLabel={t('profileSetupForm.introductoryStatement')}
                placeholder={t('profileSetupForm.introductoryStatementPlaceholder')}
                handleIntroTextChange={handleIntroTextChange}
                handleSubmit={handleSubmit}
                isUploading={isUploading}
                selectedFile={selectedFile}
                theme={theme}
              />
            </Box>

            {/* Loading Button */}
            <LoadingButton
              fullWidth
              variant="contained"
              onClick={handleSubmit}
              loadingText={t('profileSetupForm.loadingText')}
              disabled={isUploading || introText.trim() === ''}
              sx={(theme: any) => ({
                gap: (theme as ExtendedTheme).customValues.button?.spacing,
                backgroundColor:
                  introText.trim() && (theme.palette as any).secondary.main,
                color: '#FFFFFF',
                '&:hover': {
                  backgroundColor:
                    introText.trim() && (theme.palette as any).secondary.dark,
                },
                mt: { xs: 0, md: '40px', xxl: '48px' },
                textTransform: 'none',
                borderRadius: '8px',
                fontWeight: 500,
                fontSize: '14px',
                width: {
                  xs: '280px',
                  sm: '320px',
                  lg: '400px',
                  xxl: '480px',
                },
              })}
            >
              {t('profileSetupForm.saveAndContinue')}
            </LoadingButton>
          </Box>
        </Box>
      </Box>
    </PatientProfileRedirect>
  );
}
