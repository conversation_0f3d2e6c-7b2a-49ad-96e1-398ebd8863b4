{"name": "@minicardiac-client/components", "version": "0.0.1", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "dependencies": {"@minicardiac-client/types": "workspace:*", "@minicardiac-client/shared": "workspace:*", "@minicardiac-client/utilities": "workspace:*", "@minicardiac-client/constants": "workspace:*", "@minicardiac-client/apis": "workspace:*"}}