'use client';

import { useEffect } from 'react';
import { useAuth, verifySession } from '@minicardiac-client/apis';
import { useQuery } from '@tanstack/react-query';

/**
 * Simplified SessionVerifier that works with centralized middleware
 * Middleware handles route-level authentication, this handles session sync
 */
const useSessionVerification = () => {
  const { authState } = useAuth();

  const sessionQuery = useQuery({
    queryKey: ['auth', 'session'],
    queryFn: async () => {
      if (!authState.isAuthenticated || !authState.user) {
        throw new Error('User not authenticated');
      }
      return await verifySession();
    },
    enabled: authState.isAuthenticated && !!authState.user,
    retry: 1, 
    refetchInterval: 15 * 60 * 1000, 
    refetchOnWindowFocus: false,
    refetchOnMount: false // Prevent unnecessary refetches
  });

  useEffect(() => {
    // Handle session errors - let middleware handle redirects
    if (sessionQuery.isError && authState.isAuthenticated) {
      console.warn('Session verification failed, middleware will handle redirect');
      // Don't call router.refresh() as it can cause infinite loops
      // Let the middleware handle authentication redirects naturally
    }
  }, [sessionQuery.isError, authState.isAuthenticated]);

  return {
    isLoading: sessionQuery.isLoading,
    isError: sessionQuery.isError,
    data: sessionQuery.data
  };
};

export const SessionVerifier = () => {
  useSessionVerification();
  return null;
};

export default SessionVerifier;


