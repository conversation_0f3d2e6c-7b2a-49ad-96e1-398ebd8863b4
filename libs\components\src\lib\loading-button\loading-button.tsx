'use client';

import React from 'react';
import { Button, ButtonProps, CircularProgress, Box } from '@mui/material';
import { useTheme } from '@emotion/react';

interface LoadingButtonProps extends ButtonProps {
  loading?: boolean;
  loadingText?: string;
  sx?: any;
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  children,
  loading = false,
  loadingText,
  disabled,
  startIcon,
  sx,
  ...props
}) => {
  const theme = useTheme();

  const evaluatedSx = typeof sx === 'function' ? sx(theme) : sx;

  return (
    <Button
      {...props}
      disabled={loading || disabled}
      startIcon={loading ? null : startIcon}
      fullWidth
      variant="contained"
      sx={(theme) => ({
        ...evaluatedSx,
        '&.Mui-disabled': {
          backgroundColor: (theme.palette as any).neutral?.[500],
          color: 'white',
        },
        '& .MuiButton-startIcon': {
          marginRight: {
            xs: 0,
            sm: '8px',
          },
          marginLeft: {
            xs: 0,
            sm: '-4px',
          },
        },
        borderRadius: '8px',
      })}
    >
      {loading ? (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <CircularProgress size={20} color="inherit" />
          {loadingText || children}
        </Box>
      ) : (
        children
      )}
    </Button>
  );
};

export default LoadingButton;
