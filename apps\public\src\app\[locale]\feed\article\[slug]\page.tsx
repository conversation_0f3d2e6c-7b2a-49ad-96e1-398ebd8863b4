'use client';

import { useEffect } from 'react';
import { useParams } from 'next/navigation';
import ArticlePostFull from '@/libs/components/src/lib/article-post/ArticlePostFull';
import { FullPageLoader } from '@minicardiac-client/components';
import { useAuth } from '@minicardiac-client/apis';

export default function ArticlePageWrapper() {
  const { authState } = useAuth();
  const params = useParams();

  useEffect(() => {
    document.title = 'Article | MiniCardiac';
  }, []);

  if (authState.isLoading) {
    return <FullPageLoader open={true} />;
  }

  const slugParam = params?.slug;

  // Ensure `slugParam` is a string before using split
  const slug = Array.isArray(slugParam) ? slugParam.join('-') : slugParam;
  const articleId = slug?.split('-').pop();

  return <ArticlePostFull articleId={articleId} slug={slug} />;
}
