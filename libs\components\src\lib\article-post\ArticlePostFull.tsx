'use client';
import { Box } from '@mui/material';
import { useEffect, useRef, useState } from 'react';
import PostHeader from '../content-posts/PostHeader';
import ArticleCover from './ArticleCover';
import ArticleContent from './ArticleContent';
import CommentsSection from './CommentsSection';
import AddCommentBar from './AddCommentBar';
import OtherArticlesSidebar from './OtherArticlesSidebar';
import { mockComments, otherArticles } from './mockData';
interface ArticlePostFullProps {
  articleId?: string;
  slug?: string;
}

const ArticlePostFull = ({ articleId, slug }: ArticlePostFullProps) => {
  const [showAddComment, setShowAddComment] = useState(false);
  const endOfArticleRef = useRef<HTMLDivElement | null>(null);

  const user = {
    name: '<PERSON>',
    profilePic: '/placeholder-avatar.png',
    postedAgo: 'just now',
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setShowAddComment(entry.isIntersecting);
      },
      {
        root: null,
        threshold: 0.1,
      }
    );

    if (endOfArticleRef.current) {
      observer.observe(endOfArticleRef.current);
    }

    return () => {
      if (endOfArticleRef.current) {
        observer.unobserve(endOfArticleRef.current);
      }
    };
  }, []);

  return (
    <Box
      display="flex"
      flexDirection="column"
      gap={{ xs: '20px', sm: '40px' }}
      width="100%"
      py={{ xs: '16px', sm: '40px' }}
    >
      <Box px={{ xs: '16px', sm: '40px' }}>
        <PostHeader user={user} showOptions showBackButton />
      </Box>

      <Box
        display="flex"
        justifyContent={'center'}
        width={'100%'}
        gap="40px"
        px={{ xs: '16px', sm: '40px' }}
      >
        <Box width={824}>
          <ArticleCover />
          <ArticleContent />

          <div ref={endOfArticleRef} style={{ height: '1px' }} />

          <CommentsSection comments={mockComments} />

          {showAddComment && <AddCommentBar />}
        </Box>

        <OtherArticlesSidebar articles={otherArticles} />
      </Box>
    </Box>
  );
};

export default ArticlePostFull;
