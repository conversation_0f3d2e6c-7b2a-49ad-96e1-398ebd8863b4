import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';
import { Iconify } from '../iconify';
import { Box } from '@mui/material';
import { Dayjs } from 'dayjs';

interface CustomDateCalendarProps {
  selectedDate: Dayjs | null;
  setSelectedDate: (date: Dayjs | null) => void;
}

const CustomDateCalendar = ({
  selectedDate,
  setSelectedDate,
}: CustomDateCalendarProps) => {
  return (
    <Box
      sx={{
        width: 320,
        '& .MuiPickersCalendarHeader-root': {
          marginTop: '10px',
          alignItems: 'center',
        },
        '& .MuiDayCalendar-weekDayLabel': {
          color: '#A3A3A3',
          fontWeight: 400,
          fontSize: '12px',
          fontFamily: 'Plus Jakarta Sans',
          marginX: '1px',
        },
        '& .MuiYearCalendar-root': {
          height: '200px',
        },
        '& .MuiPickersDay-root': { marginX: '1px' },
        // Selected year styling
        '& .MuiYearCalendar-button': {
          fontSize: '14px',
        },
        '& .MuiYearCalendar-button.Mui-selected': {
          backgroundColor: '#A24295',
          color: '#fff',
        },
        '& .MuiYearCalendar-button.Mui-selected:hover': {
          backgroundColor: '#922d7f',
        },

        // Selected month styling
        '& .MuiMonthCalendar-button': {
          fontSize: '14px',
        },
        '& .MuiMonthCalendar-button.Mui-selected': {
          backgroundColor: '#A24295',
          color: '#fff',
        },
        '& .MuiMonthCalendar-button.Mui-selected:hover': {
          backgroundColor: '#922d7f',
        },
      }}
    >
      <DateCalendar
        value={selectedDate}
        onChange={(newDate) => setSelectedDate(newDate)}
        views={['year', 'month', 'day']}
        slots={{
          leftArrowIcon: () => (
            <Iconify
              icon="solar:alt-arrow-left-line-duotone"
              style={{ height: '36px', width: '36px' }}
              sx={{ color: '#A24295', p: '0' }}
            />
          ),
          rightArrowIcon: () => (
            <Iconify
              icon="solar:alt-arrow-right-line-duotone"
              style={{ height: '36px', width: '36px' }}
              sx={{ color: '#A24295', p: '0' }}
            />
          ),
        }}
        slotProps={{
          calendarHeader: {
            sx: {
              '& .MuiPickersCalendarHeader-label': {
                color: '#1E1E1E',
                fontWeight: 600,
                fontSize: '16px',
              },
            },
            slots: {},
          },

          day: {
            sx: (theme) => ({
              fontSize: '14px',
              fontWeight: 400,
              '&.Mui-selected': {
                backgroundColor: '#A24295 !important',
                color: '#fff !important',
              },
              '&.Mui-selected:hover': {
                backgroundColor: '#922d7f !important', // slightly darker on hover
              },
            }),
          },
        }}
      />
    </Box>
  );
};

export default CustomDateCalendar;
