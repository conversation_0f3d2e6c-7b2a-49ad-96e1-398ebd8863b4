import React from 'react';
import {
  TableBody,
  TableRow,
  TableCell,
  Button,
  Typography,
} from '@mui/material';

type DocumentItem = {
  label: string;
  type: string;
  url: string;
};

interface DocumentTableBodyProps {
  documentList: DocumentItem[];
  handlePreview: (doc: DocumentItem) => void;
  downloadFile: (url: string, label: string) => void;
}

const DocumentTableBody: React.FC<DocumentTableBodyProps> = ({
  documentList,
  handlePreview,
  downloadFile,
}) => {
  return (
    <TableBody>
      {documentList.map((doc) => (
        <TableRow key={doc.label}>
          <TableCell>{doc.label}</TableCell>
          <TableCell>
            {doc.url ? (
              <Typography color="success.main">Uploaded</Typography>
            ) : (
              <Typography color="error.main">Not Uploaded</Typography>
            )}
          </TableCell>
          <TableCell>{doc.type}</TableCell>
          <TableCell>
            {doc.url && (
              <>
                <Button
                  size="small"
                  onClick={() => handlePreview(doc)}
                  sx={{
                    backgroundColor: '#F1E3EF',
                    color: 'gray',
                    mr: 1,
                    '&:hover': {
                      backgroundColor: '#A24295',
                      color: 'white',
                    },
                  }}
                >
                  View
                </Button>
                <Button
                  size="small"
                  onClick={() => downloadFile(doc.url, doc.label)}
                  sx={{
                    backgroundColor: '#F1E3EF',
                    color: 'gray',
                    '&:hover': {
                      backgroundColor: '#A24295',
                      color: 'white',
                    },
                  }}
                >
                  Download
                </Button>
              </>
            )}
          </TableCell>
        </TableRow>
      ))}
    </TableBody>
  );
};

export default DocumentTableBody;
