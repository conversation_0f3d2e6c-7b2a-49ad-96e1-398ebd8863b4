import {
  Box,
  Divider,
  List<PERSON>temIcon,
  Menu,
  MenuItem,
  Typography,
} from '@mui/material';

import { Iconify } from '../iconify';
import { useState } from 'react';
import { Logout } from '@mui/icons-material';
import { Profile } from './Profile';
import { useSignOut } from '@minicardiac-client/apis';
import { useRouter } from 'next/navigation';

export const FeedProfile = ({
  userDetails,
}: {
  userDetails: {
    name: string;
    photoURL: string;
  } | null;
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const router = useRouter();

  const { mutate: signOut } = useSignOut();

  const handleClose = async () => {
    setAnchorEl(null);
  };

  const handleSignout = async () => {
    setAnchorEl(null);
    try {
      await signOut(undefined, {
        onSuccess: () => {
          router.push('/signin');
        },
      });
    } catch (error) {
      console.error('Error during sign out:', error);
      router.push('/signin');
    }
  };

  return (
    <Box
      sx={{
        textAlign: 'center',
        mt: { xs: 0, smd: '29px' },
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
      }}
    >
      <Box onClick={handleClick}>
        <Profile photoURL={userDetails?.photoURL} />
      </Box>

      <Box
        onClick={handleClick}
        sx={{
          display: { xs: 'none', smd: 'flex' },
          gap: { sm: '2px', xs: '2px', md: '4px' },
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          transition: 'transform 0.2s ease-in-out',
          '&:hover': {
            transform: 'scale(1.05)',
          },
          flexDirection: { sm: 'column', xs: 'column', md: 'row' },
          textAlign: 'center',
          height: '20px',
        }}
      >
        <Typography
          sx={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 700,
            fontSize: { sm: '10px', xs: '10px', md: '16px' },
            color: '#A24295',
          }}
        >
          {userDetails && userDetails.name}
        </Typography>
        <Box
          sx={{
            transition: 'transform 0.3s ease',
            transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
          }}
        >
          <Iconify icon="material-symbols:chevron-right" color="#A24295" />
        </Box>
      </Box>
      {/* Dropdown Menu */}
      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        slotProps={{
          paper: {
            elevation: 0,
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleClose}>
          <Box
            sx={{
              display: 'flex',
              width: 'fit-content',
              gap: '10px',
              alignItems: 'center',
              textAlign: 'center',
            }}
          >
            <Profile size={35} />
            <Typography
              sx={{
                fontSize: '14px',
              }}
            >
              Profile
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem onClick={handleClose}>
          <Box
            sx={{
              display: 'flex',
              width: 'fit-content',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            <Profile displayName="Asmongold" size={35} />
            <Typography
              sx={{
                fontSize: '14px',
              }}
            >
              My account
            </Typography>
          </Box>
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleSignout} sx={{ py: '16px' }}>
          <ListItemIcon>
            <Logout fontSize="small" />
          </ListItemIcon>
          <Typography
            sx={{
              fontSize: '14px',
            }}
          >
            Logout
          </Typography>
        </MenuItem>
      </Menu>
    </Box>
  );
};
