'use client';

import AuthTabContent from '../components/AuthTabContent';
import AuthLayout from '../components/AuthLayout';
import Welcome from '../components/Welcome';
import Subtitle from '../components/Subtitle';
import AuthTabs from '../components/AuthTabs';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Box } from '@mui/material';

interface SignupTypePageProps {
  onBack: () => void;
}

/**
 * SignupTypePage component for selecting user type during signup
 */
export const SignupTypePage = ({ onBack }: SignupTypePageProps) => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(1);

  const handleNavigate = (path: string) => {
    router.push(path);
  };

  const handleTypeSelection = (path: string) => {
    handleNavigate(path);
  };

  // Handle tab change
  const handleTabChange = (newTab: number) => {
    setActiveTab(newTab);

    if (newTab === 0) {
      setTimeout(() => {
        router.push('/signin');
      }, 500);
    }
  };

  return (
    <AuthLayout
      showBackButton={true}
      onBackClick={onBack}
      activeTab={activeTab}
    >
      <Box
        sx={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          mt: { xs: '40px', sm: '40px', xxl: '100px' },
          pt: { xs: '12px', sm: '0px' },
        }}
      >
        <Welcome title="Welcome to" />
        <Subtitle text="The heart of cardiac healthcare" />
        <AuthTabs 
          value={activeTab} 
          onChange={handleTabChange}
          signInLabel="Sign in"
          signUpLabel="Sign up"
        />
      </Box>
      <AuthTabContent
        activeTab={activeTab}
        onTypeSelect={handleTypeSelection}
      />
    </AuthLayout>
  );
};

export default SignupTypePage;
