'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogContent,
  IconButton,
  Typography,
  Box,
  CircularProgress,
  
} from '@mui/material';
import  { useState, } from 'react';
import Iconify from '../../iconify/iconify';
import { useGetSubscriptionPlans, useCreateSubscription } from '@minicardiac-client/apis';
import { FullPageLoader } from '../../full-page-loader/full-page-loader';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

interface StudentSignupModalProps {
  open: boolean;
  onClose: () => void;
  onContinue: (subscriptionPlanId: string) => void;
}

const StudentSignupModal = ({
  open,
  onClose,
  onContinue,
}: StudentSignupModalProps) => {
  const [error, setError] = useState<string | null>(null);
  const [isCreatingSubscription, setIsCreatingSubscription] = useState(false);

  const {
    data: subscriptionPlans,
    isLoading,
    error: fetchError,
  } = useGetSubscriptionPlans('STUDENT', { enabled: open });

  const { mutateAsync: createSubscription } = useCreateSubscription();

  // Get the student plan (should be only one)
  const studentPlan = subscriptionPlans?.[0];

  const handleContinue = async () => {
    if (!studentPlan) {
      setError('Student plan not found. Please try again later.');
      return;
    }

    setIsCreatingSubscription(true);
    setError(null);

    try {
      // Create subscription for the student plan
      await createSubscription({
        subscriptionPlanId: studentPlan.id,
        isYearly: false,
        professionalCategory: 'STUDENT'
      });

      // Call the onContinue callback with the plan ID
      onContinue(studentPlan.id);
    } catch (err) {
      console.error('Error creating subscription:', err);
      setError('Failed to create subscription. Please try again.');
    } finally {
      setIsCreatingSubscription(false);
    }
  };

  if (isLoading) {
    return (
      <FullPageLoader open={true} />
    );
  }

  if (fetchError || !studentPlan) {
    return (
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth={false}
        PaperProps={{
          sx: {
            width: '1150px',
            height: '754px',
            position: 'absolute',
            top: '39px',
            left: '65px',
            borderRadius: '8px',
          },
        }}
      >
        <DialogContent sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '700px' }}>
          <Typography variant="h6" color="error" align="center" sx={{ mb: 3 }}>
            Unable to load student plan. Please try again later.
          </Typography>
          <Button variant="outlined" onClick={onClose}>
            Close
          </Button>
        </DialogContent>
      </Dialog>
    );
  }

  return (
   <Dialog
     open={open}
     onClose={onClose}
     maxWidth={false}
     PaperProps={{
       sx: {
         width: '1150px', // Or adjust as needed, e.g., 'auto' or a percentage
         height: '754px', // Or adjust as needed, e.g., 'auto'
         borderRadius: '8px',
         // The Dialog will now be centered by default
       },
     }}
   >
      <Box sx={{ position: 'relative', p: 4 }}>
        <Typography
          variant="h5"
          component="div"
          align="center"
          sx={{
            fontWeight: 600,
            fontSize: '24px',
            color: '#333537',
            mb: 2,
          }}
        >
          Welcome to the MiniCardiac Student Plan
        </Typography>

        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 16,
            top: 16,
            color: '#A24295',
          }}
        >
          <Iconify
            icon="material-symbols:close-rounded"
            sx={{
              color: '#A24295',
              fontSize: '24px',
            }}
          />
        </IconButton>

        <Typography
          variant="body1"
          sx={{
            color: '#666',
            mb: 3,
            fontSize: '16px',
            textAlign: 'center',
            maxWidth: '800px',
            mx: 'auto',
          }}
        >
          As a medical student, you're the future of our community, and we're eager to support your career advancement.
          This is why we have the Student plan, offering greater access and opportunities than what would be afforded to a Professional on the free plan.
          When you're ready to join the workforce, you can convert your account into any tier of a Professional subscription.
        </Typography>

        <Typography
          variant="body1"
          sx={{
            color: '#666',
            mb: 3,
            fontSize: '16px',
            textAlign: 'center',
          }}
        >
          Your plan would include:
        </Typography>

        {/* Student Plan Card */}
        <Box
          sx={{
            border: '1px solid #E0E0E0',
            borderRadius: '8px',
            p: 3,
            mb: 4,
            backgroundColor: '#FFFFFF',
            maxWidth: '600px',
            mx: 'auto',
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  fontSize: '24px',
                  color: '#333537',
                }}
              >
                Student Plan
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: '#666',
                  fontSize: '14px',
                }}
              >
                Advance your career
              </Typography>
            </Box>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 700,
                fontSize: '32px',
                color: '#333537',
              }}
            >
              ${studentPlan.priceMonthly}
            </Typography>
          </Box>

          {/* Plan Features */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 3 }}>
            {studentPlan.planFeatures?.map((feature: { subscriptionFeature: { name: string } }, index: number) => (
              <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                <CheckCircleIcon sx={{ color: '#4CAF50', fontSize: 20 }} />
                <Typography
                  variant="body1"
                  sx={{
                    fontSize: '16px',
                    color: index === 2 ? '#A24295' : 'inherit', // Highlight the 3rd item (posts per month) in purple
                    fontWeight: index === 2 ? 500 : 400,
                  }}
                >
                  {feature.subscriptionFeature.name}
                  {index === 2 && ' (student tag only)'}
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>

        <Typography
          variant="body2"
          sx={{
            color: '#666',
            mb: 4,
            fontSize: '14px',
            textAlign: 'center',
            maxWidth: '800px',
            mx: 'auto',
          }}
        >
          Please note that in the next step, we will require some proof that you are a medical student to confirm your eligibility for this plan.
          Any ID, certificate, or acceptance letter would do.
        </Typography>

        {/* Error message */}
        {error && (
          <Typography
            variant="body2"
            color="error"
            align="center"
            sx={{ mb: 2 }}
          >
            {error}
          </Typography>
        )}

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, mt: 2 }}>
          <Button
            variant="outlined"
            onClick={onClose}
            disabled={isCreatingSubscription}
            sx={{
              borderColor: '#D1D5DB',
              color: '#6B7280',
              '&:hover': {
                borderColor: '#9CA3AF',
                backgroundColor: 'rgba(156, 163, 175, 0.04)',
              },
              width: '120px',
              height: '48px',
              borderRadius: '4px',
              textTransform: 'none',
              fontWeight: 500,
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => handleContinue()}
            disabled={isCreatingSubscription}
            sx={{
              backgroundColor: '#A24295',
              color: 'white',
              '&:hover': {
                backgroundColor: '#8E3A82',
              },
              width: 315,                     // Figma: width: 315
    height: 40,                     // Figma: height: 40
    borderRadius: '8px',            // Figma: border-radius: 8px
    paddingLeft: '40px',            // Figma: padding-left: 40px
    paddingRight: '40px',           // Figma: padding-right: 40px
              fontWeight: 500,
              fontSize: '16px',
            }}
          >
            {isCreatingSubscription ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CircularProgress size={20} color="inherit" />
                <span>Processing...</span>
              </Box>
            ) : (
              'Continue as a Medical Student'
            )}
          </Button>
        </Box>
      </Box>
    </Dialog>
  );
};

export default StudentSignupModal;
