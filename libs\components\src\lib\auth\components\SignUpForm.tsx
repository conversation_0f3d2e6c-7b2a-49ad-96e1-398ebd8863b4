'use client';

import { Stack, <PERSON>ert, Box } from '@mui/material';
import { TextField } from '../../components/common/TextField';
import { ExtendedTheme } from '../types/auth.types';
import PasswordField from './PasswordField';
import OrDivider from './OrDivider';
import SocialLoginButton from './SocialLoginButton';
import { LoadingButton } from '../../loading-button';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

interface SignUpFormProps {
  userType: 'professional' | 'organization' | 'patient';
  onSubmit?: (data: {
    email: string;
    password: string;
    displayName?: string;
    organizationName?: string;
  }) => void;
  isLoading?: boolean;
  error?: string | null;
  showPassword?: boolean;
  showConfirmPassword?: boolean;
  onTogglePasswordVisibility?: (field: 'password' | 'confirmPassword') => void;
  emailLabel?: string;
  emailPlaceholder?: string;
  passwordLabel?: string;
  passwordPlaceholder?: string;
  emailErrorMessage?: string;
  passwordErrorMessage?: string;
  continueLabel?: string;
  signUpLoadingText?: string;
  orLabel?: string;
  googleLabel?: string;
  appleLabel?: string;
  forgotPasswordLinkText?: string;
  signInButtonText?: string;
  googleSignInButtonText?: string;
  appleSignInButtonText?: string;
  organizationNameLabel?: string;
  organizationNamePlaceholder?: string;
  displayNameLabel?: string;
  namePlaceholder?: string;
}

export const SignUpForm = ({
  userType,
  onSubmit,
  isLoading = false,
  error,
  showPassword = false,
  showConfirmPassword = false,
  emailLabel = 'Email',
  emailPlaceholder = '<EMAIL>',
  passwordLabel = 'Password',
  passwordPlaceholder = '*******',
  emailErrorMessage = 'Please enter a valid email',
  passwordErrorMessage = 'Password is required',
  continueLabel = 'Continue',
  signUpLoadingText = 'Signing up...',
  orLabel = 'OR',
  googleLabel = 'Continue with Google',
  appleLabel = 'Continue with Apple',
  forgotPasswordLinkText = 'Forgot Password?',
  signInButtonText = 'Sign In',
  googleSignInButtonText = 'Sign in with Google',
  appleSignInButtonText = 'Sign in with Apple',
  organizationNameLabel = 'Name of the Organisation',
  organizationNamePlaceholder = 'Name of organisation',
  displayNameLabel = 'Display Name',
  namePlaceholder = 'Name',
  onTogglePasswordVisibility = () => { /* no-op */ },
}: SignUpFormProps) => {
  const getSchema = () => {
    const baseSchema = {
      email: yup
        .string()
        .email(emailErrorMessage)
        .required(emailErrorMessage),
      password: yup.string().required(passwordErrorMessage),
    };

    if (userType === 'professional' || userType === 'patient') {
      return yup.object({
        ...baseSchema,
        displayName: yup
          .string()
          .required('Display name is required')
          .min(2, 'Name must be at least 2 characters'),
      });
    } else if (userType === 'organization') {
      return yup.object({
        ...baseSchema,
        organizationName: yup
          .string()
          .required('Organization name is required')
          .min(2, 'Name must be at least 2 characters'),
      });
    }

    return yup.object(baseSchema);
  };

  const {
    register,
    handleSubmit: hookFormSubmit,
    formState: { errors, isValid },
  } = useForm<{
    email: string;
    password: string;
    displayName?: string;
    organizationName?: string;
  }>({
    resolver: yupResolver(getSchema()),
    mode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
      displayName: '',
      organizationName: '',
    },
  });

  const onFormSubmit = (data: any) => {
    if (onSubmit) {
      onSubmit({
        email: data.email,
        password: data.password,
        displayName:
          userType === 'professional' || userType === 'patient'
            ? data.displayName
            : undefined,
        organizationName:
          userType === 'organization' ? data.organizationName : undefined,
      });
    }
  };

  return (
    <Stack
      sx={{
        width: {
          xs: '280px',
          sm: '320px',
          md: '320px',
          lg: '400px',
          xxl: '460px',
        },
      }}
    >
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          gap: { xs: '40px', sm: '24px' },
        }}
      >
        {userType === 'organization' && (
          <TextField
            fullWidth
            label={organizationNameLabel}
            placeholder={organizationNamePlaceholder}
            variant="outlined"
            size="small"
            {...register('organizationName')}
            error={!!errors.organizationName}
            helperText={errors.organizationName?.message}
            disabled={isLoading}
            InputLabelProps={{ shrink: true }}
          />
        )}

        {(userType === 'professional' || userType === 'patient') && (
          <TextField
            label={displayNameLabel}
            placeholder={namePlaceholder}
            size="small"
            {...register('displayName')}
            error={!!errors.displayName}
            helperText={errors.displayName?.message}
            disabled={isLoading}
          />
        )}

        <TextField
          label={emailLabel}
          placeholder={emailPlaceholder}
          size="small"
          {...register('email')}
          error={!!errors.email}
          helperText={errors.email?.message}
          disabled={isLoading}
        />

        <PasswordField
          label={passwordLabel}
          {...register('password')}
          error={!!errors.password}
          helperText={errors.password?.message}
          disabled={isLoading}
          showPassword={showPassword}
          onToggleVisibility={() => onTogglePasswordVisibility('password')}
        />

        <LoadingButton
          fullWidth
          variant="contained"
          onClick={hookFormSubmit(onFormSubmit)}
          loading={isLoading}
          loadingText={signUpLoadingText}
          sx={(theme: any) => ({
            gap: (theme as ExtendedTheme).customValues.button?.spacing,
            backgroundColor: isValid
              ? (theme.palette as any).secondary.main
              : (theme.palette as any).primary.main,
            '&:hover': {
              backgroundColor: isValid
                ? (theme.palette as any).secondary.dark
                : (theme.palette as any).primary.dark,
            },
            borderRadius: '8px',
          })}
          disabled={!isValid || isLoading}
        >
          {continueLabel}
        </LoadingButton>
      </Box>

      <OrDivider />

      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'row', sm: 'column' },
          justifyContent: 'center',
          gap: { xs: '40px', sm: '16px' },
        }}
      >
        <SocialLoginButton provider="google" disabled={isLoading} label={googleLabel} />
        <SocialLoginButton provider="apple" disabled={isLoading} label={appleLabel} />
      </Box>
    </Stack>
  );
};

export default SignUpForm;
