'use client';

import React, { useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import useEmblaCarousel from 'embla-carousel-react';
import type { EmblaOptionsType, EmblaCarouselType } from 'embla-carousel';
import { SubscriptionOption } from '@minicardiac-client/types';
import SubscriptionPlanCard from '../SubscriptionPlanCard';
import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from './EmblaCarouselButton';

type PropType = {
  isExpanded: boolean;
  options?: EmblaOptionsType;
  onSelectPlan: (plan: number) => void;
  selectedPlan: number | null;
  subscriptionOptions: SubscriptionOption[];
  billingCycle?: 'monthly' | 'yearly';
};

const EmblaCarousel: React.FC<PropType> = ({
  options,
  onSelectPlan,
  selectedPlan,
  isExpanded,
  subscriptionOptions,
  billingCycle = 'monthly',
}) => {
  const [emblaRef, emblaApi] = useEmblaCarousel(options);

  const onSelect = useCallback(
    (api: EmblaCarouselType) => {
      if (isExpanded) {
        onSelectPlan(api.selectedScrollSnap());
      }
    },
    [isExpanded, onSelectPlan]
  );

  useEffect(() => {
    if (!emblaApi || !isExpanded) return;

    onSelect(emblaApi);
    emblaApi.on('reInit', onSelect);
    emblaApi.on('select', onSelect);

    return () => {
      emblaApi.off('reInit', onSelect);
      emblaApi.off('select', onSelect);
    };
  }, [emblaApi, onSelect, isExpanded]);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  return (
    <div className="embla">
      {isExpanded && (
        <div
          className="embla__buttons"
          style={{ visibility: prevBtnDisabled ? 'hidden' : 'visible' }}
        >
          <PrevButton onClick={onPrevButtonClick} disabled={prevBtnDisabled} />
        </div>
      )}
      <div className="embla__viewport" ref={emblaRef}>
        <div className="embla__container">
          {subscriptionOptions.map((option, index) => (
            <div className="embla__slide" key={index}>
              <div className="embla__slide__number">
                <motion.div
                  layout
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <SubscriptionPlanCard
                    isActive={selectedPlan === index}
                    onChoose={() => {
                      if (!isExpanded) onSelectPlan(index);
                    }}
                    viewingPlan={isExpanded}
                    option={option}
                    billingCycle={billingCycle}
                  />
                </motion.div>
              </div>
            </div>
          ))}
        </div>
      </div>
      {isExpanded && (
        <div
          className="embla__buttons"
          style={{ visibility: nextBtnDisabled ? 'hidden' : 'visible' }}
        >
          <NextButton
            onClick={onNextButtonClick}
            disabled={nextBtnDisabled}
            hidden={nextBtnDisabled}
          />
        </div>
      )}
    </div>
  );
};

export default EmblaCarousel;
