'use client';

import React, { useMemo } from 'react';
import { Box, useMediaQuery } from '@mui/material';
import { LoadingButton } from '../../loading-button';
import { useTheme } from '@emotion/react';

export interface ActionButtonsTemplateProps {
  onSave?: () => void;
  onSkip?: () => void;
  isSubmitting?: boolean;
  isValid?: boolean;
  saveButtonText?: string;
  skipButtonText?: string;
  variant?: 'organization' | 'student' | 'professional' | 'default';
  /**
   * @deprecated Caller-side media query flag no longer needed – kept for backward compatibility.
   */
  isXsScreen?: boolean;
}

/**
 * Shared action buttons template - copies exact CSS from existing form components
 * Used by: All profile setup forms and onboarding flows
 */
export const ActionButtonsTemplate: React.FC<ActionButtonsTemplateProps> = ({
  onSave,
  onSkip,
  isSubmitting = false,
  isValid = true,
  saveButtonText = 'Continue',
  skipButtonText = 'Do this later',
  variant = 'default',
}) => {
  const theme: any = useTheme();
  const isXsScreen = useMediaQuery(theme.breakpoints.down('sm'));

  // Memoize style objects to prevent infinite re-renders
  const containerSx = useMemo(
    () => ({
      display: 'flex',
      justifyContent: 'center',
      gap: 2,
      py: '20px',
      ...(isXsScreen && {
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'white',
        py: 2,
        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)',
        zIndex: 999,
        flexDirection: 'column',
        alignItems: 'center',
      }),
    }),
    [isXsScreen]
  );

  // Inner container CSS - memoized to prevent re-renders
  const innerContainerSx = useMemo(
    () => ({
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      gap: 2,
      flexDirection: { xs: 'column', sm: 'row' },
      alignItems: 'center',
    }),
    []
  );

  // Skip button CSS - memoized to prevent re-renders
  const skipButtonSx = useMemo(
    () => ({
      width: { xs: '172px' },
      height: '40px',
      backgroundColor: 'white',
      border: { sm: '1px solid #A24295' },
      color: '#A24295',
      '&:hover': {
        backgroundColor: 'secondary.light',
      },
    }),
    []
  );

  // Save button CSS - memoized to prevent re-renders
  const saveButtonSx = useMemo(
    () => ({
      width: { xs: '225px' },
      height: '40px',
      backgroundColor: '#A24295',
      color: 'white',
      '&:hover': {
        backgroundColor: '#8B1E7A',
      },
      '&:disabled': {
        backgroundColor: '#E0E0E0',
        color: '#9E9E9E',
      },
    }),
    []
  );

  return (
    <Box sx={containerSx}>
      <Box sx={innerContainerSx}>
        {/* Skip Button - exact CSS copied from existing forms */}
        <LoadingButton
          variant="outlined"
          sx={skipButtonSx}
          onClick={onSkip}
          disabled={isSubmitting}
        >
          {skipButtonText}
        </LoadingButton>

        {/* Save Button - exact CSS copied from existing forms */}
        <LoadingButton
          variant="contained"
          sx={saveButtonSx}
          onClick={onSave}
          loading={isSubmitting}
          disabled={!isValid || isSubmitting}
        >
          {saveButtonText}
        </LoadingButton>
      </Box>
    </Box>
  );
};

export default ActionButtonsTemplate;
