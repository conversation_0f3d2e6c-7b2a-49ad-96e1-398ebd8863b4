import '@mui/material/styles';
import '@mui/material/Typography';

export interface CustomValues {
  gradient?: {
    primary?: string;
    neutral?: string;
    prestige?: string;
  };
  paper?: {
    boxShadow?: {
      light?: string;
      medium?: string;
    };
    borderRadius?: number;
  };
  button?: {
    minWidth?: string | number;
    width?: string | number;
    height?: number;
    radius?: number;
    spacing?: number;
    smHeight?: number;
    textVariantSmPx?: number;
    padding?: {
      x?: number;
    };
    border?: {
      width?: number;
    };
  };
  tabs?: {
    width?: number;
    height?: number;
    gap?: number;
  };
  backButton?: {
    width?: number;
    height?: number;
    top?: number;
    left?: number;
    angle?: number;
  };
  typography?: {
    button?: {
      small?: {
        lineHeight?: string | number;
        fontSize?: string;
        fontWeight?: number;
        letterSpacing?: string;
      };
      medium?: {
        lineHeight?: string | number;
        fontSize?: string;
        fontWeight?: number;
        letterSpacing?: string;
      };
    };
    // Moved to component sx prop 
    // subscriptionCardTitle?: {
    //   fontFamily: string;
    //   fontWeight: number;
    //   fontSize: string;
    //   lineHeight: string;
    //   letterSpacing: string;
    //   color: string; // neutral.600
    // };
  };
  divider?: {
    spacing?: {
      y?: number;
    };
  };
  iconButton?: {
    size?: number;
  };
  spacing?: {
    welcomeToCarousel?: number;
    doctorNameToCarousel?: number;
    carouselToCards?: number;
    doctorNameToOtpDescription?: number;
  };
  auth?: {
    container?: {
      maxWidth?: string | number;
    };
    otp?: {
      inputHeight?: number;
      inputSpacing?: number;
      inputWidth?: number;
      inputRadius?: number;
      fontSize?: number;
    };
    timer?: {
      fontSize?: string;
      color?: string;
    };
  };
  signupCard?: {
    width: number;
    height: number;
    borderRadius: number;
    borderWidth: number;
    borderColor: string;
    shape?: {
      width: number;
      height: number;
      top: number;
      left: number;
      angle: number;
      opacity: number;
      borderRadius: string;
    };
    content: {
      width: number;
      height: number;
      top: number;
      left: number;
      gap: number;
      description: {
        width: number;
        height: number;
      };
    };
  };
  doctorCharacter: {
    width: number;
    height: number;
    top: number;
    left: number;
    color: string;
  };
  gentlemanCharacter: {
    width: number;
    height: number;
    top: number;
    left: number;
  };
  girlManCharacter: {
    width: number;
    height: number;
    top: number;
    left: number;
  };
  resendOtp?: {
    width: number;
    height: number;
  };
  subscriptionPlanCard: {
    width: number;
    height: number;
    borderRadius: number;
    borderWidth: number;
    p: number;
    gap: number;
    borderColor: string;
    icon: {
      width: number;
      height: number;
    };
  };
  opLights?: {
    width: number;
    height: number;
    top: number;
    left: number;
  };
  alliedCardiacMaleCharacter?: {
    width: number;
    height: number;
    top?: number;
    right?: number;
  };
  alliedCardiacFemaleCharacter?: {
    width: number;
    height: number;
    top?: number;
    left?: number;
  };
  proceedButton?: {
    width?: number;
    height?: number;
    minWidth?: string | number;
    borderRadius?: number;
    borderWidth?: number;
    paddingX?: number;
    gap?: number;
  };
  prestigeSubscriptionSection?: {
    width: number;
    height: number;
    top: number;
    left: number;
    borderRadius: number;
    borderWidth?: number;
    borderColor?: string;
    borderStyle?: string;
    colors?: {
      primary: string;
      secondary: string;
      tertiary: string;
    };
  };
}

// Augment the MUI theme types
declare module '@mui/material/styles' {
  interface TypographyVariants {
    heading1: React.CSSProperties;
    heading2: React.CSSProperties;
    heading3: React.CSSProperties;
    heading4: React.CSSProperties;
    textButton: React.CSSProperties;
    subtitle3: React.CSSProperties;
    welcomeText: React.CSSProperties;
    brandText: React.CSSProperties;
    doctorName: React.CSSProperties;
    otpDescription: React.CSSProperties;
    tabLabel: React.CSSProperties;
    // COMMENTED OUT - Moved to component sx prop (MC-219)
    // signupOptionTitle: React.CSSProperties;
    // signupOptionDescription: React.CSSProperties;
    signUpText: React.CSSProperties;
    // COMMENTED OUT - Moved to component sx prop (MC-219)
    // signUpAsText: React.CSSProperties;
    //Moved to component sx prop 
    // subscriptionCardTitle: React.CSSProperties;
    // subscriptionCardSubtitle: React.CSSProperties;
    prestigeSubscriptionTitle: React.CSSProperties;
    prestigeSubscriptionText: React.CSSProperties;
    professionalType: React.CSSProperties;
  }

  interface TypographyVariantsOptions {
    // COMMENTED OUT - Moved to component sx prop (MC-219)
    // signupOptionTitle?: React.CSSProperties;
    // signupOptionDescription?: React.CSSProperties;
    heading1?: React.CSSProperties;
    heading2?: React.CSSProperties;
    heading3?: React.CSSProperties;
    heading4?: React.CSSProperties;
    textButton?: React.CSSProperties;
    subtitle3?: React.CSSProperties;
    welcomeText?: React.CSSProperties;
    brandText?: React.CSSProperties;
    doctorName?: React.CSSProperties;
    otpDescription?: React.CSSProperties;
    tabLabel?: React.CSSProperties;
    signUpText?: React.CSSProperties;
    // COMMENTED OUT - Moved to component sx prop (MC-219)
    // signUpAsText?: React.CSSProperties;
    //  Moved to component sx prop 
    // subscriptionCardTitle: React.CSSProperties;
    // subscriptionCardSubtitle: React.CSSProperties;

    prestigeSubscriptionTitle?: React.CSSProperties;
    prestigeSubscriptionText?: React.CSSProperties;
    professionalType?: React.CSSProperties;
  }

  interface Theme {
    customValues: CustomValues;
  }

  interface ThemeOptions {
    customValues?: CustomValues;
  }

  interface Palette {
    neutral: {
      100: string;
      200: string;
      300: string;
      400: string;
      500: string;
      600: string;
      700: string;
      800: string;
      900: string;
      main: string;
      lowEmphasis: string;
    };
    lowEmphasis: string;
    tint?: {
      hover: string;
      lowEmphasis: string;
      lowerEmphasis: string;
    };
    status?: {
      error: string;
      success: string;
      warning: string;
    };
  }

  interface PaletteOptions {
    neutral?: {
      100: string;
      200: string;
      300: string;
      400: string;
      500: string;
      600: string;
      700: string;
      800: string;
      900: string;
      main: string;
      lowEmphasis: string;
    };
    lowEmphasis?: string;
    tint?: {
      hover: string;
      lowEmphasis: string;
      lowerEmphasis: string;
    };
    status?: {
      error: string;
      success: string;
      warning: string;
    };
  }
}

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    // COMMENTED OUT - Moved to component sx prop (MC-219)
    // signupOptionTitle: true;
    // signupOptionDescription: true;
    heading1: true;
    heading2: true;
    heading3: true;
    heading4: true;
    textButton: true;
    subtitle3: true;
    welcomeText: true;
    brandText: true;
    doctorName: true;
    otpDescription: true;
    tabLabel: true;
    signUpText: true;
    // COMMENTED OUT - Moved to component sx prop (MC-219)
    // signUpAsText: true;
    // Moved to component sx prop 
    // subscriptionCardTitle: true;
    // subscriptionCardSubtitle: true;

    prestigeSubscriptionTitle: true;
    prestigeSubscriptionText: true;
    professionalType: true;
  }
}
