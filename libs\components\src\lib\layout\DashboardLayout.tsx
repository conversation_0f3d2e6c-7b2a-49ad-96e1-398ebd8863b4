'use client';
import { useEffect, useState } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import WelcomeOverlay from './WelcomeOverlay';

import AddPostFAB from './AddPostFAB';
import ResponsiveLayout from './ResponsiveLayout';

export default function DashboardLayout() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [showWelcome, setShowWelcome] = useState(false);
  const fromSignup = searchParams?.get('fromSignup');

  useEffect(() => {
    if (fromSignup === 'true') {
      setShowWelcome(true);
      const newUrl = pathname;
      window.history.replaceState({}, '', newUrl);
    }
  }, [fromSignup, pathname]);

  const handleCloseWelcome = () => setShowWelcome(false);

  return (
    <>
      <WelcomeOverlay open={showWelcome} onClose={handleCloseWelcome} />
      <ResponsiveLayout />
      <AddPostFAB />
    </>
  );
}
