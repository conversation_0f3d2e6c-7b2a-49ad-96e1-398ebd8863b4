'use client';

import React from 'react';
import { Box, Container, useMediaQuery } from '@mui/material';
import { useRouter } from 'next/navigation';
import { PatientProfileWelcome } from '../../profile';
import { Subtitle, BackButton } from '../../auth';
import CustomizedSteppers from '../components/Stepper';
import { useTheme } from '@emotion/react';

export interface DocumentUploadPageTemplateProps {
  children: React.ReactNode;
  userName?: string;
  subtitleText?: string;
  showBackButton?: boolean;
  onBack?: () => void;
  currentStep?: number;
  steps?: string[];
}

/**
 * Shared template for document upload pages - copies exact CSS from existing pages
 * Used by: Professional, Student, Organization document upload flows
 */
export const DocumentUploadPageTemplate: React.FC<
  DocumentUploadPageTemplateProps
> = ({
  children,
  userName,
  subtitleText = "Let's set up your Professional Account!",
  showBackButton = true,
  onBack,
  currentStep = 1,
  steps = ['Profile Setup', 'Document Upload', 'Adding Network'],
}) => {
  const router = useRouter();
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  return (
    <Container maxWidth="lg">
      <Box mt={'20px'}>
        {/* Welcome Header - exact CSS copied from existing pages */}
        {!isSmallScreen && userName && (
          <PatientProfileWelcome patientName={userName} subtitle={''} />
        )}

        {/* Back Button - exact CSS copied from existing pages */}
        {showBackButton && <BackButton handleBackButton={handleBack} />}

        {/* Subtitle - exact CSS copied from existing pages */}
        <Subtitle
          text={subtitleText}
          sx={{ fontSize: { xs: '12px', sm: '16px' } }}
          marginBottom={'34px'}
        />

        {/* Stepper - exact CSS copied from existing pages */}
        <CustomizedSteppers activeStep={currentStep} steps={steps} />

        {/* Main Content */}
        {children}
      </Box>
    </Container>
  );
};

export default DocumentUploadPageTemplate;
