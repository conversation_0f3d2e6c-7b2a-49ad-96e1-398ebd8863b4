import { auth } from '../firebase/firebase-client.js';

import { sessionLogin } from './auth-api.js';
import { resetAuthMode } from '../http-client.js';

// Centralized session establishment to avoid duplicate calls
const sessionPromise = new Map<string, Promise<boolean>>();

const ensureSession = async (forceRefresh = false): Promise<boolean> => {
  try {
    if (!auth) return false;

    const currentUser = auth.currentUser;
    if (!currentUser) return false;

    const cacheKey = `${currentUser.uid}-${forceRefresh}`;

  
    if (sessionPromise.has(cacheKey)) {
      return sessionPromise.get(cacheKey)!;
    }

    const promise = (async () => {
      try {
        const idToken = await currentUser.getIdToken(forceRefresh);
        await sessionLogin({ idToken });
        resetAuthMode();
        return true;
      } finally {
        sessionPromise.delete(cacheKey);
      }
    })();

    sessionPromise.set(cacheKey, promise);
    return promise;
  } catch (error) {
    return false;
  }
};

export const establishSession = async (): Promise<boolean> => {
  return ensureSession(false);
};

export const handleSessionExpiration = async (): Promise<boolean> => {
  return ensureSession(true);
};

export const isSessionValid = async (): Promise<boolean> => {
  try {
    if (!auth) return false;

    const currentUser = auth.currentUser;
    if (!currentUser) return false;

    const tokenResult = await currentUser.getIdTokenResult();
    const authTime = new Date(tokenResult.authTime).getTime();
    const now = Date.now();
    const fiveDaysInMs = 5 * 24 * 60 * 60 * 1000;

    return (now - authTime) < fiveDaysInMs;
  } catch (error) {
    return false;
  }
};
