import { Box, SxProps, Typography } from '@mui/material';
import AddImageIcon from '../Icons/FeedIcons/AddImageIcon';

export const AddMoreMediaButton = ({
  open,
  sx,
}: {
  open: () => void;
  sx?: SxProps;
}) => {
  return (
    <Box
      position="absolute"
      bottom={0}
      left={0}
      width="100%"
      sx={{
        padding: '11px',
        borderTop: '1px solid #A3A3A325',
        alignItems: 'center',
        display: 'flex',
        justifyContent: 'center',
        cursor: 'pointer',
        gap: '4px',
        ...sx,
      }}
      onClick={open}
    >
      <AddImageIcon />
      <Typography
        variant="body2"
        color="secondary.main"
        fontSize={'16px'}
        fontWeight={600}
      >
        Add more media
      </Typography>
    </Box>
  );
};
