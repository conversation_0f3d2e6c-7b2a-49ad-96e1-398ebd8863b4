import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { axiosInstance } from '../http-client.js';

// Define employer types
export interface Employer {
  id: string;
  name: string;
}

export interface Categories {
  id: string;
  name: string;
  parentId: string;
  userSegment: string;
}

export interface Country {
  id: string;
  name: string;
  code: string;
  continent: {
    id: string;
    name: string;
  }
}

export interface CreateEmployerRequest {
  name: string;
  url: string;
}

export interface CreateEmployerResponse {
  id: string;
  name: string;
  url: string;
}

// Define query keys for dropdowns
export const dropdownQueryKeys = {
  all: ['dropdowns'] as const,
  employers: (searchTerm?: string) => [...dropdownQueryKeys.all, 'employers', searchTerm] as const,
  categories: (userSegment?: string) => [...dropdownQueryKeys.all, 'categories', userSegment] as const,
  countries: () => [...dropdownQueryKeys.all, 'countries'] as const,
};

// API function to fetch employers
export const fetchEmployers = async (searchTerm?: string): Promise<Employer[]> => {
  const params = searchTerm ? { name: searchTerm } : undefined;
  const response = await axiosInstance.get('/drop-downs/employers', { params });
  return response.data.data || [];
}

export const fetchCategories = async (userSegment?: string): Promise<Categories[]> => {
  const response = await axiosInstance.get(`/drop-downs/simplified-segment-categories/${userSegment}`);
  return response.data.data || [];
}

export const fetchCountries = async (): Promise<Country[]> => {
  const response = await axiosInstance.get('/drop-downs/countries');
  return response.data.data || [];
}

// Hook to get employers
export const useGetEmployers = (searchTerm?: string) => {
  return useQuery({
    queryKey: dropdownQueryKeys.employers(searchTerm),
    queryFn: async () => {
      const data = await fetchEmployers(searchTerm);
      console.log('Employers data:', data);
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
};

// Hook to get categories
export const useGetCategories = (userSegment?: string) => {
  return useQuery({
    queryKey: dropdownQueryKeys.categories(userSegment),
    queryFn: async () => {
      const data = await fetchCategories(userSegment);
      console.log('Categories data:', data);
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    enabled: !!userSegment, // Only run query if userSegment is provided
  });
}

export const useGetCountries = () => {
  return useQuery({
    queryKey: dropdownQueryKeys.countries(),
    queryFn: async () => {
      const data = await fetchCountries();
      console.log('Countries data:', data);
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  }); 
}

// API function to create an employer
export const createEmployer = async (data: CreateEmployerRequest): Promise<CreateEmployerResponse> => {
  console.log('Creating employer with data:', data);
  try {
    // Use the axiosInstance to make the API call to the employer creation endpoint
    // Following the authentication pattern from patient/profile that works successfully
    // The axiosInstance already includes the /api/v1 prefix in its baseURL
    const response = await axiosInstance.post('/employer', data);

    console.log('Create employer response:', response.data);
    return response.data.data || response.data;
  } catch (error: any) {
    console.error('Error creating employer:', error);

    // Extract and log detailed error information
    if (error.response) {
      console.error('Error response:', {
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers
      });
    }

    throw error;
  }
};

// Hook to create an employer
export const useCreateEmployer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createEmployer,
    onSuccess: () => {
      // Invalidate the employers query to refetch the data
      queryClient.invalidateQueries({ queryKey: dropdownQueryKeys.all });
    },
  });
};
