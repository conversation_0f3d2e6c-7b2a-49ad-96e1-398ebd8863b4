const ArticleIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M29.9604 13.8571V27.4838C29.9604 29.9713 27.9305 32 25.4442 32H10.5552C8.069 32 6.03906 29.9713 6.03906 27.4838V8.51617C6.03906 6.02875 8.06893 4 10.5552 4H20.1045C21.3476 4 22.4172 4.44328 23.2978 5.32275L28.6388 10.6637C29.5194 11.5432 29.9615 12.6128 29.9615 13.857L29.9604 13.8571ZM11.0581 13.7716H16.348C17.0706 13.7716 17.66 13.1833 17.66 12.4595C17.66 11.7346 17.0706 11.1475 16.348 11.1475H11.0581C10.3356 11.1475 9.7461 11.7346 9.7461 12.4595C9.7461 13.1845 10.3356 13.7716 11.0581 13.7716ZM11.0581 19.8601H24.9404C25.6665 19.8601 26.2524 19.273 26.2524 18.548C26.2524 17.8231 25.6653 17.236 24.9404 17.236L11.0594 17.2336C10.3368 17.2336 9.74732 17.8219 9.74732 18.5456C9.74732 19.2706 10.3368 19.8577 11.0594 19.8577L11.0581 19.8601ZM11.0581 25.9486H24.9404C25.6665 25.9486 26.2524 25.3615 26.2524 24.6365C26.2524 23.9116 25.6653 23.3245 24.9404 23.3245L11.0594 23.3221C10.3368 23.3221 9.74732 23.9092 9.74732 24.6341C9.74732 25.3591 10.3368 25.9462 11.0594 25.9462L11.0581 25.9486Z"
        fill={hoverFill}
      />
    </svg>
  );
};

export default ArticleIcon;
