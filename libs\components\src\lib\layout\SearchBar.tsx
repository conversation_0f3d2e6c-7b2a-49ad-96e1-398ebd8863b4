import { Box, IconButton, InputBase } from '@mui/material';
import { Iconify } from '../iconify';
import VoiceIcon from '../Icons/VoiceIcon';

export const SearchBar = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        width: '100%',
        borderRadius: '8px',
        overflow: 'hidden',
        border: '0.5px solid #A3A3A3',
        backgroundColor: 'white',
        marginTop: '16px',
      }}
    >
      <Box
        sx={{
          backgroundColor: '#A24295',
          padding: '10px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '50px',
          height: '47px',
        }}
      >
        <Iconify
          icon="material-symbols:search-rounded"
          sx={{ fontSize: 20, color: 'white' }}
        />
      </Box>
      <InputBase
        placeholder="Search by keyword, poster, or tag"
        sx={{
          flex: 1,
          paddingLeft: 2,
          paddingRight: 2,
          backgroundColor: 'white',
        }}
      />
      <IconButton>
        <VoiceIcon />
      </IconButton>
    </Box>
  );
};
