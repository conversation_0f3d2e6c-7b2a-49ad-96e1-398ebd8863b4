import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useState } from 'react';

interface MapLinkDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (link: string) => void;
  defaultValue?: string;
}

const isValidMapLink = (url: string) =>
  /^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w-./?%&=]*)?$/i.test(url);

export default function MapLinkDialog({
  open,
  onClose,
  onSave,
  defaultValue = '',
}: MapLinkDialogProps) {
  const [link, setLink] = useState(defaultValue);
  const [error, setError] = useState('');

  const handleSave = () => {
    if (!link) {
      setError('Map link cannot be empty');
      return;
    }

    if (!isValidMapLink(link)) {
      setError('Please enter a valid Google Maps link');
      return;
    }

    setError('');
    onSave(link);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        Add map link
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Typography sx={{ mb: 2 }}>
          You can add a map link for more accurate location information. This
          will appear as a link to your location on your profile.
        </Typography>
        <TextField
          fullWidth
          label="Link"
          placeholder="Enter your map link here"
          value={link}
          onChange={(e) => setLink(e.target.value)}
          error={!!error}
          helperText={error}
          InputLabelProps={{ shrink: true }}
        />
      </DialogContent>
      <DialogActions
        sx={{
          justifyContent: 'center',
        }}
      >
        <Button
          variant="outlined"
          onClick={onClose}
          sx={(theme) => ({
            borderColor: theme.palette.secondary.main,
            color: theme.palette.secondary.main,
            width: '136px'
          })}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          sx={{
            backgroundColor: 'secondary.main',
            color: 'white',
            width: '136px',
            '&:hover': {
              backgroundColor: 'secondary.dark',
            },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}
