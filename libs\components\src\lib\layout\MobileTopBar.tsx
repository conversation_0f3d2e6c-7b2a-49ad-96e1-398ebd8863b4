'use client';

import { Box } from '@mui/material';
import Image from 'next/image';
import { MINICARDIAC_TRADEMARK } from '../auth';
import { getDecodedToken } from '@minicardiac-client/utilities';
import { useAuth } from '@minicardiac-client/apis';
import { useEffect, useState } from 'react';
import { FeedProfile } from './FeedProfile';

export default function MobileTopBar() {
  const [userDetails, setUserDetails] = useState<{
    name: string;
    photoURL: string;
  } | null>(null);

  const { authState } = useAuth();
  useEffect(() => {
    async function fetchUserDetails() {
      const decodedToken = await getDecodedToken();
      setUserDetails({
        name: decodedToken.name,
        photoURL: authState.user?.photoURL || '',
      });
    }

    fetchUserDetails();
  }, [authState]);

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
      }}
    >
      <Box
        sx={{
          textAlign: 'center',
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
          cursor: 'pointer',
          px: '16px',
          py: '22px',
          justifyContent: 'space-between',
          backgroundColor: 'white',
          width: '100%',
        }}
      >
        <Image
          src={MINICARDIAC_TRADEMARK}
          alt={'Mini Card Icon'}
          width={181}
          height={28}
        />

        <FeedProfile userDetails={userDetails} />
      </Box>
    </Box>
  );
}
