'use client';

import { useState } from 'react';
import { Box, Stack, TextField, Typography } from '@mui/material';
import { ExtendedTheme } from '../types/auth.types';
import { LoadingButton } from '../../loading-button';
import WelcomeBack from './WelcomeBack';

interface OtpVerificationProps {
  email: string;
  displayName: string;
  onVerify: (otp: string) => void;
  onResendOtp: () => Promise<void>;
  isVerifying?: boolean;
  welcomeText?: string;
  otpInputLabel?: string;
  otpInputPlaceholder?: string;
  otpInstruction?: string;
  resendOtpText?: string;
  resendSuccessText?: string;
  sendingText?: string;
  verifyingText?: string;
  signInButtonText?: string;
}

/**
 * OTP Verification component for two-factor authentication
 */
export const OtpVerification = ({
  email,
  displayName,
  onVerify,
  onResendOtp,
  isVerifying = false,
  welcomeText = 'Welcome To',
  otpInputLabel = 'OTP',
  otpInputPlaceholder = '6-digit OTP',
  otpInstruction = 'Enter OTP sent to your registered email address',
  resendOtpText = 'Resend OTP',
  resendSuccessText = 'OTP resent successfully!',
  sendingText = 'Sending...',
  verifyingText = 'Verifying...',
  signInButtonText = 'Sign in',
}: OtpVerificationProps) => {
  const [otp, setOtp] = useState('');
  const [isResending, setIsResending] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);

  const isValid = /^\d{6}$/.test(otp); // Valid only if 6-digit numeric

  const handleSubmit = () => {
    if (isValid) {
      onVerify(otp);
    }
  };

  const handleResendOtp = async () => {
    setIsResending(true);
    setResendSuccess(false);
    try {
      await onResendOtp();
      setResendSuccess(true);
    } catch (error) {
      console.error('Failed to resend OTP:', error);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <Stack
      sx={{
        width: {
          xs: '280px',
          sm: '320px',
          md: '320px',
          lg: '400px',
          xxl: '460px',
        },
      }}
    >
      <Box
        sx={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          mt: { xs: '40px', sm: '40px' },
        }}
      >
        <WelcomeBack displayText={welcomeText} />
      </Box>

      <Box sx={{ position: 'relative' }}>
        <Typography
          variant="h3"
          sx={(theme) => ({
            fontFamily: "'Plus Jakarta Sans', sans-serif",
            fontWeight: 600,
            fontSize: { xs: '20px', sm: '24px' },
            lineHeight: '100%',
            textAlign: 'center',
            width: '100%',
            height: 30,
            color: (theme.palette as any).neutral?.[800] || '#333537',
          })}
        >
          {capitalizeWords(displayName)}
        </Typography>

        <Box
          sx={{
            textAlign: 'center',
            mt: {
              xs: '64px',
              sm: '80px',
            },
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <Typography
            variant="body1"
            component="span"
            sx={(theme) => ({
              fontFamily: "'Plus Jakarta Sans', sans-serif",
              fontWeight: 300,
              fontSize: '16px',
              color: (theme.palette as any).neutral?.[800] || '#333537',
              whiteSpace: 'nowrap',
              display: 'inline-block',
            })}
          >
            {otpInstruction}
          </Typography>
        </Box>
      </Box>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
        }}
      >
        <TextField
          label={otpInputLabel}
          variant="outlined"
          size="small"
          value={otp}
          onChange={(e) => {
            const value = e.target.value;
            if (/^\d{0,6}$/.test(value)) {
              setOtp(value);
            }
          }}
          placeholder={otpInputPlaceholder}
          autoComplete="one-time-code"
          InputLabelProps={{
            shrink: true,
          }}
          sx={{
            '& .MuiInputLabel-root': {
              fontSize: '16px',
              fontWeight: 500,
            },
            mt: '40px',
            width: {
              xs: '280px',
              sm: '320px',
              lg: '400px',
              xxl: '460px',
            },
          }}
        />

        <Box sx={{ textAlign: 'center', mt: '40px' }}>
          {isResending ? (
            <LoadingButton
              variant="text"
              loading
              loadingText={sendingText}
              sx={{
                color: 'primary.main',
                backgroudColor: 'transparent',
                textTransform: 'none',
                '&:hover': { backgroundColor: 'transparent' },
              }}
            />
          ) : (
            <Box
              component="button"
              onClick={handleResendOtp}
              sx={{
                background: 'none',
                border: 'none',
                padding: 0,
                cursor: 'pointer',
              }}
            >
              <Typography fontWeight="600" color="secondary.main">
                {resendOtpText}
              </Typography>
            </Box>
          )}

          {resendSuccess && (
            <Typography
              variant="caption"
              color="success.main"
              sx={{ display: 'block', mt: 1 }}
            >
              {resendSuccessText}
            </Typography>
          )}
        </Box>

        <LoadingButton
          fullWidth
          variant="contained"
          onClick={handleSubmit}
          loading={isVerifying}
          loadingText={verifyingText}
          disabled={!isValid}
          sx={(theme: any) => ({
            gap: (theme as ExtendedTheme).customValues.button?.spacing,
            backgroundColor: isValid
              ? (theme.palette as any).secondary.main
              : (theme.palette as any).primary.main,
            color: '#FFFFFF',
            '&:hover': {
              backgroundColor: isValid
                ? (theme.palette as any).secondary.dark
                : (theme.palette as any).primary.dark,
            },

            mt: { xs: '40px', sm: '24px' },
            textTransform: 'none',
            borderRadius: '8px',
            fontWeight: 500,
            fontSize: '14px',
            width: {
              xs: '280px',
              sm: '320px',
              lg: '400px',
              xxl: '460px',
            },
          })}
        >
          {signInButtonText}
        </LoadingButton>
      </Box>
    </Stack>
  );
};

export default OtpVerification;

const capitalizeWords = (name: string) =>
  name.replace(/\b\w/g, (char) => char.toUpperCase());
