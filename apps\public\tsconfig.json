{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "jsx": "preserve",
    "noEmit": true,
    "emitDeclarationOnly": false,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "paths": {
      "@/*": ["./*"],
      "@libs/*": ["../../libs/*"],
      "@minicardiac-client/shared": ["../../libs/shared/src/index.ts"],
      "@minicardiac-client/shared/lib/*": ["../../libs/shared/src/lib/*"],
      "@minicardiac-client/components": ["../../libs/components/src/index.ts"],
      "@minicardiac-client/constants": ["../../libs/constants/src/index.ts"],
      "@minicardiac-client/types": ["../../libs/types/src/index.ts"],
      "@minicardiac-client/apis": ["../../libs/apis/src/index.ts"],
      "@minicardiac-client/apis/lib/*": ["../../libs/apis/src/lib/*"],
      "@minicardiac-client/utilities": ["../../libs/utilities/src/index.ts"]
    },
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "types": ["jest", "node"],
    "outDir": "dist",
    // Changed rootDir to include the workspace root
    "rootDir": "../..",
    "tsBuildInfoFile": "dist/tsconfig.tsbuildinfo"
  },
  "include": [
    "../../apps/public/.next/types/**/*.ts",
    "../../dist/apps/public/.next/types/**/*.ts",
    ".next/types/**/*.ts",
    "next-env.d.ts",
    "src/**/*.js",
    "src/**/*.jsx",
    "src/**/*.ts",
    "src/**/*.tsx"
  ],
  "exclude": [
    "out-tsc",
    "dist",
    "node_modules",
    "jest.config.ts",
    "src/**/*.spec.ts",
    "src/**/*.test.ts",
    ".next",
    "eslint.config.js",
    "eslint.config.cjs",
    "eslint.config.mjs"
  ],
  "references": [
    {
      "path": "../../libs/utilities"
    },
    {
      "path": "../../libs/apis"
    },
    {
      "path": "../../libs/constants"
    },
    {
      "path": "../../libs/types"
    },
    {
      "path": "../../libs/components"
    },
    {
      "path": "../../libs/shared"
    }
  ]
}
