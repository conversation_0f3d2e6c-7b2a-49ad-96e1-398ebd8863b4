import { useState, useCallback, ChangeEvent } from 'react';
import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { axiosInstance } from '@minicardiac-client/apis';
import { auth } from '@minicardiac-client/apis';

export interface UploadImageError {
  message: string;
  code: string;
}

export interface UseProfilePictureProps {
  onImageUpload?: (imageUrl: string, thumbnailUrl: string) => void;
  initialImageUrl?: string | null;
}

export function useProfilePicture({ onImageUpload, initialImageUrl = null }: UseProfilePictureProps = {}) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(initialImageUrl);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Generate upload URL mutation
  const generateUploadUrlMutation = useMutation({
    mutationFn: async ({
      mediaType,
      entityType,
    }: {
      mediaType: string;
      entityType: string;
    }) => {
      const response = await axiosInstance.post('/utils/generate-upload-url', {
        mediaType,
        entityType,
      });
      return response.data;
    },
  });

  // Upload file mutation
  const uploadFileMutation = useMutation({
    mutationFn: async ({
      url,
      formData,
    }: {
      url: string;
      formData: FormData;
    }) => {
      const response = await axios.post(url, formData);
      return response.data;
    },
  });

  const handleImageChange = useCallback(
    async (e: ChangeEvent<HTMLInputElement>) => {
      if (!e.target.files?.[0]) return;

      const file = e.target.files[0];
      setError(null);

      // Create a local preview
      const localPreviewUrl = URL.createObjectURL(file);
      setPreviewUrl(localPreviewUrl);
      setIsUploading(true);

      try {
        // Check authentication
        if (!auth || !auth.currentUser) {
          throw {
            message: 'User not authenticated',
            code: 'auth/not-authenticated',
          } as UploadImageError;
        }

        // 1. Get pre-signed URL
        const uploadUrlResponse = await generateUploadUrlMutation.mutateAsync({
          mediaType: file.type,
          entityType: 'profile',
        });

        const uploadData = uploadUrlResponse.data;

        // 2. If the server already provided a fileUrl, use it directly
        if (uploadData?.fileUrl) {
          if (onImageUpload) {
            onImageUpload(uploadData.fileUrl, uploadData.fileUrl);
          }
          return;
        }

        // 3. Validate the response
        if (!uploadData?.url) {
          throw new Error('Failed to get upload URL from server');
        }

        // 4. Create FormData and upload
        const formData = new FormData();
        if (uploadData.fields) {
          Object.entries(uploadData.fields).forEach(([key, value]) => {
            formData.append(key, value as string);
          });
        }
        formData.append('file', file);

        // 5. Upload the file
        await uploadFileMutation.mutateAsync({
          url: uploadData.url,
          formData,
        });

        // 6. Construct the final image URL
        let fileUrl = '';
        if (uploadData.fileUrl) {
          fileUrl = uploadData.fileUrl;
        } else if (uploadData.fields?.key && uploadData.fields?.bucket) {
          fileUrl = `https://${uploadData.fields.bucket}.s3.amazonaws.com/${uploadData.fields.key}`;
        } else if (uploadData.key && uploadData.bucket) {
          fileUrl = `https://${uploadData.bucket}.s3.amazonaws.com/${uploadData.key}`;
        }

        // 7. Call the callback with the image URL
        if (fileUrl && onImageUpload) {
          onImageUpload(fileUrl, fileUrl);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to upload image');
        // Clean up the preview URL
        if (localPreviewUrl) {
          URL.revokeObjectURL(localPreviewUrl);
        }
        setPreviewUrl(initialImageUrl);
        throw err;
      } finally {
        setIsUploading(false);
      }
    },
    [generateUploadUrlMutation, uploadFileMutation, onImageUpload, initialImageUrl]
  );

  const handleRemove = useCallback(() => {
    if (previewUrl && previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    setError(null);
  }, [previewUrl]);

  return {
    previewUrl,
    isUploading,
    error,
    handleImageChange,
    handleRemove,
    setPreviewUrl,
  };
} 