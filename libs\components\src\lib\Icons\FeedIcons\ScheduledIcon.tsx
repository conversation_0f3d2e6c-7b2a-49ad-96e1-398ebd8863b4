const ScheduledIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  return (
    <svg
      width="36"
      height="37"
      viewBox="0 0 36 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.4376 16.9375C9.46548 16.9375 7.57367 17.7219 6.17894 19.1164C4.78421 20.5109 4 22.4027 4 24.375C4 26.3473 4.78443 28.2389 6.17894 29.6336C7.57345 31.0283 9.46527 31.8125 11.4376 31.8125C13.41 31.8125 15.3016 31.0281 16.6963 29.6336C18.091 28.2391 18.8752 26.3473 18.8752 24.375C18.8735 22.4029 18.0891 20.5128 16.6946 19.1181C15.3001 17.7235 13.4099 16.9392 11.4376 16.9375ZM12.3126 24.3748C12.3109 24.6072 12.2203 24.8294 12.0597 24.9968L10.2841 26.7639H10.2824C10.12 26.9245 9.89956 27.0168 9.67057 27.0168C9.43815 27.0168 9.21597 26.9245 9.0485 26.7639C8.88444 26.5998 8.79215 26.3777 8.79215 26.1469C8.79215 25.9145 8.88444 25.6924 9.0485 25.53L10.5626 24.0159V20.1742C10.5626 19.6923 10.954 19.2992 11.4376 19.2992C11.9212 19.2992 12.3126 19.6923 12.3126 20.1742L12.3126 24.3748Z"
        fill={hoverFill}
      />
      <path
        d="M28.9371 6.72627H27.6418V5.56246C27.6418 4.8379 27.0539 4.25 26.3293 4.25C25.6047 4.25 25.0168 4.8379 25.0168 5.56246V6.72627H15.3578V5.56246C15.3578 4.8379 14.7699 4.25 14.0453 4.25C13.3207 4.25 12.7328 4.8379 12.7328 5.56246V6.72627H11.4375C9.74729 6.72798 8.37671 8.09854 8.375 9.78869V16.1769C8.94068 15.965 9.52683 15.8163 10.125 15.7292V13.945H30.2497V24.3748C30.2497 24.7234 30.1113 25.0566 29.8652 25.3027C29.6191 25.5488 29.2859 25.6872 28.9372 25.6872H20.0832C19.9961 26.2854 19.8474 26.8715 19.6355 27.4372H28.9375C30.6277 27.4355 31.9982 26.0649 32 24.3748V9.78891C31.9982 8.09876 30.6273 6.72798 28.9371 6.72627Z"
        fill={hoverFill}
      />
    </svg>
  );
};

export default ScheduledIcon;
