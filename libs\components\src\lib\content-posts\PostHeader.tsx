import { Box, Typography, Avatar, IconButton } from '@mui/material';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import { BackButton } from '../buttons/Backbutton';
import { useRouter } from 'next/navigation';

interface PostHeaderProps {
  user: {
    name: string;
    profilePic: string;
    postedAgo: string;
  };
  showOptions?: boolean;
  showBackButton?: boolean;
}

const PostHeader = ({ user, showOptions, showBackButton }: PostHeaderProps) => {
  const router = useRouter();

  return (
    <Box display="flex" justifyContent="space-between" alignItems="center">
      <Box display="flex" alignItems="center" gap="12px">
        {showBackButton && <BackButton onClick={() => router.back()} />}
        <Avatar
          src={user.profilePic}
          alt={user.name}
          sx={{ width: { xs: 40, sm: 52 }, height: { xs: 40, sm: 52 } }}
        />
        <Box>
          <Typography fontSize="16px" fontWeight={600}>
            {user.name}
          </Typography>
          <Typography fontSize="12px" fontWeight={300} color="text.secondary">
            {user.postedAgo}
          </Typography>
        </Box>
      </Box>

      {showOptions && (
        <Box display={'flex'} alignItems="center" gap="12px">
          <IconButton
            sx={{
              color: '#A24295',
              p: 0,
            }}
          >
            <BookmarkBorderIcon sx={{ fontSize: 24 }} />
          </IconButton>

          <IconButton
            sx={{
              color: '#A24295',
              p: 0,
            }}
          >
            <MoreHorizIcon sx={{ fontSize: 24 }} />
          </IconButton>
        </Box>
      )}
    </Box>
  );
};

export default PostHeader;
