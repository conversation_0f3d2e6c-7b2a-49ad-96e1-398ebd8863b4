// slate-custom-types.d.ts
import { BaseEditor, Descendant, BaseElement, BaseText } from 'slate';
import { ReactEditor } from 'slate-react';

export type CustomText = {
  text: string;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  code?: boolean;
  strike?: boolean;
};

export type CustomElement = {
  type: string; // You can further refine this with union types e.g. 'paragraph' | 'heading' | ...
  align?: string;
  children: Descendant[];
};

// Merge with Slate module
declare module 'slate' {
  interface CustomTypes {
    Editor: BaseEditor & ReactEditor;
    Element: CustomElement;
    Text: CustomText;
  }
}

type RichTextEditorProps = {
  value: Descendant[];
  onChange: (value: Descendant[]) => void;
};
type TextAlign = 'left' | 'right' | 'center' | 'justify' | undefined;
