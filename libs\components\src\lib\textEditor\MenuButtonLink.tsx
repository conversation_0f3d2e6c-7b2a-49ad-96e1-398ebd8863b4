import { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
} from '@mui/material';
import LinkIcon from '@mui/icons-material/Link';
import { Editor } from '@tiptap/react';
import { LoadingButton } from '../loading-button';

export function MenuButtonLink({ editor }: { editor: Editor }) {
  const [open, setOpen] = useState(false);
  const [linkText, setLinkText] = useState('');
  const [linkUrl, setLinkUrl] = useState('');

  const handleOpen = () => {
    const { href = '' } = editor.getAttributes('link');
    const text = editor.state.doc.textBetween(
      editor.state.selection.from,
      editor.state.selection.to,
      ' '
    );

    setLinkText(text || '');
    setLinkUrl(href);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleInsertLink = () => {
    if (linkUrl) {
      // Insert or update link
      editor
        .chain()
        .focus()
        .extendMarkRange('link')
        .insertContent(linkText || linkUrl)
        .setLink({ href: linkUrl })
        .run();
    } else {
      // Remove link
      editor.chain().focus().extendMarkRange('link').unsetLink().run();
    }
    handleClose();
  };

  return (
    <>
      <IconButton onClick={handleOpen} size="small">
        <LinkIcon />
      </IconButton>

      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Insert/Edit Link</DialogTitle>
        <DialogContent
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '20px',
            mt: '10px',
            alignItems: 'center',
            justifyContent: 'center',
            height: '200px',
          }}
        >
          <TextField
            label="Link Text"
            value={linkText}
            onChange={(e) => setLinkText(e.target.value)}
            fullWidth
            InputLabelProps={{ shrink: true }}
            sx={{
              '& label.Mui-focused': {
                color: '#A24295',
              },
            }}
          />
          <TextField
            label="Link URL"
            value={linkUrl}
            onChange={(e) => setLinkUrl(e.target.value)}
            fullWidth
            InputLabelProps={{ shrink: true }}
            sx={{
              '& label.Mui-focused': {
                color: '#A24295',
              },
            }}
          />
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: 'center',
            gap: 2, // space between buttons
            paddingBottom: '16px',
          }}
        >
          <LoadingButton
            onClick={handleClose}
            variant="outlined"
            sx={{
              backgroundColor: 'transparent !important',
              border: '1px solid #A24295 ',
              color: '#A24295',
              boxShadow: 'none',
              '&:hover': {
                backgroundColor: '#A24295',
                boxShadow: 'none',
              },
            }}
          >
            Cancel
          </LoadingButton>
          <LoadingButton
            onClick={handleInsertLink}
            variant="contained"
            disabled={!linkUrl}
            sx={{
              backgroundColor: '#A24295',
              boxShadow: 'none',

              '&:hover': {
                backgroundColor: '#8e2975',
                boxShadow: 'none',
              },
            }}
          >
            Insert
          </LoadingButton>
        </DialogActions>
      </Dialog>
    </>
  );
}
