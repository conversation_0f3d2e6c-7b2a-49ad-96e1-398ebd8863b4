import { Box, Stack, Typography, ButtonBase } from '@mui/material';
import React, { ReactElement, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

type NavItemProps = {
  icon: ReactElement;
  label: string;
  link: string;
};

export default function NavItem({ icon, label, link }: NavItemProps) {
  const pathname = usePathname();
  const [isHovered, setIsHovered] = useState(false);

  const isActive = pathname === link;

  const fillColor = isActive || isHovered ? '#A24295' : '#A3A3A3';

  return (
    <Box
      sx={{
        display: 'flex',
        width: '100%',
        justifyContent: 'space-between',
        alignItems: 'center',
        position: 'relative',
      }}
    >
      <ButtonBase
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        sx={{
          borderRadius: 1,
          pl: { sm: '2px', xs: '2px', md: '12px' },
          py: '10px',
          justifyContent: { sm: 'center', xs: 'center', md: 'flex-start' },
          width: { sm: '100%', xs: '100%', md: '180px' },
          height: { xs: '100%', md: '44px' },
          backgroundColor: isActive ? 'rgba(162, 66, 149, 0.15)' : 'false',
          '&:hover': {
            backgroundColor: 'rgba(162, 66, 149, 0.15)',
            color: '#A24295',
            '& .label': {
              color: '#A24295',
              fontWeight: 700,
            },
          },
        }}
      >
        <Link href={link} passHref>
          <Stack
            direction="row"
            alignItems="center"
            sx={{
              display: { sm: 'flex', xs: 'flex' },
              flexDirection: { sm: 'column', xs: 'column', md: 'row' },
              gap: { xs: '15px' },
            }}
          >
            <Box
              sx={{
                width: { xs: '100%', md: 24 },
                height: { xs: 24 },
                borderRadius: '50%',
                backgroundColor: 'transparent',
                display: 'flex',
                alignItems: 'left',
                justifyContent: { xs: 'center', md: 'left' },
              }}
            >
              {React.cloneElement(icon, {
                fill: fillColor,
                size: { xs: 24 },
              })}
            </Box>
            <Typography
              className="label"
              sx={{
                transition: 'all 0.3s',
                color: isActive ? '#A24295' : '#A3A3A3',
                fontSize: { sm: 10, xs: 10, md: 14 },
                fontWeight: isActive ? 700 : 300,
                fontFamily: 'Plus Jakarta Sans',
                '&:hover': {
                  '& .label': {
                    color: '#A24295',
                    fontWeight: 700,
                  },
                },
              }}
            >
              {label}
            </Typography>
          </Stack>
        </Link>
      </ButtonBase>
      {isActive && (
        <Box
          sx={{
            width: '8px',
            height: '44px',
            borderTopLeftRadius: '8px',
            borderBottomLeftRadius: '8px',
            background: 'linear-gradient(to bottom, #A24295, #F92243)',
            position: 'absolute',
            right: { xs: -22 },
            top: 0,
            display: { sm: 'none', xs: 'none', md: 'block' },
          }}
        ></Box>
      )}
    </Box>
  );
}
