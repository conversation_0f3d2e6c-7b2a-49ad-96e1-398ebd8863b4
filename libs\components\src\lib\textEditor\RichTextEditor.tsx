import { useCallback, useMemo } from 'react';
import {
  createE<PERSON><PERSON>,
  Descendant,
  Editor,
  Transforms,
  Element as SlateElement,
} from 'slate';
import {
  Slate,
  Editable,
  withReact,
  useSlate,
  RenderElementProps,
  RenderLeafProps,
} from 'slate-react';
import { withHistory } from 'slate-history';
import { Box, Button, Divider, Toolbar, Typography } from '@mui/material';
import { Iconify } from '../iconify';
import { useTheme } from '@emotion/react';
import {
  CustomText,
  RichTextEditorProps,
  TextAlign,
} from './slate-custom-types';
import { BlockTypeDropdown } from './BlockTypeDropdown';

const LIST_TYPES = ['numbered-list', 'bulleted-list'] as const;
const TEXT_ALIGN_TYPES = ['left', 'center', 'right', 'justify'] as const;

const RichTextEditor = ({ value, onChange }: RichTextEditorProps) => {
  const editor = useMemo(() => withHistory(withReact(createEditor())), []);
  const theme: any = useTheme();

  const renderElement = useCallback(
    ({ attributes, children, element }: RenderElementProps) => {
      switch (element.type) {
        case 'block-code':
          return (
            <pre {...attributes}>
              <code>{children}</code>
            </pre>
          );
        case 'bulleted-list':
          return <ul {...attributes}>{children}</ul>;
        case 'numbered-list':
          return <ol {...attributes}>{children}</ol>;
        case 'list-item':
          return <li {...attributes}>{children}</li>;
        case 'blockquote':
          return <blockquote {...attributes}>{children}</blockquote>;
        case 'align-left':
        case 'align-center':
        case 'align-right':
        case 'align-justify':
          return (
            <div
              style={{
                textAlign: element.type.split('-')[1] as TextAlign | undefined,
              }}
              {...attributes}
            >
              {children}
            </div>
          );
        case 'heading-one':
          return <h1 {...attributes}>{children}</h1>;
        case 'heading-two':
          return <h2 {...attributes}>{children}</h2>;
        case 'heading-three':
          return <h3 {...attributes}>{children}</h3>;
        case 'heading-four':
          return <h4 {...attributes}>{children}</h4>;
        case 'heading-five':
          return <h5 {...attributes}>{children}</h5>;
        case 'heading-six':
          return <h6 {...attributes}>{children}</h6>;
        case 'paragraph':
        default:
          return <p {...attributes}>{children}</p>;
      }
    },
    []
  );

  const renderLeaf = useCallback(
    ({
      attributes,
      children,
      leaf,
    }: RenderLeafProps & { leaf: CustomText }) => {
      if (leaf.bold) children = <strong>{children}</strong>;
      if (leaf.italic) children = <em>{children}</em>;
      if (leaf.underline) children = <u>{children}</u>;
      if (leaf.code) children = <code>{children}</code>;
      if (leaf.strike) children = <s>{children}</s>;

      return <span {...attributes}>{children}</span>;
    },
    []
  );

  return (
    <Slate
      editor={editor}
      initialValue={value ? value : initialValue}
      onChange={onChange}
    >
      <Box
        sx={{
          border: '1px solid #A3A3A3',
          borderRadius: '8px',
          backgroundColor: '#fff',
        }}
      >
        <Box sx={{ position: 'relative', mx: '10px' }}>
          {/* Floating Label */}
          <Typography
            variant="body1"
            sx={{
              position: 'absolute',
              top: '-12px',
              left: '16px',
              px: '6px',
              backgroundColor: '#FFFFFF',
              fontWeight: 500,
              fontSize: theme.typography.pxToRem(16),
              color: (theme.palette as any).neutral?.[900] || '#1E1E1E',
              zIndex: 1,
            }}
          >
            Your Post
          </Typography>

          {/* Content Area with border */}
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <Editable
              placeholder="Write your content here!"
              renderLeaf={renderLeaf}
              renderElement={renderElement}
              style={{
                width: '1000px',
                maxWidth: '1000px',
                minHeight: '180px',
                outline: 'none',
                border: 'none',
                boxShadow: 'none',
                fontFamily: 'Plus Jakarta Sans, sans-serif',
                fontSize: theme.typography.pxToRem(14),
                color: (theme.palette as any).neutral?.[900] || '#1E1E1E',
                marginTop: '20px',
              }}
            />
          </Box>
        </Box>

        <Divider orientation="horizontal" sx={{ height: '20px' }} />
        <Toolbar
          sx={{
            gap: '10px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <BlockTypeDropdown
            toggleBlock={(format: string) => toggleBlock(editor, format)}
          />

          {/* MARKS */}
          <MarkButton format="bold" icon="material-symbols:format-bold" />
          <MarkButton format="italic" icon="material-symbols:format-italic" />
          <MarkButton
            format="underline"
            icon="material-symbols:format-underlined"
          />
          <MarkButton format="strike" icon="material-symbols:strikethrough-s" />

          <Divider orientation="vertical" sx={{ height: '20px' }} />
          {/* LISTS */}
          <BlockButton
            format="bulleted-list"
            icon="material-symbols:format-list-bulleted"
          />
          <BlockButton
            format="numbered-list"
            icon="material-symbols:format-list-numbered"
          />
          <Divider orientation="vertical" sx={{ height: '20px' }} />
          {/* ALIGNMENT */}
          <BlockButton
            format="align-left"
            icon="material-symbols:format-align-left"
          />
          <BlockButton
            format="align-center"
            icon="material-symbols:format-align-center"
          />
          <BlockButton
            format="align-right"
            icon="material-symbols:format-align-right"
          />
          <BlockButton
            format="align-justify"
            icon="material-symbols:format-align-justify"
          />
          <Divider orientation="vertical" sx={{ height: '20px' }} />

          <MarkButton format="code" icon="material-symbols:code" />

          <BlockButton
            format="block-code"
            icon="material-symbols-light:code-blocks-outline-sharp"
          />
          <Divider orientation="vertical" sx={{ height: '20px' }} />

          <BlockButton
            format="blockquote"
            icon="material-symbols:format-quote"
            transform="rotate(180deg)"
          />
        </Toolbar>
      </Box>
    </Slate>
  );
};

const MarkButton = ({ format, icon }: { format: string; icon: string }) => {
  const editor = useSlate();
  const theme: any = useTheme();
  const isActive = isMarkActive(editor, format);

  return (
    <Button
      onMouseDown={(event) => {
        event.preventDefault();
        toggleMark(editor, format);
      }}
      sx={{
        minWidth: '20px',
        color: isActive ? theme.palette.secondary.main : '#000000',
        '&:hover': { backgroundColor: 'transparent' },
      }}
    >
      <Iconify icon={icon} />
    </Button>
  );
};

const BlockButton = ({
  format,
  icon,
  transform,
}: {
  format: string;
  icon: string;
  transform?: string;
}) => {
  const editor = useSlate();
  const theme: any = useTheme();
  const isActive = isBlockActive(editor, format);

  return (
    <Button
      onMouseDown={(event) => {
        event.preventDefault();
        toggleBlock(editor, format);
      }}
      sx={{
        minWidth: '20px',
        color: isActive ? theme.palette.secondary.main : '#000000',
        '&:hover': { backgroundColor: 'transparent' },
        transform,
      }}
    >
      <Iconify icon={icon} />
    </Button>
  );
};

// MARK FUNCTIONS
const toggleMark = (editor: Editor, format: string) => {
  const isActive = isMarkActive(editor, format);
  if (isActive) {
    Editor.removeMark(editor, format);
  } else {
    Editor.addMark(editor, format, true);
  }
};

type CustomMarks = {
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  code?: boolean;
  strike?: boolean;
  [key: string]: unknown; // this is key to fix the error
};

const isMarkActive = (editor: Editor, format: string): boolean => {
  const marks = Editor.marks(editor) as CustomMarks | null;
  return marks ? marks[format] === true : false;
};

// BLOCK FUNCTIONS
const toggleBlock = (editor: Editor, format: string) => {
  const isActive = isBlockActive(editor, format);
  const isList = LIST_TYPES.includes(format as any);

  Transforms.unwrapNodes(editor, {
    match: (n) =>
      !Editor.isEditor(n) &&
      SlateElement.isElement(n) &&
      (LIST_TYPES.includes(n.type as any) ||
        TEXT_ALIGN_TYPES.includes(n.type as any)),
    split: true,
  });

  const newProperties: Partial<SlateElement> = {
    type: isActive ? 'paragraph' : format,
  };
  Transforms.setNodes(editor, newProperties);

  if (!isActive && isList) {
    const block = { type: format, children: [] };
    Transforms.wrapNodes(editor, block);
  }
};

const isBlockActive = (editor: Editor, format: string) => {
  const [match] = Editor.nodes(editor, {
    match: (n) =>
      !Editor.isEditor(n) && SlateElement.isElement(n) && n.type === format,
  });
  return !!match;
};

const initialValue: Descendant[] = [
  {
    type: 'paragraph',
    children: [
      {
        text: 'This is a rich text editor. Try bold, italic, underline, code, or alignment!',
      },
    ],
  },
];

export default RichTextEditor;
