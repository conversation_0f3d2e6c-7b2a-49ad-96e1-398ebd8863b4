import React from 'react';
import { CircularProgress, Typography, Backdrop } from '@mui/material';

interface FullPageLoaderProps {
  open: boolean;
  message?: string;
}

export const FullPageLoader: React.FC<FullPageLoaderProps> = ({ 
  open,
  message = '' // Default to empty string to not show any text
}) => {
  return (
    <Backdrop
      sx={{
        color: '#fff',
        zIndex: (theme) => theme.zIndex.drawer + 1,
        flexDirection: 'column',
        gap: 2,
        backgroundColor: '#FFFFFF', // Set white background
      }}
      open={open}
    >
      <CircularProgress color="secondary" /> 
 
      {message && message.length > 0 && (
        <Typography variant="h6" component="div">
          {message}
        </Typography>
      )}
    </Backdrop>
  );
};

export default FullPageLoader;
