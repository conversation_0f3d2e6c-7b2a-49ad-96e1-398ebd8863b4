/**
 * Subscription option type for different user types
 */
export interface SubscriptionOption {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  priceMonthly: number;
  priceYearly: number;
  assetUri: string;
  planFeatures: PlanFeature[];
}

// Export as a value, not just a type
export enum ValueType {
  BOOLEAN = 1,
  INTEGER = 2,
  TEXT = 3,
}

export interface PlanFeature {
  booleanValue: boolean | null;
  integerValue: number | null;
  textValue: string | null;
  subscriptionFeature: {
    id: string;
    key: string;
    name: string;
    type: ValueType;
    description: string | null;
  };
}

export interface SubscriptionOptionCardProps {
  isActive: boolean;
  onChoose: () => void;
  viewingPlan: boolean;
  option: SubscriptionOption;
}
