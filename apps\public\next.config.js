//@ts-check
const { generateRobotsTxt } = require('./scripts/robots.js');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const { composePlugins, withNx } = require('@nx/next');
const createNextIntlPlugin = require('next-intl/plugin');
const path = require('path');
/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  nx: {
    // Set this to true if you would like to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false,
  },
  transpilePackages: [
    '@minicardiac-client/shared',
    '@minicardiac-client/components',
    '@minicardiac-client/apis',
  ],
  experimental: {
    externalDir: true,
    esmExternals: true,
  },

  // Add API proxy for local development
  async rewrites() {
    // Use environment variable or default to localhost:9200
    const apiUrl =
      process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:9200/';
    // Remove trailing slash if present
    const baseUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;
    return [
      {
        source: '/api/:path*',
        destination: `${baseUrl}/api/:path*`, // Proxy to backend
      },
    ];
  },
  webpack: (config, { isServer }) => {
    if (isServer) {
      generateRobotsTxt();
    }
    // Handle .js extensions in imports
    config.resolve.extensionAlias = {
      '.js': ['.js', '.ts', '.tsx'],
      '.jsx': ['.jsx', '.tsx'],
    };
    // Add multiple specific aliases for different import patterns
    config.resolve.alias = {
      ...config.resolve.alias,
      '@minicardiac-client/shared': path.resolve(
        __dirname,
        '../../libs/shared/src'
      ),
      '@minicardiac-client/shared/lib/theme': path.resolve(
        __dirname,
        '../../libs/shared/src/lib/theme'
      ),
      // Fallback for the lib directory
      '@minicardiac-client/shared/lib': path.resolve(
        __dirname,
        '../../libs/shared/src/lib'
      ),
      // Components library
      '@minicardiac-client/components': path.resolve(
        __dirname,
        '../../libs/components/src'
      ),
      // APIs library
      '@minicardiac-client/apis': path.resolve(
        __dirname,
        '../../libs/apis/src'
      ),
    };
    // Add additional resolve modules paths
    config.resolve.modules = [
      path.resolve(__dirname, '../../libs/shared/src'),
      path.resolve(__dirname, '../../libs/shared/src/lib'),
      path.resolve(__dirname, '../../libs/components/src'),
      path.resolve(__dirname, '../../libs/components/src/lib'),
      path.resolve(__dirname, '../../libs/apis/src'),
      path.resolve(__dirname, '../../libs/apis/src/lib'),
      'node_modules',
      ...(config.resolve.modules || []),
    ];
    return config;
  },
  // Allow images from external domain (cdn)
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'dev.minicardiac.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'assets.dev.minicardiac.com',
        pathname: '**',
      },
    ],
  },
};
const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];
module.exports = composePlugins(...plugins)(withNextIntl(nextConfig));