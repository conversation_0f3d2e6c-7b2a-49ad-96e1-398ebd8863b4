const AddIcon = ({ fill = '#A3A3A3', hoverFill = '#A24295', size = 24 }) => {
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.5005 19.3343L30.3648 19.3347C30.7208 19.3285 31.0602 19.1827 31.3098 18.9288C31.5593 18.6748 31.6992 18.333 31.6992 17.9769C31.6992 17.6208 31.5593 17.279 31.3097 17.025C31.0601 16.771 30.7207 16.6253 30.3647 16.6191L19.5004 16.6187L19.5 5.75434C19.4938 5.39831 19.348 5.05896 19.094 4.80936C18.8401 4.55976 18.4982 4.41989 18.1422 4.41988C17.7861 4.41987 17.4442 4.55971 17.1903 4.80929C16.9363 5.05887 16.7906 5.39821 16.7844 5.75424L16.7848 16.6186L5.92046 16.6182C5.56795 16.6295 5.23367 16.7775 4.98837 17.0309C4.74306 17.2843 4.60598 17.6232 4.60612 17.9759C4.60625 18.3286 4.7436 18.6674 4.9891 18.9207C5.2346 19.1739 5.56899 19.3217 5.92152 19.3328L16.7849 19.3342L16.7853 30.1985C16.7821 30.3788 16.815 30.5579 16.8818 30.7254C16.9486 30.8929 17.0481 31.0454 17.1745 31.174C17.3009 31.3026 17.4517 31.4048 17.618 31.4745C17.7843 31.5442 17.9628 31.5801 18.1431 31.5802C18.3234 31.5802 18.502 31.5442 18.6683 31.4745C18.8345 31.4048 18.9853 31.3027 19.1117 31.1741C19.2381 31.0455 19.3376 30.893 19.4044 30.7255C19.4712 30.558 19.504 30.3789 19.5009 30.1986L19.5005 19.3343Z"
        fill={hoverFill}
      />
    </svg>
  );
};

export default AddIcon;
