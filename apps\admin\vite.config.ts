/// <reference types='vitest' />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import * as path from 'path';
import { exec } from 'child_process';
import tsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig(() => ({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/apps/admin',
  server: {
    port: 4200,
    host: 'localhost',
  },
  preview: {
    port: 4300,
    host: 'localhost',
  },
  plugins: [
    react(),
    tsconfigPaths(),
    {
      name: 'generate-robots-txt',
      buildStart() {
        // Run as a child process to ensure it works in all environments
        exec('node scripts/robots.cjs', (error, stdout, stderr) => {
          if (error) {
            console.error(`Error generating robots.txt: ${error.message}`);
            return;
          }
          if (stderr) {
            console.error(`robots.txt generation stderr: ${stderr}`);
            return;
          }
          console.log(stdout);
        });
      },
    },
  ],
  resolve: {
    alias: {
      '@minicardiac-client/shared': path.resolve(
        __dirname,
        '../../libs/shared/src'
      ),
      '@minicardiac-client/components': path.resolve(
        __dirname,
        '../../libs/components/src'
      ),
      '@minicardiac-client/apis': path.resolve(
        __dirname,
        '../../libs/apis/src'
      ),
    },
  },
  // Add test configuration
  test: {
    globals: true,
    environment: 'jsdom',
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    reporters: ['verbose'],
  },
  build: {
    outDir: './dist',
    emptyOutDir: true,
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true,
    },
    rollupOptions: {
      external: [
        'react',
        'react-dom',
        'react/jsx-runtime',
        '@minicardiac-client/components',
        '@minicardiac-client/apis',
      ],
    },
  },
}));
