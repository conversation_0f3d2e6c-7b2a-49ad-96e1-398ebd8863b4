'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import AuthLayout from '../components/AuthLayout';
import Welcome from '../components/Welcome';
import Subtitle from '../components/Subtitle';
import AuthTabs from '../components/AuthTabs';
import AuthTabContent from '../components/AuthTabContent';
import { Box } from '@mui/material';

interface SignUpPageTranslations {
  title: string;
  subtitle: string;
  displayNameLabel?: string;
  namePlaceholder?: string;
  emailLabel: string;
  passwordLabel: string;
  forgotPassword: string;
  continueLabel: string;
  orLabel: string;
  googleLabel: string;
  appleLabel: string;
  signIn: string;
  signUp: string;
}

interface SignupPageProps {
  userType: 'professional' | 'organization' | 'patient';
  onSignUp?: (data: {
    email: string;
    password: string;
    displayName?: string;
    organizationName?: string;
  }) => void;
  onBack?: () => void;
  isLoading?: boolean;
  error?: string | null;
  translations?: SignUpPageTranslations;
}

/**
 * SignupPage component for all signup types
 */
export const SignupPage = ({
  userType,
  onSignUp,
  onBack,
  isLoading,
  error,
  translations
}: SignupPageProps) => {
  const [activeTab, setActiveTab] = useState(1);
  const router = useRouter();
  const handleTabChange = (newTab: number) => {
    setActiveTab(newTab);

    if (newTab === 0) {
      // Wait for animation to complete
      setTimeout(() => {
        router.push('/signin');
      }, 500);
    }
  };

  const t = translations || {
    title: 'Welcome to MiniCardiac',
    subtitle: 'The heart of cardiac healthcare',
    displayNameLabel: 'Display Name',
    namePlaceholder: 'Enter your name',
    emailLabel: 'Email',
    passwordLabel: 'Password',
    forgotPassword: 'Forgot Password?',
    continueLabel: 'Continue',
    orLabel: 'OR',
    googleLabel: 'Continue with Google',
    appleLabel: 'Continue with Apple',
    signIn: 'Sign in',
    signUp: 'Sign up'
  };

  return (
    <AuthLayout
      showBackButton={true}
      onBackClick={onBack}
      activeTab={activeTab}
    >
      <Box
        sx={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          mt: { xs: '40px', sm: '40px', xxl: '100px' },
        }}
      >
        <Welcome title={t.title} />
        <Subtitle text={t.subtitle} />
        <AuthTabs
          value={activeTab}
          onChange={handleTabChange}
          signInLabel={t.signIn}
          signUpLabel={t.signUp}
        />
      </Box>
      <AuthTabContent
        activeTab={activeTab}
        onSubmit={onSignUp}
        onTypeSelect={(path: string) => router.push(path)}
        isLoading={isLoading}
        error={error}
        displayNameLabel={t.displayNameLabel}
        namePlaceholder={t.namePlaceholder}
        emailLabel={t.emailLabel}
        passwordLabel={t.passwordLabel}
        forgotPasswordLabel={t.forgotPassword}
        continueLabel={t.continueLabel}
        orLabel={t.orLabel}
        googleLabel={t.googleLabel}
        appleLabel={t.appleLabel}
      />
    </AuthLayout>
  );
};

export default SignupPage;
