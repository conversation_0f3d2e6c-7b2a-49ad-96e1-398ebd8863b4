const MediaIcon = ({ fill = '#A3A3A3', hoverFill = '#A24295', size = 24 }) => {
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18.4237 16C16.596 16 15.1094 17.4867 15.1094 19.3144C15.1094 21.1421 16.596 22.6273 18.4237 22.6273C20.2514 22.6273 21.7367 21.1407 21.7367 19.3144C21.7367 17.4867 20.25 16 18.4237 16Z"
        fill="url(#paint0_linear_1757_106367)"
      />
      <path
        d="M29.5663 8.18212H23.9288C23.3943 8.18212 22.8974 7.8898 22.6301 7.41792L22.0065 6.31127C21.5485 5.49693 20.7078 5 19.7793 5H16.4455C15.517 5 14.6762 5.49693 14.2183 6.31127L13.5946 7.41792C13.3274 7.88841 12.8304 8.18212 12.2959 8.18212H6.65701C5.24414 8.18212 4.11105 9.32772 4.11105 10.7281V24.5884C4.11105 26.0012 3.11133 31 7.11105 31H29.5663C33.1113 31 32.111 26.0012 32.111 24.5884V10.7281C32.111 9.32772 30.9653 8.18212 29.5663 8.18212ZM18.1114 26C13.5946 26 11.1113 22.2323 11.1113 19C11.1113 15.7803 13.1113 12.4316 18.1114 12.4316C22.6301 12.4316 25.1113 15.7802 25.1113 19C25.1113 22.2336 23.1113 26 18.1114 26ZM27.6688 13.9087H27.6562C26.9561 13.8962 26.3965 13.3366 26.3965 12.6364C26.3965 11.9362 26.9686 11.3641 27.6687 11.3641C28.3689 11.3767 28.9285 11.9362 28.9285 12.6364C28.9285 13.3366 28.369 13.8962 27.6688 13.9087Z"
        fill="url(#paint1_linear_1757_106367)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1757_106367"
          x1="18.423"
          y1="16"
          x2="18.423"
          y2="22.6273"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F92243" />
          <stop offset="1" stopColor="#A24295" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_1757_106367"
          x1="18.1206"
          y1="5"
          x2="18.1206"
          y2="31"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F92243" />
          <stop offset="1" stopColor="#A24295" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default MediaIcon;
