import { styled } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import Check from '@mui/icons-material/Check';
import StepConnector, {
  stepConnectorClasses,
} from '@mui/material/StepConnector';
import { StepIconProps } from '@mui/material/StepIcon';
import { Typography } from '@mui/material';

const QontoConnector = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 10,
    left: 'calc(-50% + 16px)',
    right: 'calc(50% + 16px)',
  },
  [`&.${stepConnectorClasses.active}, &.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: theme.palette.secondary.main, // Using theme secondary color (Rouge)
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    bgcolor: theme.palette.grey[300],
    borderColor: `${theme.palette.grey[500]}80`, // Using theme grey[500] with 50% opacity
    borderTopWidth: 2,
    borderRadius: 1,
  },
}));

const QontoStepIconRoot = styled('div')<{ ownerState: { active?: boolean } }>(
  ({ theme, ownerState }) => ({
    color: `${theme.palette.grey[500]}80`, // Using theme grey[500] with 50% opacity
    display: 'flex',
    height: 22,
    alignItems: 'center',
    ...(ownerState.active && {
      color: theme.palette.secondary.main, // Using theme secondary color (Rouge)
    }),
    '& .QontoStepIcon-completedIcon': {
      color: theme.palette.secondary.main, // Using theme secondary color
      zIndex: 1,
      fontSize: 18,
    },
    '& .QontoStepIcon-circle': {
      width: 8,
      height: 8,
      borderRadius: '50%',
      backgroundColor: 'currentColor',
    },
  })
);

function QontoStepIcon(props: StepIconProps) {
  const { active, completed, className } = props;
  return (
    <QontoStepIconRoot ownerState={{ active }} className={className}>
      {completed ? (
        <Check className="QontoStepIcon-completedIcon" />
      ) : (
        <div className="QontoStepIcon-circle" />
      )}
    </QontoStepIconRoot>
  );
}

export default function CustomizedSteppers({
  activeStep,
  steps,
}: {
  activeStep: number;
  steps: string[];
}) {
  return (
    <Stack
      sx={{
        mx: 'auto',
        maxWidth: 786,
        height: 62,
        width: '100%',
        alignItems: 'center',
      }}
    >
      <Stepper
        alternativeLabel
        activeStep={activeStep}
        connector={<QontoConnector />}
      >
        {steps.map((label, index) => {
          const isActive = index === activeStep;

          return (
            <Step
              key={label}
              sx={{
                p: 0,
                minWidth: { xs: '120px', sm: '262px' },
                maxWidth: { xs: '120px', sm: '262px' },
                flex: '0 0 262px',
              }}
            >
              <StepLabel
                slots={{ stepIcon: QontoStepIcon }}
                sx={{
                  '.MuiStepLabel-alternativeLabel': {},
                }}
              >
                <Typography
                  variant="body1"
                  sx={(theme) => ({
                    fontFamily: 'Plus Jakarta Sans',
                    fontWeight: isActive ? 600 : 400,
                    fontSize: { xs: '12px', smd: '20px' },
                    color: '#1E1E1E',
                  })}
                >
                  {label}
                </Typography>
              </StepLabel>
            </Step>
          );
        })}
      </Stepper>
    </Stack>
  );
}
