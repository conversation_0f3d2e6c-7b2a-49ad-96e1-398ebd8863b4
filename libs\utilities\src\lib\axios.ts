/**
 * @deprecated This file has been moved to @minicardiac-client/apis/lib/http-client.ts
 * Please import from '@minicardiac-client/apis' instead.
 *
 * NOTE: This file will be removed in a future release. All components should
 * migrate to using the apis library directly.
 */

import axios, { InternalAxiosRequestConfig, AxiosRequestConfig } from 'axios';


const HOST_API = typeof process !== 'undefined' && process.env && process.env.NEXT_PUBLIC_SERVER_URL
  ? `${process.env.NEXT_PUBLIC_SERVER_URL.endsWith('/')
      ? process.env.NEXT_PUBLIC_SERVER_URL.slice(0, -1)
      : process.env.NEXT_PUBLIC_SERVER_URL}/api`
  : '/api';

const createAxiosInstance = ({ baseURL }: { baseURL: string }) => {
  const customAxiosInstance = axios.create({
    baseURL,
    headers: {
      'Content-Type': 'application/json',
    },
  });

 
  customAxiosInstance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
   
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Basic response interceptor (without redirect for now)
  customAxiosInstance.interceptors.response.use(
    (response) => response,
    (error) => {
      // Error handling will be implemented later
      return Promise.reject(error);
    }
  );

  return customAxiosInstance;
};

export const axiosInstance = createAxiosInstance({ baseURL: HOST_API });

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  const [url, config] = Array.isArray(args) ? args : [args];
  const { data } = await axiosInstance.get(url, { ...config });
  return data?.data;
};
