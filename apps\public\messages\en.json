{"signin": {"title": "Welcome to MiniCardiac", "subtitle": "The heart of cardiac healthcare", "welcomeTo": "Welcome to", "emailLabel": "Email", "passwordLabel": "Password", "forgotPassword": "Forgot Password?", "continue": "Continue", "signIn": "Sign in", "signUp": "Sign up", "or": "OR", "google": "Continue with Google", "apple": "Continue with Apple"}, "signup": {"title": "Welcome to MiniCardiac", "subtitle": "The heart of cardiac healthcare", "welcomeTo": "Welcome to", "displayName": "Display Name", "name": "Name", "emailLabel": "Email", "passwordLabel": "Password", "forgotPassword": "Forgot Password?", "continue": "Continue", "signIn": "Sign in", "signUp": "Sign up", "or": "OR", "signUpAs": "Sign up as", "google": "Continue with Google", "apple": "Continue with Apple", "enterOtpSent": "Enter OTP sent to your registered email address", "resendOtp": "Resend OTP", "otpLabel": "OTP", "otpPlaceholder": "Enter OTP", "otpVerifiedSuccess": "OTP verified successfully!", "otpVerificationFailed": "Verification failed. No authentication token received.", "authenticationSuccessRedirect": "Authentication successful! Redirecting...", "otpResent": "OTP has been resent to your email", "otpInputPlaceholder": "6-digit OTP", "otpInstruction": "Enter OTP sent to your registered email address", "otpResentSuccess": "OTP resent successfully!", "sending": "Sending...", "verifying": "Verifying...", "missingEmailParam": "Missing email parameter. Please go back to the sign-up page.", "redirectingToProfile": "Redirecting to your profile...", "profileSetupForm": {"addProfilePicture": "+ Add Profile Picture", "introductoryStatement": "Introductory Statement", "introductoryStatementPlaceholder": "Write a couple of lines to introduce yourself to others in the community. If you would like to - this is entirely optional!", "loadingText": "Loading...", "saveAndContinue": "Save and Continue", "addPhotoLabel": "+Add Photo", "changePhotoLabel": "Change Photo", "removePhotoLabel": "Remove Photo", "characters": "characters"}, "loading": "Loading...", "genericErrorMessage": "Something went wrong. Please try again later."}, "signUpPublicFlow": {"subtitlePublicFlow": "To get you started, let's set up your public profile!"}}