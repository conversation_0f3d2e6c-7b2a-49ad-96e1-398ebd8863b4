import { useCallback, useEffect, useRef } from 'react';
import { Box, Stack, IconButton } from '@mui/material';
import CarouselSlide from './CarouselSlide';
import { Slide } from './types/carousel';
import { useCarousel } from './CarouselProvider'; // Adjust path if needed

interface CarouselProps {
  slides: Slide[];
}

const Carousel = ({ slides }: CarouselProps) => {
  const { currentIndex, setCurrentIndex } = useCarousel();
  const intervalRef = useRef<number | null>(null);

  const startAutoSlide = useCallback(() => {
    if (intervalRef.current !== null) clearInterval(intervalRef.current);
    intervalRef.current = window.setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % slides.length);
    }, 7000);
  }, [setCurrentIndex, slides.length]);

  useEffect(() => {
    startAutoSlide();
    return () => {
      if (intervalRef.current !== null) clearInterval(intervalRef.current);
    };
  }, [startAutoSlide]);

  const handleDotClick = (index: number) => {
    if (index === currentIndex) return;
    setTimeout(() => {
      setCurrentIndex(index);
      startAutoSlide();
    }, 300);
  };

  return (
    <Box sx={{ width: '100%', height: '100%', position: 'relative' }}>
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          minHeight: '100%',
          overflow: 'hidden',
        }}
      >
        {slides.map((slide, index) => (
          <Box
            key={index}
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              opacity: index === currentIndex ? 1 : 0,
              transition: 'opacity 0.5s ease',
              zIndex: index === currentIndex ? 5 : 0,
            }}
          >
            <CarouselSlide {...slide} />
          </Box>
        ))}
      </Box>

      <Stack
        direction="row"
        spacing={1}
        sx={{
          position: 'absolute',
          bottom: '40px',
          width: '100%',
          justifyContent: 'center',
          zIndex: 10,
        }}
      >
        {slides.map((_, index) => (
          <IconButton
            key={index}
            onClick={() => handleDotClick(index)}
            sx={{
              width: 12,
              height: 12,
              borderRadius: '50%',
              backgroundColor:
                currentIndex === index
                  ? 'rgba(255,255,255,0.9)'
                  : 'rgba(255,255,255,0.25)',
              transition: 'background-color 0.3s, transform 0.3s',
              padding: 0,
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.6)',
                transform: 'scale(1.2)',
              },
            }}
          />
        ))}
      </Stack>
    </Box>
  );
};

export default Carousel;
