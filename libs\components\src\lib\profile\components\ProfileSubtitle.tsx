import { Box, Typography, useTheme } from '@mui/material';
import { ExtendedTheme } from '../../auth/types/auth.types';

interface ProfileSubtitleProps {
  text: string;
}

/**
 * ProfileSubtitle component that displays a subtitle text for the profile page
 * according to the Figma design specifications
 */
export const ProfileSubtitle = ({ text }: ProfileSubtitleProps) => {
  const theme = useTheme() as ExtendedTheme;

  return (
    <Box
      sx={{
        width: '368px',
        height: '20px',
        position: 'relative',
        top: '83px',
        left: '456px',
        textAlign: 'center',
        // XL screen overrides
        [theme.breakpoints.up('xl')]: {
          top: '96px',
          left: '520px',
          width: '400px',
        },
      }}
    >
      <Typography
        sx={{
          fontFamily: theme.typography.fontFamily,
          fontWeight: 400,
          fontSize: '16px',
          lineHeight: '100%',
          letterSpacing: '0%',
          textAlign: 'center',
          color: (theme.palette as any).neutral[600] || '#737678',
          // XL font-size override
          [theme.breakpoints.up('xl')]: {
            fontSize: '18px',
          },
        }}
      >
        {text}
      </Typography>
    </Box>
  );
};

export default ProfileSubtitle;
