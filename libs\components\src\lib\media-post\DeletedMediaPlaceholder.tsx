import React from 'react';
import { Box, IconButton, Typography } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

interface DeletedMediaPlaceholderProps {
  onRemove: () => void;
  onUndo: () => void;
  previewUrl: File;
}

const DeletedMediaPlaceholder: React.FC<DeletedMediaPlaceholderProps> = ({
  onRemove,
  onUndo,
  previewUrl,
}) => {
  const fileURL = URL.createObjectURL(previewUrl);

  return (
    <Box
      sx={{
        width: { xs: '305px', sm: '237px' },
        height: { xs: '407px', sm: '237px' },
        position: 'relative',
        border: '2px dotted #A24295',
        borderRadius: '8px',
        padding: '20px',
        boxSizing: 'border-box',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '16px',
        justifyContent: 'center',
      }}
    >
      {/* Close Icon */}
      <IconButton
        onClick={() => {
          onRemove();
          onUndo();
        }}
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          width: 24,
          height: 24,
          color: '#A24295',
        }}
      >
        <CloseIcon fontSize="medium" />
      </IconButton>

      {/* Preview Image */}
      <Box
        component="img"
        src={fileURL}
        alt="undo-preview"
        width={{ xs: '100px', sm: '56px' }}
        height={{ xs: '100px', sm: '56px' }}
        sx={{
          objectFit: 'cover',
          borderRadius: '8px',
          flexShrink: 0,
        }}
      />

      {/* Text */}
      <Typography fontSize={12} fontWeight={500}>
        Media deleted
      </Typography>

      {/* Undo Button */}
      <Typography
        fontSize={16}
        fontWeight={600}
        color="#A24295"
        sx={{
          position: 'absolute',
          bottom: 20,
          left: '50%',
          transform: 'translateX(-50%)',
          cursor: 'pointer',
        }}
        onClick={onUndo}
      >
        Undo
      </Typography>
    </Box>
  );
};

export default DeletedMediaPlaceholder;
