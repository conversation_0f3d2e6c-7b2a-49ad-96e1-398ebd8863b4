'use client';

import React from 'react';
import { Box } from '@mui/material';

export interface FormContainerTemplateProps {
  children: React.ReactNode;
  variant?: 'student' | 'organization' | 'default';
}

/**
 * Shared form container template - copies exact CSS from existing form components
 * Used by: StudentProfileSetupForm, OrganisationProfileSetupForm, and other forms
 */
export const FormContainerTemplate: React.FC<FormContainerTemplateProps> = ({
  children,
  variant = 'default',
}) => {
  

  // Student form container CSS - copied exactly from StudentProfileSetupForm
  const studentContainerSx = {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    py: { xs: 3, sm: 5, md: 7 },
    px: { xs: 2, sm: 3, md: 5 },
    borderRadius: 1,
    boxShadow: (theme: any) =>
      `0px 12px 24px 0px ${(theme.palette as any).neutral[500]}25`,
    mb: 4,
  };

  // Organization form container CSS - copied exactly from OrganisationProfileSetupForm
  const organizationContainerSx = {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    px: { xs: 0, sm: 3, md: 5 },
    pb: { xs: '100px' },
    borderRadius: 1,
    boxShadow: { xs: 'none', sm: '0 12px 24px #A3A3A31F' },
    mb: 4,
  };

  // Default container CSS
  const defaultContainerSx = {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    p: 2,
    borderRadius: 1,
    mb: 4,
  };

  const getContainerSx = () => {
    switch (variant) {
      case 'student':
        return studentContainerSx;
      case 'organization':
        return organizationContainerSx;
      default:
        return defaultContainerSx;
    }
  };

  return (
    <Box sx={getContainerSx()}>
      {children}
    </Box>
  );
};

export default FormContainerTemplate;
