import { Fade, IconButton, Theme } from '@mui/material';
import Iconify from '../../iconify/iconify';
import { useRouter } from 'next/navigation';

type BackButtonProps = {
  handleBackButton?: () => void;
  visible?: boolean;
};

export const BackButton = ({
  handleBackButton,
  visible = true,
}: BackButtonProps) => {
  const router = useRouter();

  return (
    <Fade in={visible} timeout={0}>
      <IconButton
        onClick={() => router.back()}
        sx={(theme: Theme) => ({
          position: 'absolute',
          top: {
            xs: '0px',
            sm: '30px',
            lg: (theme as Theme & { customValues: any }).customValues.backButton
              .top,
            md: '0px',
          },
          left: {
            xs: '16px',
            sm: '24px',
            md: (theme as Theme & { customValues: any }).customValues.backButton
              .left,
          },
          transform: `rotate(${
            (theme as Theme & { customValues: any }).customValues.backButton
              .angle
          }deg)`,
          cursor: 'pointer',
          color: 'secondary.main',
          zIndex: 10,
        })}
      >
        <Iconify
          icon={'solar:arrow-left-linear'}
          sx={{
            color: 'secondary.main',
            fontSize: '35px',
            width: '35px',
            height: '35px',
          }}
        />
      </IconButton>
    </Fade>
  );
};
