import { useState } from 'react';

const WorkspaceIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const currentFill = isHovered ? hoverFill : fill;

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <path
        d="M14.4052 4C13.4522 4 12.6797 4.77246 12.6797 5.72535V10.3864C12.6797 11.3393 13.4522 12.1118 14.4052 12.1118H18.2745C19.2275 12.1118 20 11.3393 20 10.3864V5.72535C20 4.77246 19.2275 4 18.2745 4H14.4052Z"
        fill={currentFill}
      />
      <path
        d="M5.72551 4C4.77256 4 4 4.77246 4 5.72535V7.54576C4 8.49865 4.77256 9.27112 5.72551 9.27112H9.59484C10.5478 9.27112 11.3204 8.49865 11.3204 7.54576V5.72535C11.3204 4.77246 10.5478 4 9.59484 4H5.72551Z"
        fill={currentFill}
      />
      <path
        d="M5.72551 10.7521C4.77256 10.7521 4 11.5245 4 12.4774V18.2744C4 19.2273 4.77256 19.9998 5.72551 19.9998H9.59484C10.5478 19.9998 11.3204 19.2273 11.3204 18.2744V12.4774C11.3204 11.5245 10.5478 10.7521 9.59484 10.7521H5.72551Z"
        fill={currentFill}
      />
      <path
        d="M14.4052 13.4385C13.4522 13.4385 12.6797 14.2109 12.6797 15.1638V18.2742C12.6797 19.2271 13.4522 19.9996 14.4052 19.9996H18.2745C19.2275 19.9996 20 19.2271 20 18.2742V15.1638C20 14.2109 19.2275 13.4385 18.2745 13.4385H14.4052Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default WorkspaceIcon;
