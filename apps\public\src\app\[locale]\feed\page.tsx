'use client';

import DashboardLayout from '@/libs/components/src/lib/layout/DashboardLayout';
import { ProtectedRoute } from '../../../components/protected-route';
import { useEffect } from 'react';

export default function FeedPageWrapper() {
  useEffect(() => {
    document.title = 'Feed | MiniCardiac';
  }, []);

  // Middleware handles authentication, just render the protected content
  return (
    <ProtectedRoute>
      <DashboardLayout />
    </ProtectedRoute>
  );
}
