import { Box, Typography } from '@mui/material';
import { Slide } from './types/carousel';
import { LoadingButton } from '../loading-button';
import { ExtendedTheme } from '../auth';
import { useRouter } from 'next/navigation';

type SlideContentProps = Partial<Slide>;

const SlideContent = ({
  id,
  title,
  subtitle,
  description,
}: SlideContentProps) => {
  const router = useRouter();

  const handleSkipOrGetStarted = () => {
    router.push('/signin');
  };

  return (
    <>
      <Typography
        variant="h1"
        sx={{
          position: 'absolute',
          top: { xs: '5%', md: '30px' },
          left: '50%',
          transform: 'translateX(-50%)',
          fontFamily: 'Plus Jakarta Sans',
          fontWeight: 700,
          fontSize: { xs: '56px', md: '60px', lg: '96px' },
          color: '#ffffff',
          zIndex: 1,
          textAlign: 'center',
          maskImage: 'linear-gradient(to bottom, black 50%, transparent 100%)',
        }}
      >
        {title}
      </Typography>

      <Box
        sx={{
          position: 'absolute',
          bottom: { xs: '88px', md: '100px' },
          left: '5%',
          width: { xs: '90%', md: '472px' },
          color: '#FFFFFF',
          textAlign: { xs: 'center', md: 'left' },
          zIndex: 3,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 800,
            fontSize: { xs: '16px', md: '24px' },
            lineHeight: '100%',
            mb: 1,
          }}
        >
          {subtitle}
        </Typography>

        <Typography
          variant="body1"
          sx={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 500,
            fontSize: { xs: '16px', md: '24px' },
            lineHeight: '100%',
            mb: '32px',
          }}
        >
          {description}
        </Typography>

        <Box
          sx={{
            width: '100%',
            display: { xs: 'flex', md: 'none' },
            justifyContent: 'center',
            zIndex: 10,
          }}
        >
          {id !== undefined && id < 2 ? (
            <Box
              onClick={handleSkipOrGetStarted}
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: '600',
                color: 'white',
                fontSize: '16px',
                textDecoration: 'underline',
                textUnderlinePosition: 'under',
                cursor: 'pointer',
              }}
            >
              Skip
            </Box>
          ) : (
            <LoadingButton
              onClick={handleSkipOrGetStarted}
              sx={(theme: any) => ({
                width: {
                  xs: '360px',
                  sm: '360px',
                  md: '360px',
                  lg: '400px',
                },
                height: (theme as ExtendedTheme).customValues.button?.height,
                gap: (theme as ExtendedTheme).customValues.button?.spacing,
                backgroundColor: theme.palette.secondary?.main,
                '&:hover': {
                  backgroundColor: theme.palette.secondary?.main,
                },
                mt: '40px',
                position: 'relative',
                borderRadius: '8px',
              })}
            >
              Get Started
            </LoadingButton>
          )}
        </Box>
      </Box>
    </>
  );
};

export default SlideContent;
