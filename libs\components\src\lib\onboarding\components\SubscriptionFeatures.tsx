import React from 'react';
import { Box, Typography } from '@mui/material';

import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import RemoveCircleIcon from '@mui/icons-material/RemoveCircle';

import { PlanFeature, ValueType } from '../types/onboarding.types';

// Component for boolean type features (checkmarks)
const BooleanFeature: React.FC<{ value: boolean | null }> = ({ value }) => {
  return value === true ? (
    <CheckCircleIcon color="success" fontSize="small" />
  ) : value === false ? (
    <CancelIcon color="error" fontSize="small" />
  ) : (
    // Middle state when value is null
    <RemoveCircleIcon fontSize="small" sx={{ color: '#F9A825' }} />
  );
};

// Component for integer type features (quantity in circle)
const IntegerFeature: React.FC<{ value: number }> = ({ value }) => {
  // Special case for "unlimited"
  const isUnlimited = value === 1000;

  return (
    <Box
      sx={{
        padding: '5px',
        height: '24px',
        minWidth: '24px',
        borderRadius: '999px', // Use a large value to ensure perfect circle/pill shape
        backgroundColor: '#BE7BB5',
        fontWeight: 700,
        fontFamily: 'Plus Jakarta Sans',
        color: '#fff',
        fontSize: '12px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        whiteSpace: 'nowrap', // Prevent text wrapping
      }}
    >
      {isUnlimited ? 'Unlimited' : value}
    </Box>
  );
};

// Component for text type features
const TextFeature: React.FC<{ value: string }> = ({ value }) => {
  return (
    <Typography
      variant="body1"
      sx={{
        fontWeight: 700,
        color: '#BE7BB5',
        textAlign: 'center',
      }}
    >
      {value}
    </Typography>
  );
};

// Main Feature component that determines which type to render
export const FeatureItem: React.FC<{
  feature: PlanFeature;
  showText?: boolean;
}> = ({ feature, showText = true }) => {
  const { subscriptionFeature, booleanValue, integerValue, textValue } =
    feature;
  const { type } = subscriptionFeature;

  // Render based on type
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        ...(!showText && { justifyContent: 'center' }),
      }}
    >
      {type === ValueType.BOOLEAN && <BooleanFeature value={booleanValue} />}
      {type === ValueType.INTEGER && integerValue !== null && (
        <IntegerFeature value={integerValue} />
      )}
      {type === ValueType.TEXT && textValue && (
        <TextFeature value={textValue} />
      )}
      {showText && (
        <Typography variant="body2">{subscriptionFeature.name}</Typography>
      )}
    </Box>
  );
};
