{"name": "@minicardiac-client/source", "version": "0.0.0", "license": "MIT", "scripts": {"start:public": "nx serve public", "start:admin": "nx serve admin", "dev": "nx serve public"}, "private": true, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@emotion/babel-plugin": "11.11.0", "@eslint/compat": "^1.1.1", "@eslint/eslintrc": "^2.1.1", "@eslint/js": "^9.8.0", "@next/eslint-plugin-next": "^15.3.1", "@nrwl/react": "^19.8.4", "@nx/devkit": "20.7.1", "@nx/eslint": "20.7.1", "@nx/eslint-plugin": "20.7.1", "@nx/jest": "20.7.1", "@nx/js": "20.7.1", "@nx/next": "20.7.1", "@nx/playwright": "20.7.1", "@nx/react": "20.7.1", "@nx/vite": "20.7.1", "@nx/web": "20.7.1", "@playwright/test": "^1.36.0", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@swc/jest": "~0.2.36", "@testing-library/dom": "10.4.0", "@testing-library/react": "16.1.0", "@types/jest": "^29.5.12", "@types/node": "^20.0.0", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/react-lazy-load-image-component": "^1.6.4", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "^3.1.1", "@vitest/ui": "^3.0.0", "babel-jest": "^29.7.0", "embla-carousel": "^8.6.0", "eslint": "^9.8.0", "eslint-config-next": "^15.2.4", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-playwright": "^1.6.2", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "framer-motion": "^12.12.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jiti": "2.4.2", "jsdom": "~22.1.0", "jsonc-eslint-parser": "^2.1.0", "jwt-decode": "^4.0.0", "nx": "20.7.1", "prettier": "^2.6.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "vite": "^6.0.0", "vite-plugin-dts": "~4.5.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.0"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@hookform/resolvers": "^5.0.1", "@iconify/react": "^5.2.1", "@mui/icons-material": "^7.1.0", "@mui/lab": "7.0.0-beta.12", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.5.0", "@tanstack/react-query": "^5.74.4", "@tanstack/react-query-devtools": "^5.74.6", "@tiptap/extension-heading": "^2.12.0", "@tiptap/extension-horizontal-rule": "^2.12.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/axios": "^0.14.4", "axios": "^1.8.4", "browser-image-compression": "^2.0.2", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "draft-js": "^0.11.7", "embla-carousel-react": "^8.6.0", "firebase": "^11.6.1", "mui-tiptap": "^1.18.1", "next": "~15.2.4", "next-intl": "^4.1.0", "react": "19.0.0", "react-datepicker": "^8.4.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.1", "react-lazy-load-image-component": "^1.6.3", "react-router-dom": "6.29.0", "react-toastify": "^11.0.5", "slate": "^0.117.0", "slate-history": "^0.113.1", "slate-react": "^0.117.1", "swr": "^2.3.3", "yup": "^1.6.1", "zustand": "^5.0.4"}}