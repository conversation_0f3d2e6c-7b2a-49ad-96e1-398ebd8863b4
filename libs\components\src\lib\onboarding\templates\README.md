# Onboarding Templates - Code Duplication Fix

This directory contains shared templates that eliminate code duplication across Professional, Student, and Organization onboarding flows while **copying existing CSS exactly** without any modifications.

## 🎯 Templates Created

### 1. `DocumentUploadPageTemplate`
**Eliminates duplication in:**
- `apps/public/src/app/student/document-upload/page.tsx`
- `apps/public/src/app/organisation/subscription/free/document-upload/page.tsx`
- `apps/public/src/app/organisation/subscription/paid/document-upload/page.tsx`

**Exact CSS copied from existing pages:**
```tsx
<Container maxWidth="lg">
  <Box mt={'20px'}>
    {!isSmallScreen && (
      <PatientProfileWelcome patientName={userName} subtitle={''} />
    )}
    <BackButton handleBackButton={handleBack} />
    <Subtitle
      text={subtitleText}
      sx={{ fontSize: { xs: '12px', sm: '16px' } }}
      marginBottom={'34px'}
    />
    <CustomizedSteppers activeStep={currentStep} steps={steps} />
  </Box>
</Container>
```

### 2. `NetworkingPageTemplate`
**Eliminates duplication in:**
- `apps/public/src/app/student/network/page.tsx`
- `apps/public/src/app/organisation/subscription/free/add-network/page.tsx`
- `apps/public/src/app/organisation/subscription/paid/add-network/page.tsx`

**Same exact CSS structure as DocumentUploadPageTemplate**

### 3. `FormContainerTemplate`
**Eliminates duplication in:**
- `StudentProfileSetupForm` container CSS
- `OrganisationProfileSetupForm` container CSS

**Exact CSS variants copied:**
```tsx
// Student variant
sx={{
  width: '100%',
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  py: { xs: 3, sm: 5, md: 7 },
  px: { xs: 2, sm: 3, md: 5 },
  borderRadius: 1,
  boxShadow: (theme) => `0px 12px 24px 0px ${theme.palette.neutral[500]}25`,
  mb: 4,
}}

// Organization variant  
sx={{
  width: '100%',
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  px: { xs: 0, sm: 3, md: 5 },
  pb: { xs: '100px' },
  borderRadius: 1,
  boxShadow: { xs: 'none', sm: '0 12px 24px #A3A3A31F' },
  mb: 4,
}}
```

### 4. `ActionButtonsTemplate`
**Eliminates duplication in:**
- All profile setup forms
- All onboarding flows

**Exact CSS copied from OrganisationProfileSetupForm:**
```tsx
sx={{
  display: 'flex',
  justifyContent: 'center',
  gap: 2,
  py: '20px',
  ...(isXsScreen && {
    position: 'fixed',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    py: 2,
    boxShadow: '0 -2px 10px rgba(0,0,0,0.1)',
    zIndex: 999,
    flexDirection: 'column',
    alignItems: 'center',
  }),
}}
```

## 📖 Usage Examples

### Document Upload Page
```tsx
import { DocumentUploadPageTemplate, ActionButtonsTemplate } from '@minicardiac-client/components';

export default function MyDocumentUploadPage() {
  return (
    <DocumentUploadPageTemplate
      userName="John Doe"
      subtitleText="Let's set up your Professional Account!"
      currentStep={1}
      steps={['Profile Setup', 'Document Upload', 'Adding Network']}
    >
      <DocumentUploadForm hideSteppers={true} />
      
      <ActionButtonsTemplate
        onSave={handleSave}
        onSkip={handleSkip}
        isSubmitting={isSubmitting}
        variant="professional"
      />
    </DocumentUploadPageTemplate>
  );
}
```

### Networking Page
```tsx
import { NetworkingPageTemplate } from '@minicardiac-client/components';

export default function MyNetworkingPage() {
  return (
    <NetworkingPageTemplate
      userName="Jane Smith"
      subtitleText="Let's set up your Organization Account!"
      currentStep={2}
    >
      <ConnectWithOthers {...networkingProps} />
    </NetworkingPageTemplate>
  );
}
```

## ✅ Benefits

1. **Zero CSS Changes** - All existing CSS copied exactly
2. **Eliminates Duplication** - Shared logic across all user types
3. **Type Safety** - Full TypeScript support
4. **Easy Migration** - Drop-in replacements for existing code
5. **Consistent Behavior** - Same functionality across all flows

## 🔄 Migration Strategy

1. **Phase 1**: Use templates in new pages
2. **Phase 2**: Gradually migrate existing pages
3. **Phase 3**: Remove duplicated code after verification
4. **Phase 4**: Update imports to use barrel files

## 📁 File Structure
```
libs/components/src/lib/onboarding/templates/
├── DocumentUploadPageTemplate.tsx
├── NetworkingPageTemplate.tsx  
├── FormContainerTemplate.tsx
├── ActionButtonsTemplate.tsx
├── index.ts (barrel file)
├── examples/
│   └── StudentDocumentUploadExample.tsx
└── README.md
```
