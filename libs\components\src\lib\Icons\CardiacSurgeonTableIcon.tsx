const CardiacSurgeonTableIcon = ({
  fill = '#BCBCBC',
  hoverFill = '#000000',
  size = 24,
  hovered = false,
}) => {
  const currentFill = hovered ? hoverFill : fill;

  return (
    <svg
      width="143"
      height="16"
      viewBox="0 0 143 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0 18C0 18 6.32943 13.2732 13.061 8.73247C28.9432 -1.98031 74.6036 0.844358 77.7459 0.50492C80.8882 0.165022 107.855 -0.776374 118.643 1.33302C129.431 3.44288 143 18 143 18H0Z"
        fill="#617883"
      />
      <path
        opacity="0.2"
        d="M0 18C0 18 6.32943 13.2732 13.061 8.73247C28.9432 -1.98031 74.6036 0.844358 77.7459 0.50492C80.8882 0.165022 107.855 -0.776374 118.643 1.33302C129.431 3.44288 143 18 143 18H0Z"
        fill={currentFill}
      />
      <path
        d="M110.674 5.00368C110.674 5.00368 110.623 5.01703 110.523 5.03085C110.417 5.04421 110.271 5.0631 110.082 5.08751C109.686 5.13264 109.115 5.19758 108.383 5.2814C106.908 5.44905 104.772 5.65538 102.131 5.87691C99.4901 6.10213 96.3435 6.31537 92.8484 6.49822C89.3531 6.66909 85.5087 6.80311 81.4728 6.85562C77.4356 6.89983 73.5894 6.85838 70.0922 6.77133C66.595 6.67231 63.4463 6.5346 60.8035 6.37294C58.1606 6.21543 56.0225 6.06022 54.5459 5.92803C53.8131 5.86217 53.2418 5.81059 52.8458 5.77513C52.657 5.75532 52.5102 5.74012 52.404 5.72953C52.3046 5.71802 52.2539 5.70558 52.2539 5.70558C52.2539 5.70558 52.3052 5.70143 52.4049 5.70512C52.5115 5.71064 52.6585 5.71755 52.8476 5.72722C53.2439 5.75256 53.8159 5.78848 54.5492 5.835C56.0265 5.93863 58.1649 6.06805 60.8078 6.20391C63.4506 6.34393 66.5987 6.46367 70.095 6.55072C73.5916 6.62579 77.4362 6.66034 81.4719 6.61613C85.5063 6.56316 89.3491 6.4365 92.8438 6.27761C96.3383 6.10673 99.4846 5.91099 102.126 5.70788C104.767 5.50846 106.903 5.32792 108.38 5.18883C109.113 5.12481 109.684 5.0746 110.08 5.04006C110.269 5.02624 110.416 5.01519 110.523 5.00736C110.622 5.00046 110.674 5.00368 110.674 5.00368Z"
        fill="#263238"
      />
    </svg>
  );
};

export default CardiacSurgeonTableIcon;
