import { useState } from 'react';

const AddIcon = ({ fill = '#A3A3A3', hoverFill = '#A24295', size = 24 }) => {
  const [isHovered, setIsHovered] = useState(false);

  const currentFill = isHovered ? hoverFill : fill;
  return (
    <svg
      width="37"
      height="36"
      viewBox="0 0 37 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <rect
        x="0.5"
        y="0.25"
        width="35.5"
        height="35.5"
        rx="7.75"
        stroke={currentFill}
        strokeWidth="0.5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.7505 19.3343L30.6148 19.3347C30.9708 19.3285 31.3102 19.1827 31.5598 18.9288C31.8093 18.6748 31.9492 18.333 31.9492 17.9769C31.9492 17.6208 31.8093 17.279 31.5597 17.025C31.3101 16.771 30.9707 16.6253 30.6147 16.6191L19.7504 16.6187L19.75 5.75434C19.7438 5.39831 19.598 5.05896 19.344 4.80936C19.0901 4.55976 18.7482 4.41989 18.3922 4.41988C18.0361 4.41987 17.6942 4.55971 17.4403 4.80929C17.1863 5.05887 17.0406 5.39821 17.0344 5.75424L17.0348 16.6186L6.17046 16.6182C5.81795 16.6295 5.48367 16.7775 5.23837 17.0309C4.99306 17.2843 4.85598 17.6232 4.85612 17.9759C4.85625 18.3286 4.9936 18.6674 5.2391 18.9207C5.4846 19.1739 5.81899 19.3217 6.17152 19.3328L17.0349 19.3342L17.0353 30.1985C17.0321 30.3788 17.065 30.5579 17.1318 30.7254C17.1986 30.8929 17.2981 31.0454 17.4245 31.174C17.5509 31.3026 17.7017 31.4048 17.868 31.4745C18.0343 31.5442 18.2128 31.5801 18.3931 31.5802C18.5734 31.5802 18.752 31.5442 18.9183 31.4745C19.0845 31.4048 19.2353 31.3027 19.3617 31.1741C19.4881 31.0455 19.5876 30.893 19.6544 30.7255C19.7212 30.558 19.754 30.3789 19.7509 30.1986L19.7505 19.3343Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default AddIcon;
