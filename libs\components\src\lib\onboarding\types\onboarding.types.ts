// Import subscription types from the types library
import type { SubscriptionOption, PlanFeature } from '@minicardiac-client/types';
import { ValueType } from '@minicardiac-client/types';

// Re-export the types for backward compatibility
export type { SubscriptionOption, PlanFeature };
export { ValueType };

export interface SubscriptionOptionCardProps {
  isActive: boolean;
  onChoose: () => void;
  viewingPlan: boolean;
  option: SubscriptionOption;
}
