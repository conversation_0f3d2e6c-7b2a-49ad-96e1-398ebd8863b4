{"name": "@minicardiac-client/apis", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}, "./lib/auth/auth-store": {"types": "./dist/lib/auth/auth-store.d.ts", "import": "./dist/lib/auth/auth-store.js", "default": "./dist/lib/auth/auth-store.js"}}, "dependencies": {"@tanstack/react-query": "^5.0.0", "axios": "^1.6.0", "@minicardiac-client/types": "workspace:*", "swr": "^2.3.3", "react": "19.0.0", "firebase": "^11.6.0", "zustand": "^5.0.4", "jwt-decode": "^4.0.0"}}