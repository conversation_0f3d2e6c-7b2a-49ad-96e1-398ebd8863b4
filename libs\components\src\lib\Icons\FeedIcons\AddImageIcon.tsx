const AddImageIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M27.4286 9.42857H26.5714V8.57143C26.5694 7.89006 26.2979 7.23716 25.8161 6.75535C25.3343 6.27355 24.6814 6.00199 24 6H8.57143C7.89006 6.00199 7.23716 6.27355 6.75535 6.75535C6.27355 7.23716 6.00199 7.89006 6 8.57143V24C6.00199 24.6814 6.27355 25.3343 6.75535 25.8161C7.23716 26.2979 7.89006 26.5694 8.57143 26.5714H9.42857V27.4286C9.43056 28.1099 9.70212 28.7628 10.1839 29.2446C10.6657 29.7265 11.3186 29.998 12 30H27.4286C28.1099 29.998 28.7628 29.7265 29.2446 29.2446C29.7265 28.7628 29.998 28.1099 30 27.4286V12C29.998 11.3186 29.7265 10.6657 29.2446 10.1839C28.7628 9.70212 28.1099 9.43056 27.4286 9.42857ZM8.57143 24.8571C8.3443 24.8565 8.12667 24.766 7.96606 24.6054C7.80546 24.4448 7.71494 24.2271 7.71429 24V8.57143C7.71494 8.3443 7.80546 8.12667 7.96606 7.96606C8.12667 7.80546 8.3443 7.71494 8.57143 7.71429H24C24.2271 7.71494 24.4448 7.80546 24.6054 7.96606C24.766 8.12667 24.8565 8.3443 24.8571 8.57143V24C24.8565 24.2271 24.766 24.4448 24.6054 24.6054C24.4448 24.766 24.2271 24.8565 24 24.8571H8.57143ZM28.2857 27.4286C28.2851 27.6557 28.1945 27.8733 28.0339 28.0339C27.8733 28.1945 27.6557 28.2851 27.4286 28.2857H12C11.7729 28.2851 11.5552 28.1945 11.3946 28.0339C11.234 27.8733 11.1435 27.6557 11.1429 27.4286V26.5714H24C24.6814 26.5694 25.3343 26.2979 25.8161 25.8161C26.2979 25.3343 26.5694 24.6814 26.5714 24V11.1429H27.4286C27.6557 11.1435 27.8733 11.234 28.0339 11.3946C28.1945 11.5552 28.2851 11.7729 28.2857 12V27.4286Z"
        fill={hoverFill}
      />
      <path
        d="M22.2852 16.2856C22.2852 16.513 22.1949 16.731 22.0341 16.8917C21.8734 17.0525 21.6553 17.1428 21.428 17.1428H17.1423V21.4285C17.1423 21.6558 17.052 21.8738 16.8912 22.0346C16.7305 22.1953 16.5125 22.2856 16.2852 22.2856C16.0578 22.2856 15.8398 22.1953 15.6791 22.0346C15.5183 21.8738 15.428 21.6558 15.428 21.4285V17.1428H11.1423C10.915 17.1428 10.697 17.0525 10.5362 16.8917C10.3755 16.731 10.2852 16.513 10.2852 16.2856C10.2852 16.0583 10.3755 15.8403 10.5362 15.6796C10.697 15.5188 10.915 15.4285 11.1423 15.4285H15.428V11.1428C15.428 10.9155 15.5183 10.6974 15.6791 10.5367C15.8398 10.376 16.0578 10.2856 16.2852 10.2856C16.5125 10.2856 16.7305 10.376 16.8912 10.5367C17.052 10.6974 17.1423 10.9155 17.1423 11.1428V15.4285H21.428C21.6553 15.4285 21.8734 15.5188 22.0341 15.6796C22.1949 15.8403 22.2852 16.0583 22.2852 16.2856Z"
        fill={hoverFill}
      />
    </svg>
  );
};

export default AddImageIcon;
