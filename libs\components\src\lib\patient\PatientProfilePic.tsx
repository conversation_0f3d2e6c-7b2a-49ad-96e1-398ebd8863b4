import { ExtendedTheme } from '../auth';
import { ProfilePicture } from '../common/ProfilePicture';
import { useProfilePicture } from '../common/ProfilePicture';

type PatientProfilePicProps = {
  theme: ExtendedTheme;
  previewUrl: string | null;
  handleProfileImageChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  clearPreview: () => void;
  patientName: string;
  disabled?: boolean;
  changePhotoLabel?: string;
  addProfilePicture?: string;
  removePhotoLabel?: string;
};

const PatientProfilePic = ({
  theme,
  previewUrl,
  handleProfileImageChange,
  clearPreview,
  patientName,
  disabled = false,
  changePhotoLabel = 'Change photo',
  addProfilePicture = '+Add profile picture',
  removePhotoLabel = 'Remove photo',
}: PatientProfilePicProps) => {
  const {
    previewUrl: localPreviewUrl,
    isUploading,
    error,
    handleImageChange,
    handleRemove,
  } = useProfilePicture({
    initialImageUrl: previewUrl,
    onImageUpload: (imageUrl) => {
      // Instead of creating a synthetic event, we'll just call the handler directly
      // since we already have the image URL
      handleProfileImageChange({
        target: { value: imageUrl },
      } as React.ChangeEvent<HTMLInputElement>);
    },
  });

  return (
    <ProfilePicture
      theme={theme}
      previewUrl={localPreviewUrl}
      displayName={patientName}
      onImageChange={handleImageChange}
      onRemove={() => {
        handleRemove();
        clearPreview();
      }}
      isUploading={isUploading}
      error={error}
      disabled={disabled}
      changePhotoLabel={changePhotoLabel}
      addProfilePicture={addProfilePicture}
      removePhotoLabel={removePhotoLabel}
    />
  );
};

export default PatientProfilePic;
