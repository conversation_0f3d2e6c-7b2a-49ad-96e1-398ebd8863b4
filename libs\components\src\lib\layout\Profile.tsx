import { Box, Typography } from '@mui/material';
import { BLANK_PROFILE } from '../auth';
import Image from 'next/image';
import { getImageUrl, getInitials } from '@minicardiac-client/utilities';
import { useAuth } from '@minicardiac-client/apis';

type ProfileProps = {
  displayName?: string;
  photoURL?: string;
  size?: number;
};

export const Profile = ({ displayName, photoURL, size }: ProfileProps) => {
  const authState = useAuth();

  // Fallback to auth user values if props not provided
  const fallbackDisplayName =
    displayName ?? authState.authState.user?.displayName ?? '';
  let fallbackPhotoURL: string | null;
  if (photoURL) {
    fallbackPhotoURL = getImageUrl(photoURL);
  } else {
    fallbackPhotoURL = authState.authState.user?.photoURL ?? null;
  }
  // const fallbackPhotoURL = photoURL ?? authState.authState.user?.photoURL ?? '';
  

  const width = size ?? { xs: 40, smd: 50, lg: 104 };
  const height = size ?? { xs: 40, smd: 50, lg: 104 };
  const fontSize = size ? size * 0.4 : { sm: '1rem', md: '1.2rem', lg: '2rem' };

  return (
    <Box
      sx={{
        width,
        height,
        borderRadius: '50%',
        backgroundColor: '#A2429514',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
        overflow: 'hidden',
        mx: 'auto',
        cursor: 'pointer',
      }}
    >
      {/* Background image */}
      <Box
        component="img"
        src={BLANK_PROFILE}
        alt="Background pattern"
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          zIndex: 0,
          transform: 'scale(1.05)',
          transformOrigin: 'center',
        }}
      />

      {/* Profile photo or initials */}
      {fallbackPhotoURL ? (
        <Image
          src={fallbackPhotoURL}
          alt="Profile Picture"
          fill
          style={{ objectFit: 'cover', zIndex: 1, borderRadius: '50%' }}
        />
      ) : (
        <Typography
          sx={{
            fontSize,
            fontWeight: 500,
            color: (theme) =>
              (theme.palette as any).neutral?.[900] || '#1E1E1E',
            position: 'relative',
            zIndex: 1,
          }}
        >
          {getInitials(fallbackDisplayName)}
        </Typography>
      )}
    </Box>
  );
};
