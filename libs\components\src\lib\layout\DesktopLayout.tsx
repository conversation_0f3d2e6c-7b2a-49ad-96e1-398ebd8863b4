'use client';
import { Box } from '@mui/material';
import TopBar from './TopBar';
import MainContent from './MainContent';
import RightSidebar from './RightSidebar';
import Sidebar from './Sidebar';
import ContextTextPost from '../content-posts/ContentTextPost';
import ContentMediaPost from '../content-posts/ContentMediaPost';
import React from 'react';
import ContentArticlePost from '../content-posts/ContentArticlePost';

export default function DesktopLayout() {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: { smd: 'space-between', md: 'center', lg: 'center' },
        width: '100%',
        backgroundColor: '#F3F4F6',
        overflow: 'hidden',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          width: '100%',
          maxWidth: { lg: '1280px' },
          gap: { smd: '20px', lg: '40px' },
          height: '100vh',
        }}
      >
        {/* Sidebar */}
        <Box
          sx={{
            height: '100vh',
            overflowY: 'auto',
            backgroundColor: 'white',
            width: { sm: 100, xs: 100, md: 224 },
            minWidth: { sm: 100, xs: 100, md: 224 },
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
          }}
        >
          <Sidebar />
        </Box>

        {/* Main */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            overflow: 'hidden',
            gap: '40px',
            justifyContent: 'space-between',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              gap: '20px',

              overflow: 'hidden',
              flexDirection: 'column',
            }}
          >
            <TopBar />
            <Box
              sx={{
                overflowY: 'auto',
                width: { smd: '100%', lg: '719px' },
                pr: { smd: '20px', lg: '0px' },
                scrollbarWidth: 'none',
                '&::-webkit-scrollbar': {
                  display: 'none',
                },
              }}
            >
              <MainContent>
                {[...Array(10)].map((_, index) => (
                  <React.Fragment key={index}>
                    <ContentArticlePost />
                    <ContentMediaPost />
                    <ContextTextPost />
                  </React.Fragment>
                ))}
              </MainContent>
            </Box>
          </Box>
          <RightSidebar />
        </Box>
      </Box>
    </Box>
  );
}
