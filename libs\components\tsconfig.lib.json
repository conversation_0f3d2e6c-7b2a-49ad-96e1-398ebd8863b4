{"extends": "../../tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "outDir": "dist", "types": ["node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts", "vite/client"], "rootDir": "src", "jsx": "react-jsx", "module": "esnext", "moduleResolution": "bundler", "lib": ["dom", "dom.iterable", "esnext"], "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo", "paths": {"@minicardiac-client/apis/lib/*": ["../apis/src/lib/*"]}}, "exclude": ["out-tsc", "dist", "**/*.spec.ts", "**/*.test.ts", "**/*.spec.tsx", "**/*.test.tsx", "**/*.spec.js", "**/*.test.js", "**/*.spec.jsx", "**/*.test.jsx", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"], "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"], "references": [{"path": "../apis/tsconfig.lib.json"}, {"path": "../constants/tsconfig.lib.json"}, {"path": "../utilities/tsconfig.lib.json"}, {"path": "../shared/tsconfig.lib.json"}, {"path": "../types/tsconfig.lib.json"}]}