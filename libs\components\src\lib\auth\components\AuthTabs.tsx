'use client';

import { Tabs, Tab } from '@mui/material';
import { FC } from 'react';

interface AuthTabsProps {
  value: number;
  onChange: (newValue: number) => void;
  signInLabel: string;
  signUpLabel: string;
}

const AuthTabs: FC<AuthTabsProps> = ({ value, onChange, signInLabel, signUpLabel }) => {
  return (
    <Tabs value={value} onChange={(_, newValue) => onChange(newValue)}>
      <Tab
        label={signInLabel}
        sx={(theme) => ({
          padding: 0,
          fontSize: { xs: '18px', sm: '20px', md: '20px' },
          fontWeight: 400,
          minWidth: 'unset',
          fontFamily: "'Plus Jakarta Sans', sans-serif",
          color: (theme.palette as any).neutral[600],
          '&.Mui-selected': {
            fontWeight: 500,
            color: theme.palette.secondary.main,
          },
        })}
      />
      <Tab
        label={signUpLabel}
        sx={(theme) => ({
          padding: 0,
          fontSize: { xs: '18px', sm: '20px', md: '20px' },
          fontWeight: 400,
          minWidth: 'unset',
          fontFamily: "'Plus Jakarta Sans', sans-serif",
          color: (theme.palette as any).neutral[600],
          '&.Mui-selected': {
            fontWeight: 500,
            color: theme.palette.secondary.main,
          },
        })}
      />
    </Tabs>
  );
};

export default AuthTabs;
