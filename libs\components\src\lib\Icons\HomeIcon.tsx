import { useState } from 'react';

const HomeIcon = ({ fill = '#A3A3A3', hoverFill = '#A24295', size = 24 }) => {
  const [isHovered, setIsHovered] = useState(false);
  const currentFill = isHovered ? hoverFill : fill;

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.9892 5H12.0108C12.0541 5 12.1191 5.02165 12.1624 5.06495L19.935 11.9066C20.0217 11.9932 20.0217 12.1231 19.935 12.2097L19.6103 12.4696C19.5237 12.5562 19.3721 12.5562 19.2855 12.4696L17.6184 10.9973V17.9256C17.6184 18.5535 17.1204 19.0731 16.5142 19.0731H14.1543V13.747C14.1543 13.5088 13.9594 13.3139 13.7212 13.3139H10.2571C10.0406 13.3139 9.84574 13.5088 9.84574 13.747V19.0731H7.46414C6.85792 19.0731 6.35995 18.5535 6.35995 17.9256V10.9973L4.69283 12.4696C4.60622 12.5562 4.45467 12.5562 4.36806 12.4696L4.06495 12.2097C3.97835 12.1231 3.97835 11.9932 4.06495 11.9066L11.8376 5.06495C11.8809 5.02165 11.9242 5 11.9892 5Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default HomeIcon;
