'use client';

import { useState, useEffect } from 'react';
import { useMediaQuery, useTheme } from '@mui/material';

export const useScreenBelowSM = () => {
  const theme = useTheme();
  const isBelowSM = useMediaQuery(theme.breakpoints.down('sm'));

  const [screenBelowSM, setScreenBelowSM] = useState(isBelowSM);

  useEffect(() => {
    setScreenBelowSM(isBelowSM);
  }, [isBelowSM]);

  return screenBelowSM;
};
