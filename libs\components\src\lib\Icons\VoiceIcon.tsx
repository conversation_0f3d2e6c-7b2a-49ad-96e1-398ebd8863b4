import { useState } from 'react';

const VoiceIcon = ({ fill = '#A3A3A3', hoverFill = '#A24295', size = 24 }) => {
  const [isHovered, setIsHovered] = useState(false);
  const currentFill = isHovered ? hoverFill : fill;

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <path
        d="M11.8088 16.0308H12.1838C12.701 16.0318 13.2133 15.9297 13.6914 15.7305C14.1696 15.5313 14.6041 15.2387 14.9702 14.8697C15.3363 14.5006 15.6267 14.0622 15.8249 13.5796C16.023 13.0971 16.125 12.5797 16.125 12.0573V5.99669C16.126 5.47425 16.025 4.95674 15.8277 4.47378C15.6305 3.99082 15.3409 3.55189 14.9755 3.18212C14.6101 2.81234 14.1761 2.51898 13.6984 2.31882C13.2206 2.11866 12.7085 2.01563 12.1913 2.01563H11.8163C11.299 2.01464 10.7867 2.11668 10.3086 2.31592C9.83044 2.51516 9.3959 2.80769 9.02983 3.17676C8.66375 3.54583 8.37332 3.9842 8.17516 4.46678C7.977 4.94936 7.87501 5.46667 7.87501 5.98912V12.0497C7.87402 12.5722 7.97504 13.0897 8.17229 13.5726C8.36954 14.0556 8.65914 14.4945 9.02452 14.8643C9.3899 15.2341 9.82389 15.5274 10.3016 15.7276C10.7794 15.9278 11.2915 16.0308 11.8088 16.0308ZM9.37501 5.98912C9.376 5.33743 9.63273 4.71273 10.0889 4.25192C10.5451 3.79111 11.1636 3.53179 11.8088 3.53078H12.1838C12.5039 3.53029 12.8209 3.59348 13.1168 3.71675C13.4128 3.84002 13.6817 4.02097 13.9084 4.24924C14.1351 4.47752 14.3151 4.74866 14.438 5.04719C14.561 5.34572 14.6245 5.66579 14.625 5.98912V12.0497C14.6255 12.3731 14.5629 12.6933 14.4409 12.9922C14.3189 13.2911 14.1397 13.5628 13.9137 13.7918C13.6877 14.0208 13.4193 14.2026 13.1238 14.3268C12.8282 14.451 12.5114 14.5151 12.1913 14.5156H11.8163C11.4962 14.5161 11.1791 14.4529 10.8832 14.3297C10.5873 14.2064 10.3183 14.0255 10.0916 13.7972C9.86489 13.5689 9.68494 13.2978 9.56199 12.9992C9.43903 12.7007 9.3755 12.3806 9.37501 12.0573V5.98912Z"
        fill={currentFill}
      />
      <path
        d="M19.125 11.8641C19.125 11.6632 19.046 11.4705 18.9053 11.3285C18.7647 11.1864 18.5739 11.1066 18.375 11.1066C18.1761 11.1066 17.9853 11.1864 17.8447 11.3285C17.704 11.4705 17.625 11.6632 17.625 11.8641C17.625 13.3711 17.0324 14.8162 15.9775 15.8818C14.9226 16.9473 13.4918 17.546 12 17.546C10.5082 17.546 9.07742 16.9473 8.02252 15.8818C6.96763 14.8162 6.375 13.3711 6.375 11.8641C6.375 11.6632 6.29598 11.4705 6.15533 11.3285C6.01468 11.1864 5.82391 11.1066 5.625 11.1066C5.42609 11.1066 5.23532 11.1864 5.09467 11.3285C4.95402 11.4705 4.875 11.6632 4.875 11.8641C4.87541 13.6414 5.52687 15.3557 6.70389 16.6768C7.88091 17.9979 9.50025 18.8324 11.25 19.0194V19.0611V21.7126H8.625C8.42609 21.7126 8.23532 21.7924 8.09467 21.9345C7.95402 22.0766 7.875 22.2693 7.875 22.4702C7.875 22.6711 7.95402 22.8638 8.09467 23.0059C8.23532 23.148 8.42609 23.2278 8.625 23.2278H15.75C15.9489 23.2278 16.1397 23.148 16.2803 23.0059C16.421 22.8638 16.5 22.6711 16.5 22.4702C16.5 22.2693 16.421 22.0766 16.2803 21.9345C16.1397 21.7924 15.9489 21.7126 15.75 21.7126H12.75V19.0611C12.75 19.0611 12.75 19.0346 12.75 19.0194C14.4998 18.8324 16.1191 17.9979 17.2961 16.6768C18.4731 15.3557 19.1246 13.6414 19.125 11.8641Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default VoiceIcon;
