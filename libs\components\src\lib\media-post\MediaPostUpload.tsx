import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Box, Typography, useMediaQuery } from '@mui/material';
import { useTheme } from '@emotion/react';
import MediaUploadIcon from '../Icons/FeedIcons/MediaUploadIcon';
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  rectSortingStrategy,
} from '@dnd-kit/sortable';
import { SortableItem } from './SortableItem';
import imageCompression from 'browser-image-compression';
import MediaPostGallerySelector from './MediaPostGallerySelector';
import { AddMoreMediaButton } from './AddMoreMediaButton';

interface MediaPostUploadProps {
  onFilesChange: (files: File[]) => void;
}

const compressImage = async (file: File): Promise<File> => {
  const options = {
    maxSizeMB: 1,
    maxWidthOrHeight: 1920,
    useWebWorker: true,
  };
  try {
    if (file.size / 1024 / 1024 > 1) {
      return await imageCompression(file, options);
    }
    console.log(file);
    return file;
  } catch (err) {
    console.error('Compression error:', err);
    return file;
  }
};

const MediaPostUpload: React.FC<MediaPostUploadProps> = ({ onFilesChange }) => {
  const [files, setFiles] = useState<File[]>([]);
  const sensors = useSensors(useSensor(PointerSensor));

  const theme: any = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const compressedFiles = await Promise.all(
        acceptedFiles.map(compressImage)
      );
      const newFiles = [...files, ...compressedFiles];
      setFiles(newFiles);
      onFilesChange(newFiles);
    },
    [files, onFilesChange]
  );

  const { getRootProps, getInputProps, open, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': [],
      'video/*': [],
      'application/pdf': [],
    },
    multiple: true,
    noClick: files.length > 0,
    noKeyboard: files.length > 0,
  });

  const removeFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index);
    setFiles(newFiles);
    onFilesChange(newFiles);
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    if (active.id !== over?.id) {
      const oldIndex = files.findIndex((_, i) => `file-${i}` === active.id);
      const newIndex = files.findIndex((_, i) => `file-${i}` === over?.id);
      const newFiles = arrayMove(files, oldIndex, newIndex);
      setFiles(newFiles);
      onFilesChange(newFiles);
    }
  };

  return screenBelowSM ? (
    <MediaPostGallerySelector
      files={files}
      setFiles={setFiles}
      onFilesChange={onFilesChange}
    />
  ) : (
    <Box
      sx={{
        width: '100%',
        height: '420px',
        backgroundColor: isDragActive ? 'secondary.light' : 'white',
        cursor: files.length === 0 ? 'pointer' : 'default',
        border: `1px solid ${theme.palette.secondary.main}`,
        borderRadius: '8px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        px: 2,
        pt: 2,
        position: 'relative',
        ...(files.length === 0 && {
          '&:hover': { backgroundColor: theme.palette.secondary.light },
        }),
      }}
      {...getRootProps()}
    >
      <input {...getInputProps()} />

      {files.length === 0 ? (
        <Box
          textAlign="center"
          display={'flex'}
          flexDirection={'column'}
          alignItems={'center'}
        >
          <MediaUploadIcon />
          <Typography fontSize="20px" mt={'12px'} fontWeight={500}>
            Click to add media
          </Typography>
          <Typography
            fontSize="16px"
            mt={'4px'}
            color={theme.palette.neutral[500]}
          >
            Or drag and drop
          </Typography>
        </Box>
      ) : (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
          }}
        >
          <Typography
            fontSize="16px"
            fontWeight={500}
            color="#737678"
            mb={'12px'}
          >
            Click and drag images to re-order them.
          </Typography>

          <Box
            sx={{
              flex: 1,
              overflowY: 'auto',
              marginBottom: '60px',
              '&::-webkit-scrollbar': { display: 'none' },
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
            }}
          >
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={files.map((_, i) => `file-${i}`)}
                strategy={rectSortingStrategy}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    justifyContent: 'center',
                    gap: '20px',
                    maxWidth: '100%',
                  }}
                >
                  {files.map((file, index) => (
                    <SortableItem
                      key={`file-${index}`}
                      id={`file-${index}`}
                      file={file}
                      index={index}
                      onRemove={removeFile}
                      open={open}
                    />
                  ))}
                </Box>
              </SortableContext>
            </DndContext>
          </Box>

          <AddMoreMediaButton open={open} />
        </Box>
      )}
    </Box>
  );
};

export default MediaPostUpload;
