import { useState } from 'react';

const PublicationsIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const currentFill = isHovered ? hoverFill : fill;

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <path
        d="M7.33325 4C6.59675 4 6 4.60001 6 5.33327V17.3335C6 18.0667 6.597 18.6667 7.33325 18.6667H8.6665V20L10.3333 18.6667L12 20V18.6667H16.6667C17.4035 18.6667 18 18.0667 18 17.3335V16.6667C18 17.4 17.4032 18 16.6667 18H12V16.6667H8.66675V18H7.66675C7.1145 18 6.66675 17.5332 6.66675 17C6.66675 16.3999 7.1145 15.9999 7.66675 15.9999H16.6667C17.4035 15.9999 18 15.3999 18 14.6667V5.33327C18 4.60001 17.4032 4 16.6667 4L7.33325 4Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default PublicationsIcon;
