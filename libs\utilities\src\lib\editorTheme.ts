import { createTheme } from '@mui/material/styles';

export const editorTheme = createTheme({
  components: {
    MuiIconButton: {
      styleOverrides: {
        root: {
          '&:hover': {
            color: '#A24295',
            backgroundColor: 'rgba(162, 66, 149, 0.1)',
          },
        },
      },
    },
    MuiToggleButton: {
      styleOverrides: {
        root: {
          '&.Mui-selected': {
            color: '#A24295',
            backgroundColor: 'rgba(162, 66, 149, 0.1)',
          },
          '&.Mui-selected:hover': {
            backgroundColor: 'rgba(162, 66, 149, 0.15)',
          },
          '&:hover': {
            color: '#A24295',
            backgroundColor: 'rgba(162, 66, 149, 0.1)',
          },
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        root: {
          border: '1px solid #A3A3A3',
          borderRadius: '8px',
          padding: '4px 8px',
          minHeight: '36px',
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: '#A24295',
          },
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          '&.Mui-selected': {
            borderRadius: '8px',
            color: '#A24295',
            backgroundColor: 'rgba(162, 66, 149, 0.1)',
          },
          '&.Mui-selected:hover': {
            backgroundColor: 'rgba(162, 66, 149, 0.15)',
          },
        },
      },
    },
  },
});
