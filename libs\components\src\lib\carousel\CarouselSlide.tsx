import { Box } from '@mui/material';
import FullImage from './FullImage';
import OverlayGradient from './OverlayGradient';
import SlideContent from './SlideContent';
import { Slide } from './types/carousel';

const CarouselSlide = ({
  id,
  bgImage,
  foregroundImage,
  title,
  subtitle,
  description,
  position,
}: Slide) => {
  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        minHeight: '100%',
        overflow: 'hidden',
      }}
    >
      {/* Background Image */}
      <FullImage src={bgImage} position={position} zIndex={1} />
      <OverlayGradient />

      {/* Foreground Image */}
      <FullImage src={foregroundImage} position={position} zIndex={2} />
      <SlideContent
        id={id}
        title={title}
        subtitle={subtitle}
        description={description}
      />
    </Box>
  );
};

export default CarouselSlide;
