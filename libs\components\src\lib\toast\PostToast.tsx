import { Box, Typography } from '@mui/material';
import { Iconify } from '../iconify';

const PostToast = ({
  value,
  closeToast,
}: {
  value: string;
  closeToast?: () => void;
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        backgroundColor: 'white',
        borderRadius: '8px',
        paddingRight: '12px',
        height: '84px',
        gap: '18px',
      }}
    >
      {/* Green tick section */}
      <Box
        sx={{
          backgroundColor: '#00C96B',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: '12px',
          borderTopLeftRadius: '8px',
          borderBottomLeftRadius: '8px',
        }}
      >
        <Box
          sx={{
            width: 'fit-content',
            height: 'fit-content',
            borderRadius: '50%',
            backgroundColor: 'white',
          }}
        >
          <Iconify
            icon="material-symbols:check-small-rounded"
            sx={{
              width: 24,
              height: 24,
              color: '#00C96B',
            }}
          />
        </Box>
      </Box>

      {/* Text section */}
      <Box
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          gap: '4px',
          px: '12px',
          py: '8px',
        }}
      >
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: '16px',
            color: '#1E1E1E',
          }}
        >
          {value}
        </Typography>
        <Typography
          sx={{
            fontWeight: 600,
            fontSize: '16px',
            color: 'secondary.main',
            cursor: 'pointer',
          }}
        >
          View
        </Typography>
      </Box>

      <Box sx={{ cursor: 'pointer', ml: '8px' }} onClick={closeToast}>
        <Iconify
          icon="eva:close-fill"
          width={40}
          height={40}
          color={'#A24295'}
        />
      </Box>
    </Box>
  );
};

export default PostToast;
