'use client';

import { Box, IconButton, TextField } from '@mui/material';
import { useState, forwardRef } from 'react';
import Iconify from '../../iconify/iconify';

interface PasswordFieldProps {
  label?: string;
  placeholder?: string;
  fullWidth?: boolean;
  size?: 'small' | 'medium';
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  showPassword?: boolean;
  onToggleVisibility?: () => void;
}

/**
 * PasswordField component with visibility toggle
 */
export const PasswordField = forwardRef<HTMLInputElement, PasswordFieldProps>(
  (
    {
      label = 'Password',
      placeholder = '**********',
      fullWidth = true,
      size = 'small',
      value,
      onChange,
      disabled = false,
      error = false,
      helperText,
      showPassword = false,
      onToggleVisibility,
      ...rest
    },
    ref
  ) => {
    const [localShowPassword, setLocalShowPassword] = useState(showPassword);

    const handleToggleVisibility = () => {
      if (onToggleVisibility) {
        onToggleVisibility();
      } else {
        setLocalShowPassword(!localShowPassword);
      }
    };

    return (
      <Box sx={{ position: 'relative', width: '100%' }}>
        <TextField
          fullWidth={fullWidth}
          label={label}
          type={showPassword ? 'text' : 'password'}
          placeholder={placeholder}
          variant="outlined"
          size={size}
          value={value}
          onChange={onChange}
          disabled={disabled}
          error={error}
          helperText={helperText}
          inputRef={ref}
          {...rest}
          InputLabelProps={{
            shrink: true,
            sx: {
              fontSize: '20px',
              fontWeight: 600,
              fontFamily: 'Plus Jakarta Sans, sans-serif',
            },
          }}
          inputProps={{
            style: {
              height: '45px',
              boxSizing: 'border-box',
            },
          }}
          sx={{
            '& .MuiInputLabel-root.Mui-error': {
              color: '#FF5C5C',
            },
            '& .MuiInputBase-input': {
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 400,
              fontSize: '16px',
              letterSpacing: '0%',
              padding: '12px 14px', // Added right padding for icon space
            },
            '& .MuiFormHelperText-root': {
              fontFamily: 'Plus Jakarta Sans, sans-serif',
            },
            '& .MuiOutlinedInput-root': {
              borderRadius: '8px',
              '& fieldset': {
                border: '1.5px solid #9CA3AF !important',
              },
              '&:hover fieldset': {
                border: '1.5px solid #9CA3AF !important',
              },
              '&.Mui-focused fieldset': {
                border: '1.5px solid #9CA3AF !important',
              },
              '& input': {
                '&::placeholder': {
                  fontFamily: 'Plus Jakarta Sans',
                  fontWeight: 400,
                  fontSize: '16px',
                  lineHeight: 'components/input/value/line-height',
                  letterSpacing: '0%',
                  color: '#A3A3A3',
                  opacity: 1,
                },
              },
            },
          }}
        />
        <IconButton
          onClick={handleToggleVisibility}
          edge="end"
          sx={{
            position: 'absolute',
            right: '-25px',
            top: '50%',
            transform: 'translateY(-50%)',
            cursor: 'pointer',
            zIndex: 1,
          }}
        >
          <Iconify
            icon={showPassword ? 'solar:eye-bold' : 'solar:eye-closed-bold'}
            sx={{
              color: 'secondary.main',
              fontSize: '20px',
            }}
          />
        </IconButton>
      </Box>
    );
  }
);

export default PasswordField;