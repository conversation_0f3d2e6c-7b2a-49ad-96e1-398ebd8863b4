'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth, useVerifySession } from '@minicardiac-client/apis';
import { getDecodedToken } from '@minicardiac-client/utilities';
import { FullPageLoader } from '@minicardiac-client/components';
interface RouteGuardProps {
  children: React.ReactNode;
  disableRedirect?: boolean;
}

export const ProtectedRoute: React.FC<RouteGuardProps> = ({ children }) => {
  const { authState } = useAuth();
  const sessionQuery = useVerifySession();
  const router = useRouter();
  const pathname = usePathname();
  const [decoded, setDecoded] = useState<any>(null);

  useEffect(() => {
    const fetchToken = async () => {
      const decodedToken = await getDecodedToken();
      console.log('Decoded Token:', decodedToken); // Debug log
      setDecoded(decodedToken);
    };

    if (authState.isAuthenticated && !decoded) {
      fetchToken();
    }
  }, [authState, decoded]);

  // Handle email verification redirect
  useEffect(() => {
    if (!authState.isAuthenticated || !decoded || !decoded.email_verified) {
      return;
    }

    console.log('Auth State:', {
      isAuthenticated: authState.isAuthenticated,
      user: authState.user,
      currentPath: pathname,
      decodedToken: decoded,
    });

    // Skip redirects for these paths
    if (pathname === '/dev' || pathname?.startsWith('/professional')) {
      return;
    }

    // Only handle email verification, skip profile completion for all users
    if (decoded && !decoded.email_verified) {
      router.push(
        `/verify-otp?email=${encodeURIComponent(
          decoded.email
        )}&name=${encodeURIComponent(decoded.name || '')}&accountType=${
          decoded.accountType || 'PUBLIC'
        }`
      );
    }
    // Profile completion redirect is now disabled for both roles
  }, [authState, decoded, pathname, router]);

  // Special case for the /dev route and professional section - bypass authentication
  if (
    pathname === '/dev' ||
    (pathname && pathname.startsWith('/professional'))
  ) {
    return <>{children}</>;
  }

  if (authState.isLoading || sessionQuery.isLoading) {
    return <FullPageLoader open={true} />;
  }

  if (authState.isAuthenticated && !sessionQuery.isError) {
    return <>{children}</>;
  }

  return null;
};
export const RedirectIfAuthenticated: React.FC<RouteGuardProps> = ({
  children,
  disableRedirect = false,
}) => {
  const { authState } = useAuth();
  const sessionQuery = useVerifySession();
  const router = useRouter();
  const [hasRedirected, setHasRedirected] = useState(false);

  useEffect(() => {
    const handleRedirect = async () => {
      // Prevent multiple redirects
      if (hasRedirected) return;

      // Wait for session verification to complete successfully
      if (sessionQuery.isLoading || sessionQuery.isError) {
        return;
      }

      const decodedToken = await getDecodedToken();

      if (decodedToken && !disableRedirect) {
        setHasRedirected(true);
        router.push('/feed');
      }
    };

    if (
      !authState.isLoading &&
      authState.isAuthenticated &&
      !sessionQuery.isLoading &&
      !sessionQuery.isError &&
      !hasRedirected
    ) {
      handleRedirect();
    }
  }, [
    authState,
    sessionQuery.isLoading,
    sessionQuery.isError,
    router,
    disableRedirect,
    hasRedirected,
  ]);

  // Only show loader if we're not already showing one from the parent component
  if (authState.isLoading || sessionQuery.isLoading) {
    // Don't show loader if disableRedirect is true (parent is handling loading state)
    return disableRedirect ? <>{children}</> : <FullPageLoader open={true} />;
  }

  if (!authState.isAuthenticated || sessionQuery.isError) {
    return <>{children}</>;
  }

  return null;
};

export const PatientProfileRedirect: React.FC<RouteGuardProps> = ({
  children,
}) => {
  const { authState } = useAuth();
  const sessionQuery = useVerifySession();
  const router = useRouter();

  useEffect(() => {
    const handleRedirect = async () => {
      const decodedToken = await getDecodedToken();

      if (decodedToken?.currentStage === undefined) {
        router.push(
          `/verify-otp?email=${encodeURIComponent(
            decodedToken?.email || ''
          )}&name=${encodeURIComponent(decodedToken?.name || '')}`
        );
      } else if (decodedToken?.currentStage === 'completed') {
        router.push('/feed');
      }
    };

    if (
      !authState.isLoading &&
      authState.isAuthenticated &&
      !sessionQuery.isLoading
    ) {
      handleRedirect();
    }

    if (!authState.isAuthenticated && !authState.isLoading) {
      router.push('/signup/patient');
    }
  }, [authState, sessionQuery, router]);

  if (authState.isLoading || sessionQuery.isLoading) {
    return <FullPageLoader open={true} />;
  }

  return <>{children}</>;
};

export const VerifyOTPRedirect: React.FC<RouteGuardProps> = ({ children }) => {
  const { authState } = useAuth();
  const sessionQuery = useVerifySession();
  const router = useRouter();
  const pathname = usePathname();
  const isVerifyOtpPage = pathname?.startsWith('/verify-otp');

  useEffect(() => {
    const handleRedirect = async () => {
      const decodedToken = await getDecodedToken();

      if (decodedToken?.currentStage === undefined) {
        router.push(
          `/verify-otp?email=${encodeURIComponent(
            decodedToken?.email || ''
          )}&name=${encodeURIComponent(decodedToken?.name || '')}`
        );
      } else if (decodedToken?.currentStage === 'completed') {
        // router.push('/feed');
      }
    };

    if (
      !authState.isLoading &&
      authState.isAuthenticated &&
      !sessionQuery.isLoading
    ) {
      handleRedirect();
    }
  }, [authState, sessionQuery, router]);

  // Don't show loader on verify-otp page as it handles its own loading state
  if ((authState.isLoading || sessionQuery.isLoading) && !isVerifyOtpPage) {
    return <FullPageLoader open={true} />;
  }

  return <>{children}</>;
};
