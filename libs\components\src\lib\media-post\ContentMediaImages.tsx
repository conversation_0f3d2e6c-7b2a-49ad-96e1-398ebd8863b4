import { useTheme } from '@emotion/react';
import { Box, useMediaQuery } from '@mui/material';
import { useState, TouchEvent } from 'react';

interface ContentMediaImagesProps {
  media: string[];
  setOpenDialog: (value: boolean) => void;
}

export const ContentMediaImages = ({
  media,
  setOpenDialog,
}: ContentMediaImagesProps) => {
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const [currentIndex, setCurrentIndex] = useState(0);

  let touchStartX = 0;
  let touchEndX = 0;

  const handleTouchStart = (e: TouchEvent) => {
    touchStartX = e.touches[0].clientX;
  };

  const handleTouchEnd = (e: TouchEvent) => {
    touchEndX = e.changedTouches[0].clientX;
    handleSwipe();
  };

  const handleSwipe = () => {
    const swipeThreshold = 50; // px
    if (touchStartX - touchEndX > swipeThreshold) {
      // Swipe Left → Next
      setCurrentIndex((prev) => (prev + 1) % media.length);
    } else if (touchEndX - touchStartX > swipeThreshold) {
      // Swipe Right → Prev
      setCurrentIndex((prev) => (prev - 1 + media.length) % media.length);
    }
  };

  if (isSmallScreen) {
    return (
      <Box mt="20px" position="relative">
        {/* Image */}
        <Box
          component="img"
          src={media[currentIndex]}
          alt={`media-${currentIndex}`}
          width="100%"
          sx={{
            borderRadius: '8px',
            objectFit: 'cover',
            height: '312px',
            cursor: 'pointer',
          }}
          onClick={() => setOpenDialog(true)}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
        />

        {/* Top-right 1/4 indicator */}
        <Box
          position="absolute"
          top="8px"
          right="12px"
          px="8px"
          py="2px"
          borderRadius="12px"
          bgcolor="rgba(0,0,0,0.5)"
          color="#fff"
          fontSize="12px"
        >
          {currentIndex + 1}/{media.length}
        </Box>

        {/* Dots */}
        <Box display="flex" justifyContent="center" mt="8px" gap="6px">
          {media.map((_, index) => (
            <Box
              key={index}
              width="6px"
              height="6px"
              borderRadius="50%"
              bgcolor={index === currentIndex ? '#A24295' : '#A3A3A3'}
            />
          ))}
        </Box>
      </Box>
    );
  }

  // Large screens → original flex layout
  return (
    <Box
      mt="20px"
      sx={{
        display: 'flex',
        overflowX: media.length > 2 ? 'auto' : 'hidden',
        justifyContent: media.length <= 2 ? 'center' : 'flex-start',
        gap: '20px',
        scrollbarWidth: 'none',
        '&::-webkit-scrollbar': { display: 'none' },
      }}
    >
      {media.map((url, index) => (
        <Box
          key={index}
          component="img"
          src={url}
          alt={`media-${index}`}
          width="194px"
          height={{ xs: '312px', sm: '184px' }}
          sx={{
            objectFit: 'cover',
            borderRadius: '8px',
            flexShrink: 0,
            cursor: 'pointer',
          }}
          onClick={() => setOpenDialog(true)}
        />
      ))}
    </Box>
  );
};
