import { Container, Divider } from '@mui/material';
import { useState } from 'react';
import SubscriptionList from '../components/SubscriptionList';
import SubscriptionTable from '../components/SubscriptionTable';
import ProfileSetup from '../components/CardiacSpecialist/ProfileSetup';
import ConnectWithOthers from '../components/profiles/ConnectWithOthers';
import DocumentUploadForm from '../components/Documents/DocumentUpload';

interface SubscriptionComponentPageProps {
  userSegment?: string;
  billingCycle?: 'monthly' | 'yearly';
}

/**
 * SubscriptionTypePage component for selecting user type during signup
 */
const SubscriptionComponentPage = ({
  userSegment = 'CARDIAC_SPECIALIST',
  billingCycle = 'monthly',
}: SubscriptionComponentPageProps) => {
  const [showTable, setShowTable] = useState(false);

  const handleSwitchView = () => {
    setShowTable(!showTable);
  };

  return (
    <Container
      sx={{
        maxWidth: '1192px !important',
        mx: 'auto',
        pl: '0 !important',
        pr: '0 !important',
        py: 6,
        display: 'flex',
        flexDirection: 'column',
        gap: 3,
      }}
    >
      {!showTable ? (
        <SubscriptionList
          userSegment={userSegment}
          billingCycle={billingCycle}
          onClickSwitchToTableButton={handleSwitchView}
        />
      ) : (
        <SubscriptionTable
          userSegment={userSegment}
          billingCycle={billingCycle}
          // onSwitchToCardView={handleSwitchView}
        />
      )}
      <Divider />
      <br />
      <br />
      <ProfileSetup />
      <br />
      <Divider />
      <br />
      <DocumentUploadForm />
      <br />
      <Divider />
      <br />
      <ConnectWithOthers />
    </Container>
  );
};

export default SubscriptionComponentPage;
