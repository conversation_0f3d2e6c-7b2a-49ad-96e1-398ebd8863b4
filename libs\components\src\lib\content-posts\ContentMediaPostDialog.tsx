import { useState } from 'react';
import {
  <PERSON>,
  Typography,
  IconButton,
  TextField,
  Divider,
  Avatar,
  Button,
  useMediaQuery,
} from '@mui/material';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import CloseIcon from '@mui/icons-material/Close';

import CustomDialog from '../dialogs/CustomDialog';
import PostHeader from './PostHeader';
import { Iconify } from '../iconify';
import LikeIcon from '../Icons/ContentPostIcons/LikeIcon';
import ContentMediaPostMobile from './ContentMediaPostMobile';
import { useTheme } from '@emotion/react';

interface Comment {
  id: number;
  user: {
    name: string;
    profilePic: string;
  };
  content: string;
  postedAgo: string;
}

interface ContentMediaPostDialogProps {
  open: boolean;
  onClose: () => void;
  media: string[];
  user: {
    name: string;
    profilePic: string;
    postedAgo: string;
  };
  content: string;
  likes: number;
  comments: number;
  reposts: number;
  shares: number;
}

const mockComments: Comment[] = [
  {
    id: 1,
    user: {
      name: 'Dr. Arya Patel',
      profilePic: '/placeholder-avatar.png',
    },
    content:
      'Brilliant work on this repair! Love your attention to detail with the chordal reconstruction.',
    postedAgo: '2h ago',
  },
  {
    id: 2,
    user: {
      name: 'Dr. Meera Shah',
      profilePic: '/placeholder-avatar.png',
    },
    content: 'Such teamwork is inspiring! Thanks for sharing this.',
    postedAgo: '1h ago',
  },
];

const ContentMediaPostDialog = ({
  open,
  onClose,
  media,
  user,
  content,
  likes,
  comments,
  reposts,
  shares,
}: ContentMediaPostDialogProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const handlePrev = () =>
    setCurrentIndex((prev) => (prev === 0 ? media.length - 1 : prev - 1));

  const handleNext = () =>
    setCurrentIndex((prev) => (prev === media.length - 1 ? 0 : prev + 1));

  if (isSmallScreen) {
    return (
      <ContentMediaPostMobile
        open={open}
        onClose={onClose}
        media={media}
        user={user}
        content={content}
        likes={likes}
        commentList={mockComments}
      />
    );
  }

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiPaper-root': {
          width: '100%',
          maxWidth: 'none',
          margin: 0,
          backgroundColor: '#fff',
          boxSizing: 'border-box',
        },
        paddingX: { xs: '0px', sm: '20px', md: '60px' },
        paddingTop: { xs: '0px', sm: '45px' },
        minHeight: '740px',
      }}
    >
      <Box
        width="100%"
        display="flex"
        flexDirection={{ xs: 'column', sm: 'row' }}
        bgcolor="#fff"
        height={{ xs: '100vh', sm: '740px' }}
        p={{ xs: '0px', sm: '40px' }}
        gap="20px"
        borderRadius="8px"
        boxSizing="border-box"
      >
        {/* Left - Media */}
        <Box
          position="relative"
          width={{ xs: '100%', sm: '58%' }}
          height="100%"
          bgcolor="#000"
          display="flex"
          justifyContent="center"
          alignItems="center"
          sx={{
            borderRadius: '8px',
            overflow: 'hidden',
          }}
        >
          <Box
            component="img"
            src={media[currentIndex]}
            alt={`media-${currentIndex}`}
            sx={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              borderRadius: '8px',
            }}
          />

          {/* Navigation Buttons */}
          {!isSmallScreen && media.length > 1 && (
            <>
              <IconButton
                onClick={handlePrev}
                sx={{
                  position: 'absolute',
                  left: '8px',
                  color: '#fff',
                  backgroundColor: 'transparent',
                  borderRadius: 0,
                  '&:hover': {
                    backgroundColor: 'transparent',
                  },
                  width: '36px',
                  height: '36px',
                  p: 0,
                }}
              >
                <ArrowBackIosNewIcon sx={{ fontSize: 36 }} />
              </IconButton>
              <IconButton
                onClick={handleNext}
                sx={{
                  position: 'absolute',
                  right: '8px',
                  color: '#fff',
                  backgroundColor: 'transparent',
                  borderRadius: 0,
                  '&:hover': {
                    backgroundColor: 'transparent',
                  },
                  width: '36px',
                  height: '36px',
                  p: 0,
                }}
              >
                <ArrowForwardIosIcon sx={{ fontSize: 36 }} />
              </IconButton>
            </>
          )}
        </Box>

        {/* Right - Content */}
        <Box
          width={{ xs: '100%', sm: '42%' }}
          height="100%"
          display="flex"
          flexDirection="column"
          justifyContent="space-between"
          boxSizing="border-box"
          sx={{
            borderTopRightRadius: '8px',
            borderBottomRightRadius: '8px',
            backgroundColor: '#fff',
            position: 'relative',
          }}
        >
          <Box
            position="absolute"
            top="0px"
            right="0px"
            display="flex"
            alignItems="center"
            gap="20px"
            zIndex={10}
          >
            <IconButton
              sx={{
                p: 0,
                color: '#A24295',
                width: '36px',
                height: '36px',
              }}
            >
              <MoreHorizIcon sx={{ fontSize: 36 }} />
            </IconButton>

            <IconButton
              onClick={onClose}
              sx={{
                p: 0,
                color: '#A24295',
                width: '36px',
                height: '36px',
              }}
            >
              <CloseIcon sx={{ fontSize: 36 }} />
            </IconButton>
          </Box>

          {/* Post Header */}
          <PostHeader user={user} showOptions={false} />

          {/* Caption */}
          <Box
            mt="20px"
            lineHeight="22px"
            sx={{
              overflowY: 'auto',
              fontSize: '16px',
              color: '#1E1E1E',
              '& p:last-child': { fontWeight: 700, fontSize: '16px' },
            }}
            dangerouslySetInnerHTML={{ __html: content }}
          />

          <Divider sx={{ mt: '12px', mb: '8px' }} />

          {/* Comments Header */}
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            mb={'20px'}
          >
            <Typography fontSize="12px" fontWeight={600}>
              Comments ({mockComments.length})
            </Typography>

            <Box display="flex" gap="28px" alignItems="center">
              <Box
                display="flex"
                alignItems="center"
                gap="4px"
                sx={{ cursor: 'pointer' }}
              >
                <LikeIcon />
                <Typography fontSize="12px" fontWeight={600} color="#A24295">
                  Like ({likes})
                </Typography>
              </Box>

              <Box
                display="flex"
                alignItems="center"
                gap="4px"
                sx={{ cursor: 'pointer' }}
              >
                <Iconify
                  icon={'garden:arrow-retweet-stroke-12'}
                  sx={{ color: '#A24295', fontSize: 20 }}
                />
                <Typography fontSize="12px" fontWeight={600} color="#A24295">
                  Repost ({reposts})
                </Typography>
              </Box>

              <Box
                display="flex"
                alignItems="center"
                gap="4px"
                sx={{ cursor: 'pointer' }}
              >
                <Iconify
                  icon={'mdi:share'}
                  sx={{ color: '#A24295', fontSize: 20 }}
                />
                <Typography fontSize="12px" fontWeight={600} color="#A24295">
                  Share ({shares})
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Comments */}
          <Box
            flex={1}
            overflow="auto"
            display="flex"
            flexDirection="column"
            gap="8px"
            pr="8px"
          >
            {mockComments.map((comment) => (
              <Box key={comment.id} display="flex" gap="12px">
                <Avatar
                  src={comment.user.profilePic}
                  alt={comment.user.name}
                  sx={{ width: 40, height: 40 }}
                />
                <Box flex={1} bgcolor="#F3F4F6" p={'12px'} borderRadius="8px">
                  <Box
                    display="flex"
                    justifyContent="space-between"
                    alignItems="center"
                  >
                    <Typography fontSize="16px" fontWeight={600}>
                      {comment.user.name}
                    </Typography>
                    <Typography
                      fontSize="16px"
                      fontWeight={300}
                      color="text.secondary"
                    >
                      {comment.postedAgo}
                    </Typography>
                  </Box>
                  <Typography mt="8px" fontSize="16px" fontWeight={400}>
                    {comment.content}
                  </Typography>
                  <Box
                    mt="8px"
                    display="flex"
                    gap="16px"
                    width={'138px'}
                    height={'30px'}
                    alignItems={'center'}
                    ml="8px"
                  >
                    <Button
                      variant="text"
                      sx={{
                        p: 0,
                        minWidth: 'auto',
                        fontSize: '12px',
                        fontWeight: 600,
                        color: '#A24295',
                        textTransform: 'none',
                      }}
                    >
                      Like
                    </Button>
                    <Box
                      sx={{
                        width: '2px',
                        height: '2px',
                        backgroundColor: '#A3A3A3',
                        borderRadius: '50%',
                      }}
                    ></Box>
                    <Button
                      variant="text"
                      sx={{
                        p: 0,
                        minWidth: 'auto',
                        fontSize: '12px',
                        fontWeight: 600,
                        color: '#A24295',
                        textTransform: 'none',
                      }}
                    >
                      Reply
                    </Button>
                  </Box>
                </Box>
              </Box>
            ))}
          </Box>

          {/* Add Comment */}
          <Box display="flex" gap="12px" alignItems="flex-start" mt={2}>
            <Avatar
              src="/placeholder-avatar.png"
              alt="Your profile"
              sx={{ width: 40, height: 40 }}
            />
            <TextField
              fullWidth
              size="small"
              placeholder="What do you think of this post?"
              InputProps={{
                sx: {
                  backgroundColor: '#F3F4F6',
                  borderRadius: '8px',
                  height: '45px',
                  px: 2,
                  fontSize: '14px',
                },
              }}
            />
          </Box>
        </Box>
      </Box>
    </CustomDialog>
  );
};

export default ContentMediaPostDialog;
