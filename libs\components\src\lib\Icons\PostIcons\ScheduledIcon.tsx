const ScheduledIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
  useGradient = true,
}) => {
  const gradient = 'url(#paint1_linear_1757_106371)';

  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.4376 16.6875C9.46548 16.6875 7.57367 17.4719 6.17894 18.8664C4.78421 20.2609 4 22.1527 4 24.125C4 26.0973 4.78443 27.9889 6.17894 29.3836C7.57345 30.7783 9.46527 31.5625 11.4376 31.5625C13.41 31.5625 15.3016 30.7781 16.6963 29.3836C18.091 27.9891 18.8752 26.0973 18.8752 24.125C18.8735 22.1529 18.0891 20.2628 16.6946 18.8682C15.3001 17.4735 13.4099 16.6892 11.4376 16.6875ZM12.3126 24.1248C12.3109 24.3572 12.2203 24.5794 12.0597 24.7469L10.2841 26.5139H10.2824C10.12 26.6746 9.89956 26.7668 9.67057 26.7668C9.43815 26.7668 9.21597 26.6746 9.0485 26.5139C8.88444 26.3499 8.79215 26.1277 8.79215 25.897C8.79215 25.6646 8.88444 25.4424 9.0485 25.28L10.5626 23.7659V19.9243C10.5626 19.4423 10.954 19.0493 11.4376 19.0493C11.9212 19.0493 12.3126 19.4423 12.3126 19.9243L12.3126 24.1248Z"
        fill={useGradient ? gradient : fill}
      />
      <path
        d="M28.9371 6.47627H27.6418V5.31246C27.6418 4.5879 27.0539 4 26.3293 4C25.6047 4 25.0168 4.5879 25.0168 5.31246V6.47627H15.3578V5.31246C15.3578 4.5879 14.7699 4 14.0453 4C13.3207 4 12.7328 4.5879 12.7328 5.31246V6.47627H11.4375C9.74729 6.47798 8.37671 7.84854 8.375 9.53869V15.9269C8.94068 15.715 9.52683 15.5663 10.125 15.4792V13.695H30.2497V24.1248C30.2497 24.4734 30.1113 24.8066 29.8652 25.0527C29.6191 25.2988 29.2859 25.4372 28.9372 25.4372H20.0832C19.9961 26.0354 19.8474 26.6215 19.6355 27.1872H28.9375C30.6277 27.1855 31.9982 25.8149 32 24.1248V9.53891C31.9982 7.84876 30.6273 6.47798 28.9371 6.47627Z"
        fill={useGradient ? gradient : fill}
      />
      <defs>
        <linearGradient
          id="paint0_linear_1757_106371"
          x1="11.4376"
          y1="16.6875"
          x2="11.4376"
          y2="31.5625"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F92243" />
          <stop offset="1" stopColor="#A24295" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_1757_106371"
          x1="20.1875"
          y1="4"
          x2="20.1875"
          y2="27.1872"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F92243" />
          <stop offset="1" stopColor="#A24295" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default ScheduledIcon;
