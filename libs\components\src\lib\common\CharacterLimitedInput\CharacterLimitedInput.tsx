import React from 'react';
import { TextField, Typography, Box } from '@mui/material';
import {
  INTRODUCTORY_STATEMENT_LIMITS,
  BIO_LIMITS,
  TEXT_POST_LIMITS,
  ARTICLE_LIMITS,
  ARTICLE_SUMMARY_LIMITS
} from '@minicardiac-client/constants';

export interface CharacterLimitedInputProps {
  value: string;
  onChange: (value: string) => void;
  fieldType: 'introductoryStatement' | 'bio' | 'textPost' | 'article' | 'articleSummary';
  label?: string;
  placeholder?: string;
  multiline?: boolean;
  rows?: number;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  showCharacterCount?: boolean;
  sx?: object;
}

/**
 * Reusable component for text inputs with character limits
 * Uses constants from @minicardiac-client/constants for consistent limits
 */
export const CharacterLimitedInput: React.FC<CharacterLimitedInputProps> = ({
  value,
  onChange,
  fieldType,
  label,
  placeholder,
  multiline = false,
  rows = 1,
  disabled = false,
  error = false,
  helperText,
  showCharacterCount = true,
  sx = {},
}) => {
  // Get character limit based on field type
  const getCharacterLimit = (type: string): number => {
    switch (type) {
      case 'introductoryStatement':
        return INTRODUCTORY_STATEMENT_LIMITS.MAX;
      case 'bio':
        return BIO_LIMITS.MAX;
      case 'textPost':
        return TEXT_POST_LIMITS.MAX;
      case 'article':
        return ARTICLE_LIMITS.MAX;
      case 'articleSummary':
        return ARTICLE_SUMMARY_LIMITS.MAX;
      default:
        return 1000;
    }
  };

  const max = getCharacterLimit(fieldType);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= max) {
      onChange(newValue);
    }
  };

  const getCharacterCountColor = () => {
    return 'text.secondary';
  };

  const getCharacterCountText = () => {
    return `${value.length}/${max}`;
  };

  return (
    <Box sx={{ position: 'relative', ...sx }}>
      <TextField
        value={value}
        onChange={handleChange}
        label={label}
        placeholder={placeholder}
        multiline={multiline}
        rows={multiline ? rows : undefined}
        disabled={disabled}
        error={error}
        helperText={helperText}
        fullWidth
        inputProps={{
          maxLength: max
        }}
        InputLabelProps={{ shrink: true }}
      />
      
      {showCharacterCount && (
        <Typography
          variant="caption"
          sx={{
            position: 'absolute',
            bottom: multiline ? 8 : -20,
            right: 8,
            color: getCharacterCountColor(),
            backgroundColor: multiline ? 'background.paper' : 'transparent',
            px: multiline ? 1 : 0,
          }}
        >
          {getCharacterCountText()}
        </Typography>
      )}
      

    </Box>
  );
};

export default CharacterLimitedInput;
