import { useState, useRef } from 'react';
import { Box, Dialog, IconButton, Typography } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import {
  SortableContext,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import { DndContext } from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import { SortableItem } from './SortableItem';
import AddImageIcon from '../Icons/FeedIcons/AddImageIcon';

interface SelectedMediaReviewDialogProps {
  open: boolean;
  onClose: () => void;
  files: File[];
  setFiles: (files: File[]) => void;
  onAddMedia: () => void;
}

const SelectedMediaReviewDialog = ({
  open,
  onClose,
  files,
  setFiles,
  onAddMedia,
}: SelectedMediaReviewDialogProps) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const scrollRef = useRef<HTMLDivElement | null>(null);

  const handleRemove = (index: number) => {
    const updated = [...files];
    updated.splice(index, 1);
    setFiles(updated);
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    if (active.id !== over?.id) {
      const oldIndex = files.findIndex((f) => f.name === active.id);
      const newIndex = files.findIndex((f) => f.name === over?.id);
      setFiles(arrayMove(files, oldIndex, newIndex));
    }
  };

  const handleScroll = () => {
    if (scrollRef.current) {
      const scrollLeft = scrollRef.current.scrollLeft;
      const itemWidth = 305 + 16; // width + gap
      const index = Math.round(scrollLeft / itemWidth);
      setActiveIndex(index);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} fullScreen>
      {/* Header */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        p={2}
        borderBottom="1px solid #ddd"
      >
        <Typography variant="h6" fontWeight={600}>
          Review Media
        </Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </Box>

      {/* Scrollable Media Row */}
      <Box
        ref={scrollRef}
        onScroll={handleScroll}
        display="flex"
        overflow="auto"
        gap={2}
        p={2}
        sx={{ scrollSnapType: 'x mandatory' }}
      >
        <DndContext onDragEnd={handleDragEnd}>
          <SortableContext
            items={files.map((f) => f.name)}
            strategy={horizontalListSortingStrategy}
          >
            {files.map((file, index) => (
              <Box
                key={file.name}
                minWidth="305px"
                height="407px"
                borderRadius="12px"
                sx={{
                  scrollSnapAlign: 'start',
                  position: 'relative',
                  flexShrink: 0,
                }}
              >
                <SortableItem
                  id={file.name}
                  file={file}
                  index={index}
                  onRemove={() => handleRemove(index)}
                  open={() => console.log('hi')}
                />
              </Box>
            ))}
          </SortableContext>
        </DndContext>
      </Box>

      {/* Pagination */}
      <Box display="flex" justifyContent="center" mb={2}>
        <Typography fontSize="16px" fontWeight={500} color="#000">
          {files.length ? activeIndex + 1 : 0}/{files.length}
        </Typography>
      </Box>

      {/* Add More Media */}
      <Box
        position="absolute"
        bottom={0}
        left={0}
        width="100%"
        sx={{
          padding: '11px',
          borderTop: '1px solid #A3A3A325',
          alignItems: 'center',
          display: 'flex',
          justifyContent: 'center',
          cursor: 'pointer',
          gap: '4px',
          background: '#fff',
        }}
        onClick={onAddMedia}
      >
        <AddImageIcon />
        <Typography
          variant="body2"
          color="secondary.main"
          fontSize={'16px'}
          fontWeight={600}
        >
          Add more media
        </Typography>
      </Box>
    </Dialog>
  );
};

export default SelectedMediaReviewDialog;
