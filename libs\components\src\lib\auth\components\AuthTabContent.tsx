'use client';

import { Box } from '@mui/material';
import { useState, useEffect, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import SignInForm from './SignInForm';
import SignupTypeList from './SignupTypeList';
import SignUpForm from './SignUpForm';

interface AuthTabContentProps {
  onSubmit?: (data: {
    email: string;
    password: string;
    displayName?: string;
    organizationName?: string;
  }) => void;
  onForgotPassword?: () => void;
  onTypeSelect?: (path: string) => void;
  activeTab: number;
  isLoading?: boolean;
  error?: string | null;
  displayNameLabel?: string;
  namePlaceholder?: string;
  emailLabel?: string;
  passwordLabel?: string;
  forgotPasswordLabel?: string;
  continueLabel?: string;
  orLabel?: string;
  googleLabel?: string;
  appleLabel?: string;
  locale?: string;
}

const AuthTabContent = (props: AuthTabContentProps) => {
  const {
    onSubmit,
    onForgotPassword,
    onTypeSelect,
    activeTab = 0,
    isLoading,
    error,
    displayNameLabel = 'Display Name',
    namePlaceholder = 'Name',
    emailLabel = 'Email',
    passwordLabel = 'Password',
    forgotPasswordLabel = 'Forgot Password?',
    continueLabel = 'Continue',
    orLabel = 'OR',
    googleLabel = 'Continue with Google',
    appleLabel = 'Continue with Apple',
  } = props;

  console.log('Reached auth tab content');
  const pathname = usePathname();
  const [userType, setUserType] = useState<string>('');
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({
    'signin-password': false,
    'signup-password': false,
    'confirm-password': false,
  });

  const togglePasswordVisibility = useCallback(
    (fieldId: 'signin-password' | 'signup-password' | 'confirm-password') => {
      setShowPasswords((prev) => ({
        ...prev,
        [fieldId]: !prev[fieldId],
      }));
    },
    []
  );

  useEffect(() => {
    if (!pathname) return;
    const pathParts = pathname.split('/').filter(Boolean);
    const signupIndex = pathParts.indexOf('signup');
    if (signupIndex !== -1 && pathParts.length > signupIndex + 1) {
      const typeFromUrl = pathParts[signupIndex + 1];
      setUserType(typeFromUrl);
    } else {
      setUserType('');
    }
  }, [pathname, activeTab]);

  const handleTypeSelect = (path: string) => {
    if (onTypeSelect) onTypeSelect(path);
  };

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
      }}
    >
      <Box
        sx={{
          width: {
            md: '600px',
            sm: '400px',
            xs: '400px',
          },
          position: 'relative',
          overflow: 'hidden',
          minHeight: '100vh',
        }}
      >
        {/* SignIn Form */}
        <Box
          sx={{
            position: 'absolute',
            width: '100%',
            top: 0,
            left: activeTab === 0 ? '0%' : '-100%',
            transition: 'left 0.3s ease-in-out',
            backgroundColor: '#ffffff',
            borderRadius: 2,
            paddingY: 5,
            boxSizing: 'border-box',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            willChange: 'left',
          }}
        >
          <SignInForm
            onSubmit={onSubmit}
            onForgotPassword={onForgotPassword}
            isLoading={isLoading}
            error={error}
            emailLabel={emailLabel}
            passwordLabel={passwordLabel}
            forgotPasswordLabel={forgotPasswordLabel}
            continueLabel={continueLabel}
            orLabel={orLabel}
            googleLabel={googleLabel}
            appleLabel={appleLabel}
            showPassword={showPasswords['signin-password']}
            onTogglePasswordVisibility={() =>
              togglePasswordVisibility('signin-password')
            }
          />
        </Box>

        {/* Signup Type Selection */}
        <Box
          sx={{
            position: 'absolute',
            width: '100%',
            top: 0,
            left: activeTab === 1 && !userType ? '0%' : '100%',
            transition: 'left 0.3s ease-in-out',
            backgroundColor: '#ffffff',
            borderRadius: 2,
            boxSizing: 'border-box',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            pt: '20px',
            willChange: 'left',
            visibility: activeTab === 1 && !userType ? 'visible' : 'hidden',
          }}
        >
          <SignupTypeList onTypeSelect={handleTypeSelect} />
        </Box>

        {/* SignUp Form */}
        <Box
          sx={{
            position: 'absolute',
            width: '100%',
            top: 0,
            left: userType && activeTab === 1 ? '0%' : '100%',
            transition: 'left 0.3s ease-in-out',
            backgroundColor: '#ffffff',
            borderRadius: 2,
            padding: { xs: '32px 16px', sm: '48px 32px', md: '64px' },
            boxSizing: 'border-box',
            display: 'flex',
            willChange: 'left',
            visibility: userType && activeTab === 1 ? 'visible' : 'hidden',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {userType === 'professional' && (
            <SignUpForm
              userType="professional"
              onSubmit={onSubmit}
              isLoading={isLoading}
              error={error}
              showPassword={showPasswords['signup-password']}
              showConfirmPassword={showPasswords['confirm-password']}
              onTogglePasswordVisibility={(field) =>
                togglePasswordVisibility(
                  field === 'password' ? 'signup-password' : 'confirm-password'
                )
              }
              displayNameLabel={displayNameLabel}
              namePlaceholder={namePlaceholder}
              emailLabel={emailLabel}
              passwordLabel={passwordLabel}
              continueLabel={continueLabel}
              orLabel={orLabel}
              googleLabel={googleLabel}
              appleLabel={appleLabel}
            />
          )}
          {userType === 'organization' && (
            <SignUpForm
              userType="organization"
              onSubmit={onSubmit}
              isLoading={isLoading}
              error={error}
              showPassword={showPasswords['signup-password']}
              showConfirmPassword={showPasswords['confirm-password']}
              onTogglePasswordVisibility={(field) =>
                togglePasswordVisibility(
                  field === 'password' ? 'signup-password' : 'confirm-password'
                )
              }
              displayNameLabel={displayNameLabel}
              namePlaceholder={namePlaceholder}
              emailLabel={emailLabel}
              passwordLabel={passwordLabel}
              continueLabel={continueLabel}
              orLabel={orLabel}
              googleLabel={googleLabel}
              appleLabel={appleLabel}
            />
          )}
          {userType === 'patient' && (
            <SignUpForm
              userType="patient"
              onSubmit={onSubmit}
              isLoading={isLoading}
              error={error}
              showPassword={showPasswords['signup-password']}
              showConfirmPassword={showPasswords['confirm-password']}
              onTogglePasswordVisibility={(field) =>
                togglePasswordVisibility(
                  field === 'password' ? 'signup-password' : 'confirm-password'
                )
              }
              displayNameLabel={displayNameLabel}
              namePlaceholder={namePlaceholder}
              emailLabel={emailLabel}
              passwordLabel={passwordLabel}
              continueLabel={continueLabel}
              orLabel={orLabel}
              googleLabel={googleLabel}
              appleLabel={appleLabel}
            />
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default AuthTabContent;
