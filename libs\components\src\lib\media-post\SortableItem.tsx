import { Box, IconButton } from '@mui/material';
import MediaFileCard from './MediaFileCard';
import { useSortable } from '@dnd-kit/sortable';

import { CSS } from '@dnd-kit/utilities';
import { Iconify } from '../iconify';

interface SortableItemProps {
  file: File;
  id: string;
  index: number;
  onRemove: (index: number) => void;
  open: () => void;
}

export const SortableItem = ({
  id,
  file,
  index,
  onRemove,
  open,
}: SortableItemProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : undefined,
  };

  return (
    <Box
      ref={setNodeRef}
      style={style}
      {...attributes}
      sx={{ display: 'inline-block', position: 'relative', m: 1 }}
    >
      <MediaFileCard
        file={file}
        index={index}
        onRemove={onRemove}
        open={open}
        dragHandle={
          <IconButton
            {...listeners}
            sx={{
              position: 'absolute',
              top: 6,
              right: 6,
              zIndex: 10,
              backgroundColor: '#fff',
              '&:hover': { backgroundColor: '#f0f0f0' },
            }}
          >
            <Iconify icon={'material-symbols:drag-pan-rounded'} />
          </IconButton>
        }
      />
    </Box>
  );
};
