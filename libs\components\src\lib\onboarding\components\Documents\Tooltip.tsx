import {
  IconButton,
  Stack,
  Tooltip,
  TooltipProps,
  Typography,
  styled,
  tooltipClasses,
} from '@mui/material';
import InfoOutlineIcon from '@mui/icons-material/InfoOutline';
// import { ExtendedTheme } from 'src/lib/auth';
const CustomWidthTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ ...props.classes, popper: className }} />
))<TooltipProps>({
  [`&.MuiPopper-root`]: {
    backgroundColor: 'transparent', // This affects the outer wrapper
  },
  [`& .${tooltipClasses.tooltip}`]: {
    maxWidth: '200px',
  },
});

export const FileTypeTooltip = () => (
  <CustomWidthTooltip
    title={
      <Stack gap={1}>
        <Typography
          variant={'caption'}
          sx={(theme) => ({
            color: theme.palette.grey[600],
            fontWeight: 400,
          })}
        >
          Accepted file types:
        </Typography>
        <Typography variant="body2" sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
          PDF, TXT, ODT, DOC upto 4MB in size.
        </Typography>
      </Stack>
    }
    slotProps={{
      tooltip: {
        sx: {
          backgroundColor: '#ffffff',
          boxShadow: '0px 12px 24px 0px rgba(0, 0, 0, 0.12)',
          padding: '12px 16px',
          borderRadius: '4px',
          maxWidth: '300px',
          fontSize: '14px',
          lineHeight: '1.5',
        },
      },
      arrow: {
        sx: {
          color: '#ffffff',
        },
      },
    }}
    sx={{
      backgroundColor: 'transparent',
      '&:hover': {
        backgroundColor: 'transparent',
      },
    }}
    placement="right-start"
    arrow
  >
    <IconButton size="small" sx={{ ml: 0.5 }}>
      <InfoOutlineIcon
        color="secondary"
        sx={{
          height: 24,
          width: 24,
        }}
      />
    </IconButton>
  </CustomWidthTooltip>
);

export default FileTypeTooltip;
