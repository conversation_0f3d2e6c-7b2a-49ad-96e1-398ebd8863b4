import React, { useState } from 'react';
import { Box, IconButton, Typography } from '@mui/material';
import DeleteIcon from '../Icons/DeleteIcon';
import AddTextIcon from '../Icons/FeedIcons/AddTextIcon';
import MediaPreview from './MediaPreview';
import { MediaFileCardBackSide } from './MediaFileCardBackSide';
import DeletedMediaPlaceholder from './DeletedMediaPlaceholder';

interface MediaFileCardProps {
  file: File;
  index: number;
  open?: () => void;
  onRemove: (index: number) => void;
  dragHandle?: React.ReactNode;
}

const MediaFileCard: React.FC<MediaFileCardProps> = ({
  file,
  index,
  open,
  onRemove,
  dragHandle,
}) => {
  const [isFlipped, setIsFlipped] = useState(false);
  const [altText, setAltText] = useState('');
  const [isDeleted, setIsDeleted] = useState(false);

  const handleSave = () => {
    console.log('Saved Alt Text:', altText);
    setIsFlipped(false);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDeleted(true);
  };

  const handleUndo = () => {
    setIsDeleted(false);
  };

  if (isDeleted) {
    return (
      <DeletedMediaPlaceholder
        previewUrl={file}
        onRemove={() => onRemove(index)}
        onUndo={handleUndo}
      />
    );
  }
  return (
    <Box
      sx={{
        width: { xs: '305px', sm: '237px' },
        height: { xs: '407px', sm: '237px' },
        perspective: '1000px',
        position: 'relative',
      }}
    >
      <Box
        sx={{
          width: '100%',
          height: '100%',
          position: 'relative',
          transformStyle: 'preserve-3d',
          transition: 'transform 0.6s',
          transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)',
        }}
      >
        {/* Front Side */}
        <Box
          sx={{
            width: '100%',
            height: '100%',
            position: 'absolute',
            backfaceVisibility: 'hidden',
            borderRadius: '8px',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Box
            sx={{
              flex: 1,
              position: 'relative',
              width: '100%',
              height: '100%',
              '&:hover .overlay': { opacity: 1 },
            }}
          >
            <MediaPreview file={file} />
            <Box
              className="overlay"
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(30, 30, 30, 0.6)',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                padding: 1,
                opacity: 0,
                transition: 'opacity 0.3s ease-in-out',
              }}
            >
              <IconButton size="small" onClick={handleDelete}>
                <DeleteIcon />
              </IconButton>
            </Box>
          </Box>

          <Box
            position="absolute"
            bottom={0}
            left={0}
            width="100%"
            onClick={() => setIsFlipped(true)}
            sx={{
              height: '38px',
              padding: '11px',
              border: '1px solid #A24295',
              borderTop: 'none',
              borderEndStartRadius: '8px',
              borderEndEndRadius: '8px',
              alignItems: 'center',
              display: 'flex',
              justifyContent: 'center',
              cursor: 'pointer',
              backgroundColor: 'white',
              zIndex: 2,
              gap: '4px',
            }}
          >
            <AddTextIcon />
            <Typography fontSize={12} fontWeight={600} color="secondary.main">
              Add alt text
            </Typography>
          </Box>

          {dragHandle && (
            <Box sx={{ position: 'absolute', top: 6, right: 6, zIndex: 10 }}>
              {dragHandle}
            </Box>
          )}
        </Box>

        {/* Back Side */}
        <MediaFileCardBackSide
          file={file}
          altText={altText}
          handleSave={handleSave}
          setAltText={setAltText}
          setIsFlipped={setIsFlipped}
        />
      </Box>
    </Box>
  );
};

export default MediaFileCard;
