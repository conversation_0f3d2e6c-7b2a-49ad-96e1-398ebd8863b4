'use client';

import { SignupPage } from '@minicardiac-client/components';
import { useAuth } from '@minicardiac-client/apis';
import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from '@/apps/public/src/i18n/navigation';

export default function SignUpPublicPage() {
  const router = useRouter();
  const [isMounted, setIsMounted] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { registerUser } = useAuth();
  const t = useTranslations('signup');

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleBack = () => {
    setTimeout(() => {
      router.back();
    }, 300);
  };

  const handleSignUp = async (data: {
    email: string;
    password: string;
    displayName?: string;
    organizationName?: string;
  }) => {
    setIsRegistering(true);
    setError(null);
    try {
      const response = await registerUser({
        email: data.email,
        password: data.password,
        displayName: data.displayName || '',
        accountType: 'PUBLIC',
      });

      if (response.requiresOTP) {
        router.push(
          `/verify-otp?email=${encodeURIComponent(
            data.email
          )}&name=${encodeURIComponent(data.displayName || '')}&accountType=PUBLIC`
        );
      } else {
        router.push(
          `/patient/profile?name=${encodeURIComponent(data.displayName || '')}`
        );
      }
    } catch (err) {
      const errorMessage = t('genericErrorMessage');
      setError(errorMessage);
    } finally {
      setIsRegistering(false);
    }
  };

  if (!isMounted) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        {t('loading')}
      </div>
    );
  }

  return (
    <SignupPage
      userType="patient"
      onBack={handleBack}
      onSignUp={handleSignUp}
      isLoading={isRegistering}
      error={error}
      translations={{
        title: t('title'),
        subtitle: t('subtitle'),
        displayNameLabel: t('displayName'),
        namePlaceholder: t('name'),
        emailLabel: t('emailLabel'),
        passwordLabel: t('passwordLabel'),
        forgotPassword: t('forgotPassword'),
        continueLabel: t('continue'),
        orLabel: t('or'),
        googleLabel: t('google'),
        appleLabel: t('apple'),
        signIn: t('signIn'),
        signUp: t('signUp'),
      }}
    />
  );
}
