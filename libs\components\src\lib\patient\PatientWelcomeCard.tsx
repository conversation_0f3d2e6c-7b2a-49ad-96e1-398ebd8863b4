'use client';

import { useEffect, useState } from 'react';
import { Box, Typography, IconButton } from '@mui/material';
import { PatientProfileWelcome } from '../profile';
import { ORAGANIZATION_WELCOME_PIC, PATIENT_WELCOME_PIC } from '../auth';
import Image from 'next/image';
import Iconify from '../iconify/iconify';
import WelcomeCardButtons from './WelcomeCardButtons';
import { getDecodedToken } from '@minicardiac-client/utilities';
import { useAuth } from '@minicardiac-client/apis';

type Props = {
  onClose: () => void;
};

const PatientWelcomeCard = ({ onClose }: Props) => {
  const { authState } = useAuth();
  const [accountType, setAccountType] = useState<
    'ORGANISATION' | 'PATIENT' | null
  >(null);

  useEffect(() => {
    const fetchToken = async () => {
      try {
        const decodedToken = await getDecodedToken();
        console.log(decodedToken);
        setAccountType(decodedToken.accountType);
      } catch (error) {
        console.error('Failed to decode token:', error);
        setAccountType('PATIENT'); // fallback
      }
    };

    fetchToken();
  }, []);

  const imageSrc =
    accountType === 'ORGANISATION'
      ? ORAGANIZATION_WELCOME_PIC
      : PATIENT_WELCOME_PIC;

  return (
    <Box
      sx={{
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        backgroundColor: 'white',
        borderRadius: '8px',
        px: '20px',
      }}
    >
      {/* Cancel (X) Icon */}
      <IconButton
        onClick={onClose}
        sx={{
          position: 'absolute',
          top: { xs: 0, md: 30 },
          right: 16,
          zIndex: 2,
        }}
      >
        <Iconify
          icon="material-symbols:close-rounded"
          sx={{
            color: 'secondary.main',
            fontSize: '35px',
            width: '35px',
            height: '35px',
          }}
        />
      </IconButton>

      {/* Patient Name */}
      <PatientProfileWelcome patientName={authState.user?.displayName || ''} />

      {/* Image */}
      <Box
        sx={{
          width: '100%',
          maxWidth: 500,
          mt: '40px',
        }}
      >
        <Box
          sx={{
            position: 'relative',
            width: '100%',
            height: { xs: 200, md: 400 },
            borderRadius: 2,
            overflow: 'hidden',
          }}
        >
          {accountType && (
            <Image
              alt="welcome-card-image"
              src={imageSrc}
              fill
              style={{ objectFit: 'contain' }}
            />
          )}
        </Box>
      </Box>

      {/* Question Text */}
      <Box
        sx={{
          mt: '40px',
          textAlign: 'center',
        }}
      >
        <Typography
          sx={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 400,
            fontSize: { xs: '16px', md: '20px' },
          }}
        >
          Would you like a walkthrough of the application or do you prefer to
          explore on your own?
        </Typography>
      </Box>

      <WelcomeCardButtons onClose={onClose} />
    </Box>
  );
};

export default PatientWelcomeCard;
