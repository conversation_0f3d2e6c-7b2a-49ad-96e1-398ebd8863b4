'use client';

import { Box, Typography } from '@mui/material';

interface WelcomeProps {
  title: string;
}

/**
 * Welcome component that displays the welcome message
 */
export const Welcome = ({ title }: WelcomeProps) => {
  return (
    <Box
      sx={{
        textAlign: 'center',
        mb: 1,
        whiteSpace: 'nowrap',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '4px',
      }}
    >
      <Typography
        sx={(theme) => ({
          ...(theme.typography as unknown as { heading1: React.CSSProperties })
            .heading1,
          display: 'inline-block',
          fontSize: {
            xs: '28px',
            sm: '32px',
            md: '32px',
            lg: '32px',
          },
          textTransform: 'none',
        })}
      >
        Welcome to
      </Typography>
      <Typography
        component="span"
        sx={(theme) => ({
          ...(theme.typography as unknown as { heading2: React.CSSProperties })
            .heading2,
          display: 'inline-block',
          fontSize: {
            xs: '28px',
            sm: '32px',
            md: '32px',
            lg: '32px',
          },
        })}
      >
        MiniCardiac
      </Typography>
    </Box>
  );
};

export default Welcome;
