import { Avatar, Box, TextField } from '@mui/material';

const AddCommentBar = () => (
  <Box
    position="fixed"
    bottom={0}
    left={0}
    right={0}
    bgcolor="#fff"
    boxShadow="0px -4px 20px rgba(0,0,0,0.08)"
    display="flex"
    gap="12px"
    alignItems="flex-start"
    px={'40px'}
    py={'20px'}
  >
    <Box width={824} display={'flex'} flexDirection={'row'} gap={'8px'}>
      <Avatar src="/placeholder-avatar.png" sx={{ width: 40, height: 40 }} />
      <TextField
        fullWidth
        size="small"
        placeholder="What do you think of this post?"
        InputProps={{
          sx: {
            backgroundColor: '#F3F4F6',
            borderRadius: '8px',
            px: 2,
            fontSize: '14px',
            height: '45px',
          },
        }}
      />
    </Box>
  </Box>
);

export default AddCommentBar;
