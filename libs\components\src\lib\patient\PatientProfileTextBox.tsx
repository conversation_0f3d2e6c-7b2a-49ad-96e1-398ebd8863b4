import { Box, TextField, Typography } from '@mui/material';
import { ExtendedTheme } from '../auth';
import { INTRODUCTORY_STATEMENT_LIMITS } from '@minicardiac-client/constants';

type TextBoxProps = {
  introText: string;
  introLabel?: string;
  placeholder?: string;
  handleIntroTextChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: () => void;
  isUploading: boolean;
  selectedFile: File | null;
  theme: ExtendedTheme;
};

const PatientProfileTextBox = ({
  introText,
  introLabel,
  placeholder,
  handleIntroTextChange,
  handleSubmit,
  isUploading,
  selectedFile,
  theme,
}: TextBoxProps) => {
  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        zIndex: 1,
        height: '100%',
      }}
    >
      <TextField
        label={introLabel || "Introductory Statement"}
        value={introText}
        onChange={handleIntroTextChange}
        multiline
        rows={7}
        placeholder={placeholder || "Write a couple of lines to introduce yourself to others in the community. If you would like to - this is entirely optional!"}
        fullWidth
        variant="outlined"
        inputProps={{ maxLength: INTRODUCTORY_STATEMENT_LIMITS.MAX }}
        InputLabelProps={{ shrink: true }}
        sx={{
          '& .MuiInputLabel-root': {
            fontWeight: 500,
            fontSize: theme.typography.pxToRem(16),
            color: (theme.palette as any).neutral?.[900] || '#1E1E1E',
          },
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: '#A3A3A3',
            borderRadius: '8px',
          },
          '& .MuiOutlinedInput-root': {
            height: 'auto',
            borderRadius: '8px',
            backgroundColor: '#FFFFFF',
            fontFamily: 'Plus Jakarta Sans, sans-serif',
            fontSize: theme.typography.pxToRem(14),
            color: (theme.palette as any).neutral?.[900] || '#1E1E1E',
          },
          '& .MuiOutlinedInput-inputMultiline': {
            padding: '16px 14px',
          },
        }}
      />

      <Box
        sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'flex-end',
          mt: 0.5,
          position: 'absolute',
          bottom: '0px',
          right: '10px',
        }}
      >
        <Typography
          variant="body2"
          sx={{
            color: (theme.palette as any).neutral?.[600] || '#737678',
            fontSize: theme.typography.pxToRem(14),
            mb: '16px',
            mr: '14px',
          }}
        >
          {introText.length}/{INTRODUCTORY_STATEMENT_LIMITS.MAX} characters
        </Typography>
      </Box>
    </Box>
  );
};

export default PatientProfileTextBox;
