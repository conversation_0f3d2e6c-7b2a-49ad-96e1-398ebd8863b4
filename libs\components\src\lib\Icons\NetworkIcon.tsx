import { useState } from 'react';

const NetworkIcon = ({
  fill = 'currentColor',
  hoverFill = '#A24295',
  size = 24,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const currentFill = isHovered ? hoverFill : fill;

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <path
        d="M11.9691 18.2763C10.9557 18.2649 9.97403 18.1093 9.06135 17.639C8.1924 17.1914 7.69389 16.5635 7.81482 15.5009C7.97188 14.121 8.27143 12.8024 9.09382 11.6576C9.97507 10.4309 11.1081 9.67336 12.693 9.98684C13.8771 10.2211 14.6561 11.0314 15.2545 12.0213C15.9875 13.2339 16.2773 14.5752 16.2971 15.9778C16.3054 16.569 15.9796 17.0027 15.5188 17.3304C14.518 18.0422 13.3685 18.2462 12.1722 18.276C12.1046 18.2777 12.0368 18.2763 11.9691 18.2763Z"
        fill={currentFill}
        stroke="white"
        strokeWidth="0.5"
      />
      <path
        d="M9.34448 10.5608C7.75643 12.2417 7.26724 14.2892 7.29014 16.5047C6.4507 16.7045 4.65847 16.2134 4.19283 15.3851C4.01131 15.0623 3.97997 14.6067 4.00995 14.2217C4.12675 12.7215 4.58314 11.3544 5.78395 10.3453C6.84916 9.45013 8.38985 9.53283 9.34448 10.5608Z"
        fill={currentFill}
      />
      <path
        d="M16.7802 16.5787C16.8527 14.354 16.3856 12.3897 14.9083 10.7576C14.6999 10.5273 14.816 10.4131 14.9938 10.2736C15.9579 9.51736 17.3307 9.5277 18.2901 10.3528C19.3687 11.2804 19.9126 12.486 19.9827 13.9038C19.9973 14.2008 19.9567 14.5023 19.991 14.796C20.0395 15.2111 19.8603 15.4972 19.5551 15.7335C18.7378 16.3661 17.7913 16.5523 16.7802 16.5787Z"
        fill={currentFill}
      />
      <path
        d="M9.87902 7.17342C9.89084 5.94707 10.8347 4.99367 12.0307 5.00003C13.3022 5.0068 14.2406 5.9491 14.2323 7.21077C14.2243 8.42143 13.2643 9.36842 12.0552 9.35826C10.8009 9.34773 9.8671 8.41018 9.87902 7.17342Z"
        fill={currentFill}
      />
      <path
        d="M7.47345 9.45369C6.45474 9.46022 5.68482 8.69225 5.67585 7.66068C5.66727 6.67391 6.45572 5.8804 7.44916 5.876C8.46209 5.87151 9.24993 6.65887 9.25187 7.67764C9.25377 8.67411 8.47961 9.44724 7.47345 9.45369Z"
        fill={currentFill}
      />
      <path
        d="M16.5919 9.45889C15.604 9.45902 14.8123 8.67226 14.8047 7.68283C14.797 6.6759 15.5998 5.87603 16.6102 5.88385C17.6107 5.89159 18.3961 6.67095 18.3975 7.6574C18.3989 8.66719 17.6056 9.45875 16.5919 9.45889Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default NetworkIcon;
