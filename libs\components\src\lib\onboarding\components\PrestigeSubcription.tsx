import { Box, Link, Typography } from '@mui/material';

const PrestigeSubscription = () => {
  return (
    <Box
      sx={(theme) => ({
        position: '',
        '@media (min-width:1280px)': {
          position: 'absolute',
        },
        bottom: '55px',
        right: '-160px',
        transform: 'none',
        borderRadius: '12px',
        border: 'none',
        background: {
          xs: '#A2429525',
          lg: `linear-gradient(90deg, rgba(162, 66, 149, 0.2) 0%, rgba(220, 183, 215, 0.2) 60.08%, rgba(255, 255, 255, 0.2) 100%)`,
        },
        display: { xs: 'none', sm: 'flex' },
        flexDirection: 'column',
        justifyContent: 'center',
        padding: '12px 18px',
        zIndex: 10,
        marginLeft: '20px',
        gap: '18px',
      })}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
        }}
      >
        <Typography
          variant="h6"
          sx={{
            mb: 0.5,
            color: '#1E1E1E',
          }}
        >
          Want the prestige subscription without the price tag?
        </Typography>
        <Typography
          variant="body2"
          sx={{
            mb: 1,
          }}
        >
          Apply for industry sponsorship to become a brand ambassador
        </Typography>
      </Box>
      <Link
        component="button"
        sx={(theme) => ({
          color:
            theme.palette.secondary.main,
          textDecoration: 'none',
          fontFamily: theme.typography.fontFamily,
          fontWeight: 500,
          fontSize: '14px',
          lineHeight: '20px',
          display: 'flex',
          alignItems: 'center',
          alignSelf: 'flex-start',
          '&:hover': {
            textDecoration: 'underline',
          },
        })}
      >
        Find out more →
      </Link>
    </Box>
  );
};

export default PrestigeSubscription;
