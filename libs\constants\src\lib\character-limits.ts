/**
 * Character limits for various text inputs across the application
 * Based on industry standards and MiniCardiac requirements
 */

/**
 * Introductory Statement Character Limits
 * Industry Range: 150-180 characters
 * MiniCardiac: 240 characters
 */
export const INTRODUCTORY_STATEMENT_LIMITS = {
  MAX: 240,
} as const;

/**
 * Bio Character Limits
 * Industry Range: 180-3000 characters
 * MiniCardiac: 8000 characters (Medical personnel might have specialised/lengthier descriptions)
 */
export const BIO_LIMITS = {
  MAX: 8000,
} as const;

/**
 * Text Post Character Limits
 * Industry Range: 150-5000 characters (Twitter: 64k+ paid, Facebook: 25k)
 * MiniCardiac: 20000 characters
 */
export const TEXT_POST_LIMITS = {
  MAX: 20000,
} as const;

/**
 * Article Character Limits
 * Based on medical article standards (not social media standards)
 * Industry Range: up to 10000 words (≈ 72000 characters)
 * MiniCardiac: 75000 characters
 */
export const ARTICLE_LIMITS = {
  MAX: 75000,
} as const;

/**
 * Article Summary Character Limits
 * Industry Range: up to 500 words (≈ 3600 characters)
 * MiniCardiac: 4000 characters
 */
export const ARTICLE_SUMMARY_LIMITS = {
  MAX: 4000,
} as const;




