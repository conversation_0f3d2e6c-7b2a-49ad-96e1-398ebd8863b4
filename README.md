# Minicardiac Client Monorepo

This repository contains the client-side applications and shared libraries for the Minicardiac platform, built using Nx monorepo architecture.

## Project Structure

This monorepo contains multiple applications and libraries:

### Applications
- **Public** (`/apps/public/`) - Next.js application for public-facing components and pages requiring SEO indexing
- **Admin** (`/apps/admin/`) - React application for admin-only interfaces that don't require SEO indexing

### Libraries
- **Components** (`/libs/components/`) - Reusable UI components
- **Shared** (`/libs/shared/`) - Shared utilities and theme
- **APIs** (`/libs/apis/`) - TanStack Query hooks for backend modules
- **Types** (`/libs/types/`) - Global TypeScript interfaces
- **Constants** (`/libs/constants/`) - Application-wide constants
- **Utilities** (`/libs/utilities/`) - Shared functions and helpers

## Getting Started

### Prerequisites
- Node.js (v18+)
- pnpm
- nx

### Installation

1. Clone the repository
   ```bash
   git clone https://github.com/MiniCardiac/minicardiac-client.git
   cd minicardiac-client
   ```

2. Install dependencies
   ```bash
   pnpm install
   ```

3. Set up Doppler for secrets management
   ```bash
   # Install Doppler CLI if not already installed
   # For Windows (using scoop)
   scoop install doppler

   # For macOS
   brew install dopplerhq/cli/doppler

   # Login to Doppler
   doppler login

   # Configure Doppler for your project
   doppler setup
   ```

   Required environment variables include Firebase configuration:
   ```
   NEXT_PUBLIC_FIREBASE_API_KEY
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
   NEXT_PUBLIC_FIREBASE_PROJECT_ID
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
   NEXT_PUBLIC_FIREBASE_APP_ID
   NEXT_PUBLIC_SERVER_URL (defaults to http://localhost:9200)
   ```

## Development

Run both applications in development mode:

```bash
# With Doppler (recommended)
doppler run -- pnpm nx run-many --targets=serve --projects=public,admin

# Or run applications individually
doppler run -- pnpm start:public
doppler run -- pnpm start:admin
```

Applications will be available at:
- Public (Next.js): http://localhost:3000
- Admin: http://localhost:4200

### API Configuration

The applications are configured to proxy API requests to a backend service running on `localhost:9200` by default. You can change this by setting the `NEXT_PUBLIC_SERVER_URL` environment variable in your Doppler project.

```bash
# Example: Setting API URL in Doppler
doppler secrets set NEXT_PUBLIC_SERVER_URL=https://api.example.com
```

## Building

Build all applications:

```bash
doppler run -- pnpm nx run-many --targets=build --projects=public,admin
```

Or build applications individually:

```bash
doppler run -- pnpm nx build public
doppler run -- pnpm nx build admin
```

## Testing

Run tests for all projects:

```bash
doppler run -- pnpm nx run-many --targets=test --all
```

Or test specific projects:

```bash
doppler run -- pnpm nx test public
doppler run -- pnpm nx test admin
```

## Development Workflow

### Creating New Components

To add a new component to the shared components library:

```bash
pnpm nx generate @nx/react:component --project=components --name=MyComponent
```

### Creating New Libraries

To create a new library:

```bash
pnpm nx generate @nx/js:library --name=my-new-lib --directory=libs
```

### Dependency Graph

View the project dependency graph:

```bash
pnpm nx graph
```

## Technology Stack

- **Framework**: Next.js
- **State Management**: React Context API
- **Data Fetching**: TanStack Query
- **UI Components**: Material UI
- **Authentication**: Firebase Authentication
- **Form Handling**: React Hook Form with Yup validation
- **Build System**: Nx

## Project Standards

- Use TypeScript for type safety
- Follow the component structure within the components library
- Keep shared code in appropriate libraries
- Use TanStack Query for API requests
- Material UI for consistent styling

## Ports

| Application | Development Port |
|-------------|------------------|
| Public      | 3000             |
| Admin       | 4200             |

## Troubleshooting

### PNPM Global Installation Issues

If you encounter issues with global PNPM installations:

```bash
# Set up PNPM properly
pnpm setup

# For Windows PowerShell, manually set environment variables
$env:PNPM_HOME = "$env:LOCALAPPDATA\pnpm"
$env:Path = "$env:PNPM_HOME;$env:Path"
```

## Resources

- [Nx Documentation](https://nx.dev/)
- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [TanStack Query](https://tanstack.com/query/latest)
- [Material UI](https://mui.com/)
