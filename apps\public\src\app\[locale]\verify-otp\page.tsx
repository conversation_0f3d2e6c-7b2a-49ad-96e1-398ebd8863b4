'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  AuthLayout,
  OtpVerification,
  useSnackbar,
  FullPageLoader,
} from '@minicardiac-client/components';
import { useAuth } from '@minicardiac-client/apis';
import { VerifyOTPRedirect } from '../../../components/protected-route';
import { useRouter } from '@/apps/public/src/i18n/navigation';
import { useTranslations } from 'next-intl';

export default function VerifyOtpPage() {
  const router = useRouter();
  const t = useTranslations('signup');
  const searchParams = useSearchParams();
  const [email, setEmail] = useState<string>('');
  const [displayName, setDisplayName] = useState<string>('');
  const [accountType, setAccountType] = useState<string>('');
  const { showSuccess, showError } = useSnackbar();

  const [isVerifying, setIsVerifying] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  const { verifyOtp, regenerateOtp, signInWithCustomToken } = useAuth();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (searchParams) {
      const emailParam = searchParams.get('email');
      const nameParam = searchParams.get('name');
      const accountTypeParam = searchParams.get('accountType');

      if (emailParam) setEmail(emailParam);
      if (nameParam) setDisplayName(nameParam);
      if (accountTypeParam) setAccountType(accountTypeParam);
    }
  }, [searchParams]);

  const handleVerify = async (otp: string) => {
    if (!email) return;

    setIsVerifying(true);
    try {
      const response = await verifyOtp({ email, otp });

      if (response && response.customToken) {
        showSuccess(t('otpVerifiedSuccess'));

        await signInWithCustomToken(response.customToken);
       setIsRedirecting(true);
        showSuccess(t('authenticationSuccessRedirect'));

        if (accountType === 'PROFESSIONAL') {
          router.push('/professional/type-selection');
        } else if (accountType === 'ORGANISATION') {
          router.push('/organisation/subscription');
        } else {
          router.push(
            `/patient/profile?name=${encodeURIComponent(displayName || '')}`
          );
        }
      } else {
        showError(t('otpVerificationFailed'));
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      showError(t('genericErrorMessage'));
    } finally {
      if (!isRedirecting) {
        setIsVerifying(false);
      }
    }
  };

  const handleResendOtp = async () => {
    if (!email) return Promise.reject(new Error('Email is required'));

    try {
      await regenerateOtp({ email });
      showSuccess(t('otpResent'));
      return Promise.resolve();
    } catch (error) {
      showError(t('genericErrorMessage'));
      return Promise.reject(error);
    }
  };

  if (!isMounted) {
    return (
      <AuthLayout activeTab={0} showCarousel={false}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
          }}
        >
          <div>{t('loading')}</div>
        </div>
      </AuthLayout>
    );
  }

  // Optional: uncomment if needed
  // if (!email) {
  //   return (
  //     <AuthLayout
  //       activeTab={1}
  //       showCarousel={true}
  //       showBackButton={true}
  //       onBackClick={() => router.back()}
  //     >
  //       <div>{t('missingEmailParam')}</div>
  //     </AuthLayout>
  //   );
  // }

  return (
    <VerifyOTPRedirect>
      {isMounted && (
        <FullPageLoader
          open={isRedirecting}
          message={t('redirectingToProfile')}
        />
      )}

      <AuthLayout
        showCarousel={true}
        showBackButton={true}
        sx={{ justifyContent: { xs: 'top', sm: 'top', md: 'center' } }}
        onBackClick={() => router.back()}
        activeTab={0}
      >
        <OtpVerification
          email={email || ''}
          displayName={displayName || ''}
          onVerify={handleVerify}
          onResendOtp={handleResendOtp}
          isVerifying={isVerifying}
          welcomeText={t('welcomeTo')}
          otpInputLabel={t('otpLabel')}
          otpInputPlaceholder={t('otpInputPlaceholder')}
          otpInstruction={t('otpInstruction')}
          resendOtpText={t('resendOtp')}
          resendSuccessText={t('otpResentSuccess')}
          sendingText={t('sending')}
          verifyingText={t('verifying')}
          signInButtonText={t('signIn')}
        />
      </AuthLayout>
    </VerifyOTPRedirect>
  );
}
