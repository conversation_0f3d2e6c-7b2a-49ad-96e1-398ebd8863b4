import {
  Box,
  Button,
  IconButton,
  LinearProgress,
  Stack,
  Typography,
  alpha,
  linearProgressClasses,
  styled,
  Grid,
} from '@mui/material';
import { useState } from 'react';
import {
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Error as <PERSON>rrorIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';

import CustomizedSteppers from '../Stepper/Stepper';
import { DOCUMENT_REQUIREMENTS } from '../../constants/onboarding.constants';
import TooltipExtended from './Tooltip';
import { Subtitle } from '../../../auth/components/Subtitle';

const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 12,
  borderRadius: 99,
  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor: alpha(theme.palette.grey[500], 0.25),
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 99,
    backgroundImage: `linear-gradient(to right, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: '100% 100%',
  },
}));

type UploadStatus = {
  file: File;
  status: string;
  progress: number;
  errorMessage?: string;
  id: string; // Unique identifier for each upload
};

// Modified structure to hold multiple documents per fieldId
type UploadStatusesState = {
  [fieldId: string]: UploadStatus[];
};

const fileTypeError = 'Incorrect file type.';
const networkError = 'Network error.';

// Mock function for file upload
const uploadFile = (fieldId: string) => {
  return new Promise((resolve, reject) => {
    // Simulate network request
    setTimeout(() => {
      // Random success or failure
      if (Math.random() > 0.2) {
        resolve({ success: true, fieldId });
      } else {
        reject({
          success: false,
          fieldId,
          error: Math.random() > 0.5 ? fileTypeError : networkError,
        });
      }
    }, 2000);
  });
};

const DocumentUploadField = ({
  fieldId,
  label,
  optional = false,
  maxCount = 1,
  onFileUpload,
  uploadStatuses,
  onFileRemove,
  onFileRetry,
  instructions,
}: {
  fieldId: string;
  label: string;
  optional: boolean;
  maxCount: number;
  onFileUpload: (file: File, fieldId: string) => void;
  uploadStatuses: UploadStatus[];
  onFileRemove: (fieldId: string, uploadId: string) => void;
  onFileRetry: (fieldId: string, uploadId: string) => void;
  instructions: string;
}) => {
  const handleFileSelect = (e: any) => {
    if (e.target.files.length > 0) {
      onFileUpload(e.target.files[0], fieldId);
    }
  };

  // Check if max file limit is reached
  const isMaxLimitReached = uploadStatuses.length >= maxCount;

  return (
    <Grid
      container
      sx={{
        pl: { xs: '16px', sm: '40px' },
        pr: { xs: '16px', sm: 2 },
        alignItems: 'center',
        borderBottom: (theme) =>
          `1px dashed ${alpha((theme.palette as any).neutral[500], 0.25)}`,
      }}
    >
      <Grid
        container
        flexGrow={1}
        // height={100}
        minHeight={90}
        alignItems={'center'}
        sx={{
          flexDirection: { xs: 'row', sm: 'row' },
          flexWrap: 'wrap',
          justifyContent: {
            xs: 'space-between',
            sm: 'flex-start',
          },
          rowGap: { xs: 1, sm: 0 },
        }}
      >
        {/* Left section - Label */}
        <Grid size={{ xs: 4.5 }} sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography
                variant={'subtitle4' as any}
                sx={{ fontSize: { xs: '12px', sm: '16px' }, fontWeight: 600 }}
              >
                {label}
                {optional && (
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{ ml: 0.5 }}
                  >
                    (Optional)
                  </Typography>
                )}
              </Typography>
              <TooltipExtended />
            </Box>
          </Box>
        </Grid>

        {/* Middle section - Add files button */}
        <Grid size={{ xs: 12, sm: 4.5 }}>
          <Button
            component="label"
            sx={{ color: 'secondary' }}
            disabled={isMaxLimitReached}
          >
            Add files
            <input
              type="file"
              hidden
              onChange={handleFileSelect}
              disabled={isMaxLimitReached}
            />
          </Button>
        </Grid>

        {/* Right section - First file info and status */}
        <Grid size={{ xs: 12, sm: 6 }} sx={{ width: '100%' }}>
          {uploadStatuses.length > 0 && (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
                borderRadius: 2,
                padding: 2,
              }}
            >
              {/* File name */}
              <Typography
                variant="body2"
                noWrap
                sx={{
                  width: '100%',
                  fontWeight: 500,
                }}
              >
                {uploadStatuses[0].file.name}
              </Typography>

              {/* Status and actions */}
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  // alignItems: {
                  //   xs: 'flex-start',
                  //   sm: 'center',
                  // },
                  justifyContent: 'space-between',
                  gap: 1.5,
                  width: '100%',
                }}
              >
                {/* Status Display */}
                <Box sx={{ flexGrow: 1 }}>
                  {uploadStatuses[0].status === 'uploading' && (
                    <BorderLinearProgress
                      variant="determinate"
                      value={uploadStatuses[0].progress}
                      sx={{ width: { xs: '100%', sm: '280px' } }}
                    />
                  )}

                  {uploadStatuses[0].status === 'success' && (
                    <Box
                      sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
                    >
                      <CheckCircleIcon color="success" fontSize="small" />
                      <Typography variant="body2">Upload successful</Typography>
                    </Box>
                  )}

                  {uploadStatuses[0].status === 'error' && (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5,
                        color: 'error.main',
                      }}
                    >
                      <ErrorIcon color="error" fontSize="small" />
                      <Typography variant="caption" color="error">
                        {uploadStatuses[0].errorMessage}
                      </Typography>
                    </Box>
                  )}
                </Box>

                {/* Action Buttons */}
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: 1.5,
                    flexWrap: 'wrap',
                  }}
                >
                  {uploadStatuses[0].status === 'error' ? (
                    uploadStatuses[0].errorMessage === networkError ? (
                      <IconButton
                        size="small"
                        onClick={() =>
                          onFileRetry(fieldId, uploadStatuses[0].id)
                        }
                      >
                        <RefreshIcon color="secondary" />
                      </IconButton>
                    ) : (
                      <IconButton
                        size="small"
                        onClick={() =>
                          onFileRemove(fieldId, uploadStatuses[0].id)
                        }
                      >
                        <CloseIcon color="secondary" />
                      </IconButton>
                    )
                  ) : uploadStatuses[0].status === 'success' ? (
                    <IconButton
                      size="small"
                      onClick={() =>
                        onFileRemove(fieldId, uploadStatuses[0].id)
                      }
                    >
                      <DeleteIcon color="secondary" />
                    </IconButton>
                  ) : (
                    <IconButton
                      size="small"
                      onClick={() =>
                        onFileRemove(fieldId, uploadStatuses[0].id)
                      }
                    >
                      <CloseIcon color="secondary" />
                    </IconButton>
                  )}
                </Box>
              </Box>
            </Box>
          )}
        </Grid>
      </Grid>

      {/* Additional files section - Reusing the commented code for multiple files */}
      {uploadStatuses.length > 1 &&
        uploadStatuses.slice(1).map((uploadStatus, index) => (
          <Grid
            key={uploadStatus.id}
            size={{ xs: 6 }}
            offset={6}
            height={{ xs: '70px', sm: '90px' }}
            alignContent={'center'}
            justifySelf={'end'}
            container
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                width: '100%',
              }}
            >
              <Box
                sx={{ display: 'flex', alignItems: 'center', width: '100%' }}
              >
                <Typography
                  variant="body2"
                  noWrap
                  sx={{
                    width: 120,
                    flexShrink: 0,
                  }}
                >
                  {uploadStatus.file.name}
                </Typography>

                <Box sx={{ flexGrow: 1, ml: 8 }}>
                  {uploadStatus.status === 'uploading' && (
                    <BorderLinearProgress
                      variant="determinate"
                      value={uploadStatus.progress}
                      sx={{ width: '280px' }}
                    />
                  )}

                  {uploadStatus.status === 'success' && (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <CheckCircleIcon
                        color="success"
                        fontSize="small"
                        width={16}
                        sx={{ mr: 0.5 }}
                      />
                      <Typography
                        variant="body2"
                        noWrap
                        sx={{
                          flexShrink: 0,
                          ml: 0.5,
                        }}
                      >
                        Upload successful
                      </Typography>
                    </Box>
                  )}

                  {uploadStatus.status === 'error' && (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        color: 'error.main',
                      }}
                    >
                      <ErrorIcon
                        color="error"
                        fontSize="small"
                        width={16}
                        sx={{ mr: 0.5 }}
                      />
                      <Typography variant="caption" color="error">
                        {uploadStatus.errorMessage}
                      </Typography>
                    </Box>
                  )}
                </Box>

                {uploadStatus.status === 'error' ? (
                  uploadStatus.errorMessage === networkError ? (
                    <IconButton
                      size="small"
                      onClick={() => onFileRetry(fieldId, uploadStatus.id)}
                    >
                      <RefreshIcon
                        fontSize="small"
                        color="secondary"
                        sx={{
                          width: 24,
                          height: 24,
                        }}
                      />
                    </IconButton>
                  ) : (
                    <IconButton
                      size="small"
                      onClick={() => onFileRemove(fieldId, uploadStatus.id)}
                    >
                      <CloseIcon
                        fontSize="small"
                        color="secondary"
                        sx={{
                          width: 24,
                          height: 24,
                        }}
                      />
                    </IconButton>
                  )
                ) : uploadStatus.status === 'success' ? (
                  <IconButton
                    size="small"
                    onClick={() => onFileRemove(fieldId, uploadStatus.id)}
                  >
                    <DeleteIcon
                      fontSize="small"
                      color="secondary"
                      sx={{
                        width: 24,
                        height: 24,
                      }}
                    />
                  </IconButton>
                ) : (
                  <IconButton
                    size="small"
                    onClick={() => onFileRemove(fieldId, uploadStatus.id)}
                  >
                    <CloseIcon
                      fontSize="small"
                      color="secondary"
                      sx={{
                        width: 24,
                        height: 24,
                      }}
                    />
                  </IconButton>
                )}
              </Box>
            </Box>
          </Grid>
        ))}
    </Grid>
  );
};

const useGetDocumentDetails = () => {
  return DOCUMENT_REQUIREMENTS;
};

const DocumentUploadForm = ({
  hideSteppers = false,
}: { hideSteppers?: boolean } = {}) => {
  const [uploadStatuses, setUploadStatuses] = useState<UploadStatusesState>({});

  const documentFields = useGetDocumentDetails();

  // Generate a unique ID for each upload
  const generateUniqueId = () =>
    `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const handleFileUpload = (file: File, fieldId: string) => {
    // Find the field config to check maxCount
    const fieldConfig = documentFields.find((field) => field.id === fieldId);
    if (!fieldConfig) return;

    // Check if max limit is reached
    const currentUploads = uploadStatuses[fieldId] || [];
    if (currentUploads.length >= fieldConfig.maxCount) {
      console.warn(
        `Maximum number of files (${fieldConfig.maxCount}) reached for ${fieldConfig.documentType.name}`
      );
      return;
    }

    const uploadId = generateUniqueId();

    // Set initial uploading state
    setUploadStatuses((prev) => ({
      ...prev,
      [fieldId]: [
        ...(prev[fieldId] || []),
        {
          id: uploadId,
          file,
          status: 'uploading',
          progress: 0,
        },
      ],
    }));

    // Simulate progress
    const progressInterval = setInterval(() => {
      setUploadStatuses((prev) => {
        const currentUploads = prev[fieldId] || [];
        const uploadIndex = currentUploads.findIndex(
          (upload) => upload.id === uploadId
        );

        if (uploadIndex === -1) return prev;

        const current = currentUploads[uploadIndex];
        if (current.status === 'uploading' && current.progress < 80) {
          const updatedUploads = [...currentUploads];
          updatedUploads[uploadIndex] = {
            ...current,
            progress: current.progress + 20,
          };

          return {
            ...prev,
            [fieldId]: updatedUploads,
          };
        }
        return prev;
      });
    }, 500);

    // Upload the file
    uploadFile(fieldId)
      .then(() => {
        clearInterval(progressInterval);
        setUploadStatuses((prev) => {
          const currentUploads = prev[fieldId] || [];
          const uploadIndex = currentUploads.findIndex(
            (upload) => upload.id === uploadId
          );

          if (uploadIndex === -1) return prev;

          const updatedUploads = [...currentUploads];
          updatedUploads[uploadIndex] = {
            ...updatedUploads[uploadIndex],
            status: 'success',
            progress: 100,
          };

          return {
            ...prev,
            [fieldId]: updatedUploads,
          };
        });
      })
      .catch((error) => {
        clearInterval(progressInterval);
        setUploadStatuses((prev) => {
          const currentUploads = prev[fieldId] || [];
          const uploadIndex = currentUploads.findIndex(
            (upload) => upload.id === uploadId
          );

          if (uploadIndex === -1) return prev;

          const updatedUploads = [...currentUploads];
          updatedUploads[uploadIndex] = {
            ...updatedUploads[uploadIndex],
            status: 'error',
            progress: 100,
            errorMessage: error.error,
          };

          return {
            ...prev,
            [fieldId]: updatedUploads,
          };
        });
      });
  };

  const handleFileRemove = (fieldId: string, uploadId: string) => {
    setUploadStatuses((prev) => {
      const currentUploads = prev[fieldId] || [];
      const updatedUploads = currentUploads.filter(
        (upload) => upload.id !== uploadId
      );

      if (updatedUploads.length === 0) {
        const { [fieldId]: _, ...rest } = prev;
        return rest;
      }

      return {
        ...prev,
        [fieldId]: updatedUploads,
      };
    });
  };

  const handleFileRetry = (fieldId: string, uploadId: string) => {
    const currentUploads = uploadStatuses[fieldId] || [];
    const uploadIndex = currentUploads.findIndex(
      (upload) => upload.id === uploadId
    );

    if (uploadIndex === -1) return;

    const { file } = currentUploads[uploadIndex];
    if (!file) return;

    // Remove the failed upload
    handleFileRemove(fieldId, uploadId);

    // Create a new upload with the same file
    handleFileUpload(file, fieldId);
  };

  return (
    <Stack alignItems={'center'} mb={{ xs: '150px', sm: '10px' }}>
      {!hideSteppers && (
        <CustomizedSteppers
          activeStep={1} // Set to 1 for Document Upload (0-based index)
          steps={['Profile Setup', 'Document Upload', 'Adding Network']}
        />
      )}

      <Subtitle
        text={
          'Please upload your documents for verification. This is strictly confidential and will not be publicly accessible'
        }
        sx={{
          fontSize: {
            xs: '12px',
            sm: '16px',
          },
          mt: '40px',
        }}
      />
      <Box
        sx={{
          width: 1,
          borderRadius: 1,
          boxShadow: (theme) =>
            `0px 12px 24px 0px ${alpha(
              (theme.palette as any).neutral[500],
              0.25
            )}`,
        }}
      >
        {documentFields.map((field) => (
          <DocumentUploadField
            key={field.id}
            fieldId={field.id}
            label={field.documentType.name}
            optional={!field.isRequired}
            maxCount={field.maxCount}
            instructions={field.instructions}
            onFileUpload={handleFileUpload}
            onFileRemove={handleFileRemove}
            onFileRetry={handleFileRetry}
            uploadStatuses={uploadStatuses[field.id] || []}
          />
        ))}
      </Box>
    </Stack>
  );
};

export default DocumentUploadForm;
