import { Box, Typography } from '@mui/material';
import CommentIcon from '../Icons/ContentPostIcons/CommentIcon';
import { Icon } from '@iconify/react';

interface PostFooterActionsProps {
  likes: number;
  comments: number;
  reposts: number;
  shares: number;
}

const actions = [
  {
    label: 'Like',
    IconComponent: () => (
      <Icon
        icon="mdi:heart-outline"
        style={{ color: '#A24295', fontSize: 24 }}
      />
    ),
    key: 'likes',
  },
  {
    label: 'Comment',
    IconComponent: CommentIcon,
    key: 'comments',
  },
  {
    label: 'Repost',
    IconComponent: () => (
      <Icon
        icon="garden:arrow-retweet-stroke-12"
        style={{ color: '#A24295', fontSize: 24 }}
      />
    ),
    key: 'reposts',
  },
  {
    label: 'Share',
    IconComponent: () => (
      <Icon icon="mdi:share" style={{ color: '#A24295', fontSize: 24 }} />
    ),
    key: 'shares',
  },
];

const PostFooterActions = ({
  likes,
  comments,
  reposts,
  shares,
}: PostFooterActionsProps) => {
  const counts: Record<string, number> = { likes, comments, reposts, shares };

  return (
    <Box
      display="flex"
      alignItems="center"
      justifyContent="space-between"
      gap="40px"
      mt="20px"
      sx={{ height: '38px', width: '100%' }}
    >
      {actions.map(({ label, IconComponent, key }) => (
        <Box
          key={key}
          display="flex"
          alignItems="center"
          justifyContent="center"
          gap="4px"
          sx={{ cursor: 'pointer' }}
        >
          <IconComponent />
          <Typography
            fontSize="12px"
            fontWeight={600}
            color="#A24295"
            sx={{
              display: { xs: 'none', sm: 'inline' },
            }}
          >
            {label} ({counts[key]})
          </Typography>

          <Typography
            fontSize="12px"
            fontWeight={600}
            color="#A24295"
            sx={{
              display: { xs: 'inline', sm: 'none' },
            }}
          >
            {counts[key]}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export default PostFooterActions;
