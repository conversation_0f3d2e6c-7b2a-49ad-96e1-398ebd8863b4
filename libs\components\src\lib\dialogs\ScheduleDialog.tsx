import { Box, Typography, Stack, useMediaQuery } from '@mui/material';
import { LoadingButton } from '../loading-button';
import CustomDialog from './CustomDialog';
import ScheduleDateTimePicker from './ScheduleDateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import PostToast from '../toast/PostToast';
import { toast } from 'react-toastify';
import { useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { BackButton } from '../buttons/Backbutton';
import { useTheme } from '@emotion/react';

interface SchedulePostDialogProps {
  open: boolean;
  onClose: () => void;
  returnToDialog?: string | null;
  setReturnDialogOpen?: (val: boolean) => void;
}

const SchedulePostDialog = ({
  open,
  onClose,
  returnToDialog,
  setReturnDialogOpen,
}: SchedulePostDialogProps) => {
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(dayjs());
  const [hour, setHour] = useState('');
  const [minute, setMinute] = useState('');

  const theme: any = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));

  const isScheduleEnabled =
    selectedDate !== null &&
    hour.trim() !== '' &&
    minute.trim() !== '' &&
    /^\d{1,2}$/.test(hour) &&
    /^\d{1,2}$/.test(minute);

  const handlePost = () => {
    onClose();
    toast(<PostToast value={'Post Scheduled'} />, {
      position: 'bottom-right',
      autoClose: 5000,
      hideProgressBar: true,
      closeButton: false,
      style: {
        padding: 0,
        width: 'fit-content',
        background: 'white',
        boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)',
      },
    });
  };

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title=""
      sx={{
        px: { xs: 0, sm: '110px' },
        pt: { xs: 0, sm: '154px' },
        alignItems: { xs: 'stretch', sm: 'start' },
        '.MuiDialog-paper': {
          maxHeight: { xs: '100%', sm: 'calc(100% - 64px' },
        },
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        gap={'40px'}
        sx={{
          backgroundColor: 'white',
          padding: { xs: '16px', sm: '20px', md: '20px', lg: '40px' },
        }}
      >
        <Box
          display="flex"
          justifyContent={{ xs: 'start', sm: 'space-between' }}
          gap={'16px'}
          alignItems="center"
        >
          {screenBelowSM && (
            <BackButton
              onClick={() => {
                setReturnDialogOpen?.(true);
              }}
            />
          )}

          <Typography
            sx={{
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 500,
              fontSize: { xs: '20px', sm: '28px' },
              color: '#1E1E1E',
              height: '35px',
            }}
          >
            Schedule Post
          </Typography>
        </Box>

        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <ScheduleDateTimePicker
            selectedDate={selectedDate}
            setSelectedDate={setSelectedDate}
            hour={hour}
            setHour={setHour}
            minute={minute}
            setMinute={setMinute}
          />
        </LocalizationProvider>

        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={{ xs: '10px', sm: '20px' }}
          justifyContent={{ xs: 'center', sm: 'center' }}
          sx={{
            position: { xs: 'fixed', sm: 'static' },
            bottom: 0,
            left: 0,
            mt: { xs: '0px', sm: '40px' },
            width: { xs: '100%', sm: 'auto' },
            background: { xs: 'rgba(255,255,255,0.8)', sm: 'transparent' },
            backdropFilter: { xs: 'blur(20px)', sm: 'none' },
            padding: { xs: '20px', sm: 0 },
            boxShadow: { xs: '0 -4px 20px 0 rgba(0,0,0,0.1)', sm: 'none' },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: '20px',
            }}
          >
            <LoadingButton
              variant="outlined"
              onClick={() => {
                setReturnDialogOpen?.(true);
              }}
              sx={{
                width: { xs: '156px' },
                height: '40px',
                backgroundColor: 'white',
                border: { xs: 'none', sm: '1px solid #A24295' },
                color: '#A24295',
                '&:hover': {
                  backgroundColor: 'secondary.light',
                },
                fontSize: '16px',
                fontWeight: 700,
              }}
            >
              Return to Post
            </LoadingButton>

            <LoadingButton
              variant="outlined"
              onClick={handlePost}
              disabled={!isScheduleEnabled}
              sx={{
                width: { xs: '156px' },
                height: '40px',
                backgroundColor: 'secondary.main',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'secondary.light',
                },
                fontSize: '16px',
                fontWeight: 700,
              }}
            >
              Schedule
            </LoadingButton>
          </Box>
        </Stack>
      </Box>
    </CustomDialog>
  );
};

export default SchedulePostDialog;
