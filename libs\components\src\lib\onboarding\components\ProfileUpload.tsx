import { Box, useTheme } from '@mui/material';
import { ExtendedTheme } from '../../auth/types/auth.types';
import { ProfilePicture } from '../../common/ProfilePicture';
import { useProfilePicture } from '../../common/ProfilePicture';

interface ProfileUploadProps {
  onImageUpload?: (imageUrl: string, thumbnailUrl: string) => void;
  initialImageUrl?: string;
  displayName?: string;
  disabled?: boolean;
}

const ProfileUpload = ({
  onImageUpload,
  initialImageUrl,
  displayName = '',
  disabled = false,
}: ProfileUploadProps) => {
  const theme = useTheme() as ExtendedTheme;
  const {
    previewUrl,
    isUploading,
    error,
    handleImageChange,
    handleRemove,
  } = useProfilePicture({
    initialImageUrl: initialImageUrl || null,
    onImageUpload,
  });

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
      <ProfilePicture
        theme={theme}
        previewUrl={previewUrl}
        displayName={displayName}
        onImageChange={handleImageChange}
        onRemove={handleRemove}
        isUploading={isUploading}
        error={error}
        disabled={disabled}
      />
    </Box>
  );
};

export default ProfileUpload;
