import { Box, Typography } from '@mui/material';
import ArticleCard from './ArticleCard';

const OtherArticlesSidebar = ({ articles }: { articles: any[] }) => (
  <Box
    width={{ xs: '270px', lg: '336px' }}
    height="fit-content"
    maxHeight={660}
    sx={{
      overflowY: 'scroll',
      p: '20px',
      '&::-webkit-scrollbar': { display: 'none' },
      boxShadow:
        '0px 12px 24px rgba(0, 0, 0, 0.05), 0px 0px 2px rgba(0,0,0,0.1)',
      borderRadius: '8px',
      display: { xs: 'none', md: 'block' },
    }}
  >
    <Typography fontSize="20px" fontWeight={600} mb="40px">
      Other Articles
    </Typography>

    <Box display="flex" flexDirection="column" gap="40px">
      {articles.map((article) => (
        <ArticleCard key={article.id} article={article} />
      ))}
    </Box>
  </Box>
);

export default OtherArticlesSidebar;
