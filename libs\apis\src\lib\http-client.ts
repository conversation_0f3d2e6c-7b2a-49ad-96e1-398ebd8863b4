import axios, {
  Axi<PERSON><PERSON>rror,
  InternalAxiosRequestConfig,
  AxiosRequestConfig,
} from 'axios';
import { auth } from '../lib/firebase/firebase-client.js';
import { signInWithCustomToken } from 'firebase/auth';
import { handleSessionExpiration } from './auth/auth-utils.js';

const getApiBaseUrl = () => {
  const isBrowser =
    typeof process === 'undefined' ||
    !process.versions ||
    !process.versions.node;

  if (typeof process !== 'undefined' && process.env) {
    if (process.env.NEXT_PUBLIC_SERVER_URL) {
      const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL.endsWith('/')
        ? process.env.NEXT_PUBLIC_SERVER_URL.slice(0, -1)
        : process.env.NEXT_PUBLIC_SERVER_URL;
      return `${baseUrl}/api/v1`;
    } else if (isBrowser) {
      return '/api/v1';
    } else {
      return '/api/v1';
    }
  }

  return '/api/v1';
};

const API_BASE_URL = getApiBaseUrl();

let sessionRefreshAttempts = 0;
const MAX_SESSION_REFRESH_ATTEMPTS = 3;

let useTokenFallbackMode = false;

const getCookie = (name: string): string | undefined => {
  if (typeof window === 'undefined' || typeof document === 'undefined')
    return undefined;

  const match = document.cookie.match(
    new RegExp('(^|;\\s*)(' + name + ')=([^;]*)')
  );
  return match ? match.pop() : undefined;
};

const createAxiosInstance = ({ baseURL }: { baseURL: string }) => {
  const customAxiosInstance = axios.create({
    baseURL,
    headers: {
      'Content-Type': 'application/json',
    },
    withCredentials: true,
  });

  customAxiosInstance.interceptors.request.use(
    async (config: InternalAxiosRequestConfig) => {
      if (!auth) {
        console.log('Auth not initialized in request interceptor');
        return config;
      }

      const currentUser = auth.currentUser;

      if (currentUser) {
        try {
          console.log('Getting ID token for request to:', config.url);
          const idToken = await currentUser.getIdToken();
          console.log('Token obtained successfully');

          config.headers.Authorization = `Bearer ${idToken}`;

          if (useTokenFallbackMode) {
            config.withCredentials = false;
            console.log('Using token fallback mode');
          } else {
            config.withCredentials = true;

            if (
              typeof window !== 'undefined' &&
              window.location.hostname === 'localhost'
            ) {
              const sessionCookie = getCookie('__session');
              if (sessionCookie && !config.headers.Cookie) {
                config.headers.Cookie = `__session=${sessionCookie}`;
                console.log('Added session cookie to request');
              }
            }
          }
        } catch (error) {
          console.error('Error getting ID token:', error);
        }
      } else {
        console.log('No current user found in request interceptor');
      }

      return config;
    },
    (error) => Promise.reject(error)
  );

  customAxiosInstance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const config = error.config as any;
      if (config?._isRetry) {
        return Promise.reject(error);
      }

      // Handle both 440 (session expired) and 403 (forbidden/token issues)
      if (error.response?.status === 440 || error.response?.status === 403) {
        if (sessionRefreshAttempts >= MAX_SESSION_REFRESH_ATTEMPTS) {
          useTokenFallbackMode = true;
          sessionRefreshAttempts = 0;

          if (typeof window !== 'undefined' && auth) {
            await auth.signOut();
            window.location.href = '/signin';
          }

          return Promise.reject({
            message: 'Your session has expired. Please sign in again.',
            code: 'auth/session-expired',
          });
        }

        try {
          sessionRefreshAttempts++;

          if (!auth) {
            throw new Error('Firebase auth is not initialized');
          }

          const currentUser = auth.currentUser;
          if (!currentUser) {
            throw new Error('No user signed in');
          }

          // Use centralized session handling with force refresh for token issues
          const success = await handleSessionExpiration();
          if (!success) {
            throw new Error('Session refresh failed');
          }

          sessionRefreshAttempts = 0;

          if (config) {
            config._isRetry = true;
            return customAxiosInstance(config);
          }

          return Promise.resolve({} as any);
        } catch (refreshError) {
          useTokenFallbackMode = true;

          return Promise.reject({
            message: 'Your session has expired. Please sign in again.',
            code: 'auth/session-expired',
          });
        }
      }

      if (error.response?.status === 401) {
        if (!auth) {
          if (typeof window !== 'undefined') {
            window.location.href = '/signin';
          }
          return Promise.reject({
            message: 'Firebase auth is not initialized',
            code: 'auth/not-initialized',
          });
        }

        // const currentUser = auth.currentUser;
        // if (!currentUser) {
        //   if (typeof window !== 'undefined') {
        //     window.location.href = '/signin';
        //   }

        //   return Promise.reject({
        //     message: 'You must be signed in to access this resource.',
        //     code: 'auth/not-signed-in',
        //   });
        // }

        try {
          const response = await axios.post(
            `${API_BASE_URL}/auth/verify-session`,
            {},
            { withCredentials: true }
          );

          const customToken = response.data?.data?.customToken;

          if (customToken && auth) {
            await signInWithCustomToken(auth, customToken);

            if (config) {
              config._isRetry = true;
              return customAxiosInstance(config);
            }
          }
        } catch (verifyError) {
          useTokenFallbackMode = true;

          return Promise.reject({
            message: 'Authentication failed. Please sign in again.',
            code: 'auth/authentication-failed',
          });
        }
      }

      return Promise.reject(error);
    }
  );

  return customAxiosInstance;
};

export const axiosInstance = createAxiosInstance({ baseURL: API_BASE_URL });

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  const [url, config] = Array.isArray(args) ? args : [args, {}];
  const { data } = await axiosInstance.get(url, { ...config });
  return data?.data;
};

/**
 * Reset the token fallback mode and session refresh failure count
 * Call this after a successful login to try session-based auth again
 */
export const resetAuthMode = () => {
  useTokenFallbackMode = false;
  sessionRefreshAttempts = 0;
};
