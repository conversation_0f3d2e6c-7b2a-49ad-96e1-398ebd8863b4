const ArticleIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M29.9989 13.8571V27.4838C29.9989 29.9713 27.9624 32 25.4681 32H10.5308C8.03651 32 6 29.9713 6 27.4838V8.51617C6 6.02875 8.03645 4 10.5308 4H20.111C21.3581 4 22.4312 4.44328 23.3147 5.32275L28.673 10.6637C29.5564 11.5432 30 12.6128 30 13.857L29.9989 13.8571ZM11.0353 13.7716H16.3424C17.0673 13.7716 17.6586 13.1833 17.6586 12.4595C17.6586 11.7346 17.0673 11.1475 16.3424 11.1475H11.0353C10.3104 11.1475 9.71905 11.7346 9.71905 12.4595C9.71905 13.1845 10.3104 13.7716 11.0353 13.7716ZM11.0353 19.8601H24.9626C25.6911 19.8601 26.2789 19.273 26.2789 18.548C26.2789 17.8231 25.6899 17.236 24.9626 17.236L11.0366 17.2336C10.3117 17.2336 9.72027 17.8219 9.72027 18.5456C9.72027 19.2706 10.3117 19.8577 11.0366 19.8577L11.0353 19.8601ZM11.0353 25.9486H24.9626C25.6911 25.9486 26.2789 25.3615 26.2789 24.6365C26.2789 23.9116 25.6899 23.3245 24.9626 23.3245L11.0366 23.3221C10.3117 23.3221 9.72027 23.9092 9.72027 24.6341C9.72027 25.3591 10.3117 25.9462 11.0366 25.9462L11.0353 25.9486Z"
        fill="url(#paint0_linear_1757_106361)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1757_106361"
          x1="18"
          y1="4"
          x2="18"
          y2="32"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F92243" />
          <stop offset="1" stopColor="#A24295" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default ArticleIcon;
