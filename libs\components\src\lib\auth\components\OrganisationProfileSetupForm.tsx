import { useCallback, useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import {
  Box,
  Button,
  TextField,
  Typography,
  Grid,
  Autocomplete,
  CircularProgress,
  Checkbox,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import IntroductoryStatement from '../../onboarding/components/CardiacSpecialist/IntroductoryStatement';
import {
  Categories,
  Country,
  useAuth,
  useGetCategories,
  useGetCountries,
} from '@minicardiac-client/apis';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { INTRODUCTORY_STATEMENT_LIMITS } from '@minicardiac-client/constants';
import MapLinkDialog from './MapLinkDialog';
import MainProfessionSelector from '../../form/MainProfessionSelector';
import { ActionButtonsTemplate } from '../../onboarding/templates';
import { removeAWSS3DomainFromURL } from '@minicardiac-client/utilities';
import { ProfilePicture } from '../../common/ProfilePicture';
import { useProfilePicture } from '../../common/ProfilePicture';
import { ExtendedTheme } from '../../auth/types/auth.types';

export interface OrganisationProfileFormData {
  category: string;
  location: string;
  parentOrganisation: string | null;
  introductoryStatement: string;
  mainProfession: 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH';
  mapLink?: string;
  profileImageUrl?: string;
  profileImageUrlThumbnail?: string;
  poc?: string;
  npoc?: string;
  subsidiaryCheckbox?: boolean;
  orgWebsite?: string;
}

interface OrganisationProfileSetupFormProps {
  isBasicPlan?: boolean;
  onChange?: (formData: OrganisationProfileFormData) => void;
  onSave?: (formData: OrganisationProfileFormData) => void;
  onSkip?: () => void;
  isSubmitting?: boolean;
  subscriptionType?: string;
}

const validationSchema = yup.object().shape({
  category: yup.string().required('Category is required'),
  location: yup.string().required('Location is required'),
  parentOrganisation: yup.string().notRequired(),
  introductoryStatement: yup
    .string()
    .required('Introductory statement is required')
    .max(
      INTRODUCTORY_STATEMENT_LIMITS.MAX,
      `Maximum ${INTRODUCTORY_STATEMENT_LIMITS.MAX} characters only`
    ),
  mainProfession: yup
    .mixed<'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH'>()
    .oneOf(['CARDIAC_SURGEON', 'CARDIOLOGIST', 'BOTH'], 'Invalid profession')
    .required('Main profession is required'),
  mapLink: yup.string().url('Must be a valid URL').notRequired(),
  poc: yup.string().notRequired(),
  npoc: yup.string().notRequired(),
  subsidiaryCheckbox: yup.boolean().notRequired(),
  orgWebsite: yup.string().url('Must be a valid URL').notRequired(),
  profileImageUrl: yup.string().notRequired(),
  profileImageUrlThumbnail: yup.string().notRequired(),
});

const sizeOptions = [
  'Less than 20',
  'Between 20-50',
  'Between 50-100',
  'Between 100-500',
  'Between 500-1000',
  'Between 1,000-5,000',
  'More than 5,000',
];

export default function OrganisationProfileSetupForm({
  isBasicPlan = false,
  onChange,
  onSave,
  onSkip,
  isSubmitting = false,
  subscriptionType,
}: OrganisationProfileSetupFormProps) {
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm({
    resolver: yupResolver(validationSchema),
    mode: 'onChange',
    defaultValues: {
      category: '',
      location: '',
      parentOrganisation: '',
      introductoryStatement: '',
      mainProfession: 'CARDIAC_SURGEON',
      mapLink: '',
      poc: '',
      npoc: '',
      subsidiaryCheckbox: false,
      orgWebsite: '',
      profileImageUrl: '',
      profileImageUrlThumbnail: '',
    },
  });

  // Watch form values
  const formValues = watch();

  const { data: categories, isLoading: isLoadingCategories } =
    useGetCategories('ORGANISATION');
  const { data: countries, isLoading: isLoadingCountries } = useGetCountries();
  const { authState } = useAuth();

  const theme = useTheme() as ExtendedTheme;

  const isXsScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const onSubmit = (data: any) => {
    const normalisedData: OrganisationProfileFormData = {
      ...data,
      parentOrganisation: data.parentOrganisation ?? null,
      mapLink: data.mapLink ?? undefined,
    };

    if (onSave) {
      onSave(normalisedData);
    }
  };

  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    }
  };

  const handleFieldChange = useCallback(
    (name: keyof OrganisationProfileFormData, value: any) => {
      // Normalize undefined to null for nullable fields
      const normalizedValue =
        name === 'parentOrganisation' && value === undefined ? null : value;

      // Only setValue for fields registered in react-hook-form
      const formFields: Array<keyof OrganisationProfileFormData> = [
        'category',
        'location',
        'parentOrganisation',
        'introductoryStatement',
        'mainProfession',
        'mapLink',
        'poc',
        'npoc',
        'subsidiaryCheckbox',
        'orgWebsite',
        'profileImageUrl',
        'profileImageUrlThumbnail',
      ];
      if (formFields.includes(name)) {
        setValue(name as any, normalizedValue, { shouldValidate: true });
      }

      if (onChange) {
        const updatedValues = {
          ...formValues,
          [name]: normalizedValue,
        };

        onChange(updatedValues as OrganisationProfileFormData);
      }
    },
    [setValue, onChange, formValues]
  );

  const updateFormField = (field: any, value: any) => {
    handleFieldChange(field, value);
  };

  const [selectedCategory, setSelectedCategory] = useState<Categories | null>(
    null
  );
  const [selectedLocation, setSelectedLocation] = useState<Country | null>(
    null
  );
  const [mainWork, setMainWork] = useState<
    'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH' | null
  >(null);
  const [isMapDialogOpen, setIsMapDialogOpen] = useState(false);
  const [mapLink, setMapLink] = useState('');

  const { previewUrl, isUploading, error, handleImageChange, handleRemove } =
    useProfilePicture({
      initialImageUrl: formValues?.profileImageUrl || null,
      onImageUpload: (imageUrl, thumbnailUrl) => {
        updateFormField('profileImageUrl', imageUrl);
        const cleanedImageUrl = removeAWSS3DomainFromURL(thumbnailUrl);
        updateFormField('profileImageUrlThumbnail', cleanedImageUrl);
      },
    });

  return (
    <>
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          px: { xs: 0, sm: 3, md: 5 },
          pb: { xs: '100px' },
          borderRadius: 1,
          boxShadow: { xs: 'none', sm: '0 12px 24px #A3A3A31F' },
          mb: 4,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: { xs: 'center', md: 'start' },
            justifyContent: 'space-between',
            width: '100%',
            gap: { xs: '40px', md: '72px' },
          }}
        >
          {/* Profile Picture Upload Section */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: { xs: 'center', md: 'flex-start' },
            }}
          >
            <ProfilePicture
              theme={theme}
              previewUrl={previewUrl}
              displayName={authState.user?.displayName || ''}
              onImageChange={handleImageChange}
              onRemove={handleRemove}
              isUploading={isUploading}
              error={error}
              disabled={isSubmitting}
            />
          </Box>

          {/* Form Fields Section */}
          <Box flexGrow={1}>
            <Grid
              container
              columns={{ xs: 4, sm: 2, md: 2 }}
              rowSpacing={'40px'}
              columnSpacing={{ xs: 3, md: 5 }}
              position={'relative'}
            >
              {/* Category Field */}
              <Grid size={{ xs: 12, md: 1 }} sx={{ width: '100%', mt: 3 }}>
                <Autocomplete
                  sx={{
                    width: '100%',
                    maxWidth: { md: '360px' },
                  }}
                  options={categories || []}
                  getOptionLabel={(option) => option.name}
                  loading={isLoadingCategories}
                  value={selectedCategory}
                  onChange={(_, newValue) => {
                    setSelectedCategory(newValue);
                    updateFormField('category', newValue?.id);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Category"
                      variant="outlined"
                      size="small"
                      placeholder="Choose from Options"
                      error={!!errors.category}
                      helperText={errors.category?.message}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {isLoadingCategories ? (
                              <CircularProgress color="inherit" size={20} />
                            ) : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                    />
                  )}
                />
              </Grid>

              {/* Location Field */}
              <Grid
                size={{ xs: 12, md: 1 }}
                sx={{
                  width: '100%',
                  maxWidth: { md: '360px' },
                }}
              >
                <MainProfessionSelector
                  control={control}
                  errors={errors}
                  mainWork={mainWork}
                  setMainWork={setMainWork}
                  isBasicPlan={isBasicPlan}
                />
              </Grid>

              {/* Location */}
              <Grid
                size={{ xs: 12, md: 1 }}
                gap={'8px'}
                alignItems="start"
                display="flex"
                flexDirection="column"
                justifyContent="start"
                alignContent="center"
                sx={{
                  width: '100%',
                  mb: '0',
                }}
              >
                <Controller
                  name="location"
                  control={control}
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      sx={{ width: '100%', maxWidth: { md: '360px' } }}
                      options={countries || []}
                      getOptionLabel={(option) =>
                        `${option.name}, ${option.continent.name}`
                      }
                      loading={isLoadingCountries}
                      onChange={(_, newValue) => {
                        if (newValue) {
                          setSelectedLocation(newValue);
                          updateFormField(
                            'location',
                            `${newValue?.name}, ${newValue?.continent.name}`
                          );
                        } else {
                          setSelectedLocation(null);
                          updateFormField('location', '');
                        }
                      }}
                      value={selectedLocation}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Location"
                          variant="outlined"
                          size="small"
                          placeholder="Name your City/Country"
                          error={!!errors.location}
                          helperText={errors.location?.message}
                          InputLabelProps={{ shrink: true }}
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                {isLoadingCountries ? (
                                  <CircularProgress color="inherit" size={20} />
                                ) : null}
                                {params.InputProps.endAdornment}
                              </>
                            ),
                          }}
                        />
                      )}
                    />
                  )}
                />
                {mapLink && (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      ml: 1,
                      width: '100%',
                    }}
                  >
                    <Typography
                      variant="body2"
                      color="textSecondary"
                      component="a"
                      href={mapLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{
                        textDecoration: 'none',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: 200,
                        fontWeight: 400,
                      }}
                    >
                      {mapLink}
                    </Typography>

                    <Button
                      variant="text"
                      onClick={() => setIsMapDialogOpen(true)}
                      sx={{
                        ml: 2,
                        minWidth: 'auto',
                        padding: 0,
                        fontSize: '0.75rem',
                        fontWeight: 500,
                        textTransform: 'none',
                        color: 'secondary.main',
                      }}
                    >
                      Edit
                    </Button>
                  </Box>
                )}

                {!mapLink && !subscriptionType && (
                  <Typography
                    variant="body2"
                    color="secondary"
                    sx={{
                      ml: 1,
                      cursor: 'pointer',
                      textDecoration: 'none',
                      width: 'fit-content',
                      fontWeight: 600,
                      fontSize: '12px',
                    }}
                    onClick={() => setIsMapDialogOpen(true)}
                  >
                    Add your location's map link? This is optional.
                  </Typography>
                )}

                {/* {errors?.location && (
                  <Typography color="error" variant="caption" sx={{ mt: 1 }}>
                    {errors?.location.message}
                  </Typography>
                )} */}
              </Grid>

              {/* Map Link */}
              {subscriptionType && (
                <Grid size={{ xs: 12, md: 1 }} sx={{ width: '100%' }}>
                  <Controller
                    name="mapLink"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        sx={{ width: '100%', maxWidth: { md: '360px' } }}
                        label="Map Link (optional)"
                        variant="outlined"
                        placeholder="Drop your exact location here"
                        fullWidth
                        size="small"
                        error={!!errors.parentOrganisation}
                        helperText={errors.parentOrganisation?.message}
                        slotProps={{
                          inputLabel: {
                            shrink: true,
                          },
                        }}
                      />
                    )}
                  />
                </Grid>
              )}

              {/* Parent Organization */}
              <Grid size={{ xs: 12, md: 1 }} sx={{ width: '100%' }}>
                <Controller
                  name="parentOrganisation"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      sx={{ width: '100%', maxWidth: { md: '360px' } }}
                      label="Parent Organisation (optional)"
                      variant="outlined"
                      placeholder="Name of Organisation"
                      fullWidth
                      size="small"
                      error={!!errors.parentOrganisation}
                      helperText={errors.parentOrganisation?.message}
                      slotProps={{
                        inputLabel: {
                          shrink: true,
                        },
                      }}
                    />
                  )}
                />
              </Grid>

              {/* Organization Size*/}
              {subscriptionType && (
                <Grid size={{ xs: 12, md: 1 }} sx={{ width: '100%' }}>
                  <Autocomplete
                    sx={{
                      width: '100%',
                      maxWidth: { md: '360px' },
                    }}
                    options={sizeOptions}
                    getOptionLabel={(option) => option}
                    loading={isLoadingCategories}
                    onChange={(_, newValue) => {
                      updateFormField('organization-size', newValue);
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Organisation Size (employees)"
                        variant="outlined"
                        size="small"
                        placeholder="200"
                        error={!!errors.category}
                        helperText={errors.category?.message}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {isLoadingCategories ? (
                                <CircularProgress color="inherit" size={20} />
                              ) : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>
              )}

              {/* Point of contact */}
              {subscriptionType && (
                <Grid size={{ xs: 12, md: 1 }} sx={{ width: '100%' }}>
                  <Controller
                    name="poc"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        sx={{ width: '100%', maxWidth: { md: '360px' } }}
                        label="Point of contact"
                        variant="outlined"
                        placeholder="Phone number of point of contact "
                        fullWidth
                        size="small"
                        error={!!errors.parentOrganisation}
                        helperText={errors.parentOrganisation?.message}
                        slotProps={{
                          inputLabel: {
                            shrink: true,
                          },
                        }}
                      />
                    )}
                  />
                </Grid>
              )}

              {/* Name of point of contact */}
              {subscriptionType && (
                <Grid size={{ xs: 12, md: 1 }} sx={{ width: '100%' }}>
                  <Controller
                    name="npoc"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        sx={{ width: '100%', maxWidth: { md: '360px' } }}
                        label="Name of point of contact"
                        variant="outlined"
                        placeholder="Owner of the provided phone number"
                        fullWidth
                        size="small"
                        error={!!errors.parentOrganisation}
                        helperText={errors.parentOrganisation?.message}
                        slotProps={{
                          inputLabel: {
                            shrink: true,
                          },
                        }}
                      />
                    )}
                  />
                </Grid>
              )}

              {/* Organisation website */}
              {subscriptionType && (
                <Grid size={{ xs: 12, md: 1 }} sx={{ width: '100%' }}>
                  <Controller
                    name="orgWebsite"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        sx={{ width: '100%', maxWidth: { md: '360px' } }}
                        label="Organisation website"
                        variant="outlined"
                        placeholder="Phone number of point of contact "
                        fullWidth
                        size="small"
                        error={!!errors.parentOrganisation}
                        helperText={errors.parentOrganisation?.message}
                        slotProps={{
                          inputLabel: {
                            shrink: true,
                          },
                        }}
                      />
                    )}
                  />
                </Grid>
              )}

              {/* Checkbox */}
              {subscriptionType && (
                <Grid size={{ xs: 12 }} sx={{ width: '100%' }}>
                  <Controller
                    name="subsidiaryCheckbox"
                    control={control}
                    render={({ field }) => (
                      <Box display="flex" alignItems="center" gap={1}>
                        <Checkbox
                          {...field}
                          checked={Boolean(field.value)}
                          onChange={(e) => field.onChange(e.target.checked)}
                          sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '4px',
                            p: 0,
                            border: '1px solid #A3A3A3',
                            // backgroundColor: 'transparent',
                            fill: 'transparent',
                            '&:hover': {
                              backgroundColor: 'transparent',
                            },
                            '&.Mui-checked': {
                              color: 'white',
                              backgroundColor: 'white',
                              '& svg': {
                                fill: '#A24295',
                              },
                            },
                          }}
                        />
                        <Typography
                          sx={{
                            fontWeight: 400,
                            fontSize: '16px',
                            color: '#1E1E1E',
                          }}
                        >
                          I would like to add Subsidiary Organisation accounts
                          under this Prestige plan
                        </Typography>
                      </Box>
                    )}
                  />
                </Grid>
              )}

              {/* Introductory Statement */}
              <Grid size={{ xs: 12 }} sx={{ width: '100%' }}>
                <IntroductoryStatement
                  initialValue={formValues.introductoryStatement}
                  onChange={(text) =>
                    updateFormField('introductoryStatement', text)
                  }
                />
                {errors.introductoryStatement && (
                  <Typography color="error" variant="caption" sx={{ mt: 1 }}>
                    {errors.introductoryStatement.message}
                  </Typography>
                )}
              </Grid>

              {/* Main Profession Toggle */}
            </Grid>
          </Box>
        </Box>
      </Box>

      {/* Action Buttons using shared template */}
      <ActionButtonsTemplate
        onSave={handleSubmit(onSubmit)}
        onSkip={handleSkip}
        isSubmitting={isSubmitting}
        isValid={isValid}
        saveButtonText="Save and Continue"
        skipButtonText="Do this later"
        variant="organization"
        isXsScreen={isXsScreen}
      />

      <MapLinkDialog
        open={isMapDialogOpen}
        onClose={() => setIsMapDialogOpen(false)}
        onSave={(link) => {
          setMapLink(link);
          updateFormField('mapLink', link);
        }}
        defaultValue={mapLink}
      />
    </>
  );
}
