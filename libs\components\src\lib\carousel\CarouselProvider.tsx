import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from 'react';

// Types
interface CarouselContextType {
  currentIndex: number;
  setCurrentIndex: React.Dispatch<React.SetStateAction<number>>;
  goToNext: () => void;
  goToPrev: () => void;
}

// Create context with default values
const CarouselContext = createContext<CarouselContextType | undefined>(
  undefined
);

// Custom hook to use the context
export const useCarousel = () => {
  const context = useContext(CarouselContext);
  if (!context) {
    throw new Error('useCarousel must be used within a CarouselProvider');
  }
  return context;
};

// Provider component
interface CarouselProviderProps {
  children: ReactNode;
}

export const CarouselProvider: React.FC<CarouselProviderProps> = ({
  children,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const goToNext = useCallback(() => {
    setCurrentIndex((prevIndex) => prevIndex + 1); // Go to the next slide
  }, []);

  const goToPrev = useCallback(() => {
    setCurrentIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : 0)); // Go to the previous slide
  }, []);

  const contextValue: CarouselContextType = {
    currentIndex,
    setCurrentIndex,
    goToNext,
    goToPrev,
  };

  return (
    <CarouselContext.Provider value={contextValue}>
      {children}
    </CarouselContext.Provider>
  );
};
