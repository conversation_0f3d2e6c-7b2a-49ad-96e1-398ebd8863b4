import React from 'react';
import { Box, Button, Typography } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';

const ButtonSelect = ({
  isActive,
  onClick,
}: {
  isActive: boolean;
  onClick: () => void;
}) => {
  return (
    <Button
      onClick={onClick}
      sx={{
        border: 'solid',
        borderWidth: isActive ? '3px' : '0.5px',
        borderRadius: '8px',
        p: 2,
        overflow: 'hidden',
        borderColor: (theme) => theme.palette.secondary.main,
        '&:hover': {
          textDecorationLine: 'none',
        },
      }}
    >
      <Typography
        sx={(theme) => ({
          ...(
            theme.typography as unknown as {
              subtitle3: React.CSSProperties;
            }
          ).subtitle3,
          lineHeight: '24px',
          color: (theme) =>
            isActive
              ? theme.palette.secondary.main
              : theme.palette.grey[900],
          fontWeight: isActive ? 700 : 300,
        })}
      >
        Select
      </Typography>

      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          p: 2.5,
          width: '50px',
          position: 'relative',
          height: '100%',
        }}
      >
        {/* Background Shape */}
        <Box
          component="img"
          src="/assets/shape.png"
          alt="Background shape"
          sx={{
            position: 'absolute',
            width: '68px',
            height: '68px',
            opacity: 0.7,
            zIndex: 0,
            pointerEvents: 'none',
          }}
        />

        <CheckIcon
          sx={{
            width: 22,
            height: 22,
            position: 'absolute',
            top: '10px',
            left: '34px',
            color: (theme) =>
              isActive
                ? theme.palette.secondary.main
                : theme.palette.grey[600],
          }}
        />
      </Box>
    </Button>
  );
};

export default ButtonSelect;
